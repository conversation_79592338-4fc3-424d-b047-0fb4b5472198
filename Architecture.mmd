graph LR
  %% Client and UI
  subgraph Client
    U[User Browser]
  end

  subgraph GUI["Gradio GUI Service"]
    G["gui/src/gui.py<br/>Tabs: Music Gen, Remix, Extend,<br/>Extract Vocals, Sound, Auto-Prompt, Artist"]
    G -->|HTTP REST| AAPI
    G -->|WebSocket| AAPI
  end

  U --> G

  %% API Layer
  subgraph API["FastAPI Service (api/src/main.py)"]
    AAPI["Main Router<br/>/auto-prompt<br/>/artist-generation<br/>/album-cover-art<br/>/spoken-word-script<br/>/generate-voice<br/>/generate-voice-over<br/>/translate-wav-audio<br/>/translate-text<br/>ws:/generate-music<br/>ws:/remix-music<br/>ws:/extend-music<br/>ws:/extract-vocals<br/>ws:/sound-generation"]

    %% Internal services
    subgraph SVC["Internal Services"]
      AP["AutoPrompter<br/>(api/.../auto_prompting)"]
      AR["ArtistGenerator<br/>(api/.../artist_generation)"]
      AI["AlbumImageGenerator<br/>(api/.../album_image_gen)"]
      SW["SpokenWordGenerator<br/>(api/.../spoken_word)"]
      TR["TranslationService<br/>(api/.../translation)"]
      MG["MusicGPTClient<br/>(api/.../music_generators)"]
    end

    %% Wiring of endpoints to services
    AAPI --> AP
    AAPI --> AR
    AAPI --> AI
    AAPI --> SW
    AAPI --> TR
    AAPI --> MG
  end

  %% External integrations
  subgraph EXT["External AI Services"]
    GEM["Google GenAI<br/>(Gemini/Imagen)"]
    OAI["Azure OpenAI"]
    EL["ElevenLabs"]
    MAPI["MusicGPT API"]
  end

  %% Service-to-external edges
  AP -->|JSON Gen| GEM
  AR -->|JSON Gen| GEM
  AI -->|Image Gen| GEM
  SW -->|Script JSON| GEM
  SW -->|Voice Design<br/>TTS<br/>Dubbing| EL
  TR -->|Structured JSON| OAI
  MG <-->|MusicAI<br/>Remix<br/>Extend<br/>Extraction<br/>Sound| MAPI

  %% WS status updates back to client via API
  MG -->|WS status<br/>final URLs| AAPI
  AAPI -->|WS messages| G

  %% Infra/Deployment
  subgraph Deploy["Deployment"]
    DC["Docker Compose (local)<br/>api:8000, gui:7860"]
    CR["Terraform -> Cloud Run"]
    ARg["Terraform -> Artifact Registry"]
  end

  DC --> API
  DC --> GUI
  ARg --> CR
  CR --> API

  %% Notes
  classDef note fill:#fff7cc,stroke:#b59f3b,color:#333
  N1["Environment/Secrets:<br/>MUSIC_GPT_API_KEY<br/>OPENAI_API_KEY<br/>GEMINI_API_KEY<br/>ELEVENLABS_API_KEY<br/>MUSIC_GENERATION_API_KEY"]:::note
  AAPI -. uses .-> N1