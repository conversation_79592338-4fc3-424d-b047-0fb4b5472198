graph LR
  %% Client and UI
  subgraph Client
    U[User Browser]
  end

  subgraph GUI[Gradio GUI Service]
    G[gui/src/gui.py]\n- Tabs: Music Gen, Remix, Extend, Extract Vocals, Sound, Auto‑Prompt, Artist
    G -->|HTTP (REST)| AAPI
    G -->|WebSocket| AAPI
  end

  U --> G

  %% API Layer
  subgraph API[FastAPI Service (api/src/main.py)]
    AAPI[Main Router]\n/auto-prompt\n/artist-generation\n/album-cover-art\n/spoken-word-script\n/generate-voice\n/generate-voice-over\n/translate-wav-audio\n/translate-text\nws:/generate-music\nws:/remix-music\nws:/extend-music\nws:/extract-vocals\nws:/sound-generation

    %% Internal services
    subgraph SVC[Internal Services]
      AP[AutoPrompter]\n(api/.../auto_prompting)
      AR[ArtistGenerator]\n(api/.../artist_generation)
      AI[AlbumImageGenerator]\n(api/.../album_image_gen)
      SW[SpokenWordGenerator]\n(api/.../spoken_word)
      TR[TranslationService]\n(api/.../translation)
      MG[MusicGPTClient]\n(api/.../music_generators)
    end

    %% Wiring of endpoints to services
    AAPI -- /auto-prompt --> AP
    AAPI -- /artist-generation --> AR
    AAPI -- /album-cover-art --> AI
    AAPI -- /spoken-word-script --> SW
    AAPI -- /generate-voice --> SW
    AAPI -- /generate-voice-over --> SW
    AAPI -- /translate-wav-audio --> SW
    AAPI -- /translate-text --> TR

    AAPI -- ws:/generate-music --> MG
    AAPI -- ws:/remix-music --> MG
    AAPI -- ws:/extend-music --> MG
    AAPI -- ws:/extract-vocals --> MG
    AAPI -- ws:/sound-generation --> MG
  end

  %% External integrations
  subgraph EXT[External AI Services]
    GEM[Google GenAI (Gemini/Imagen)]
    OAI[Azure OpenAI]
    EL[ElevenLabs]
    MAPI[MusicGPT API]
  end

  %% Service-to-external edges
  AP -->|JSON Gen| GEM
  AR -->|JSON Gen| GEM
  AI -->|Image Gen| GEM
  SW -->|Script (JSON)| GEM
  SW -->|Voice Design/ TTS/ Dubbing| EL
  TR -->|Structured JSON| OAI
  MG <--> |MusicAI / Remix / Extend / Extraction / Sound| MAPI

  %% WS status updates back to client via API
  MG -->|WS status + final URLs| AAPI -->|WS messages| G

  %% Infra/Deployment
  subgraph Deploy[Deployment]
    DC[Docker Compose (local)\napi:8000, gui:7860]
    CR[Terraform -> Cloud Run]
    ARg[Terraform -> Artifact Registry]
  end

  DC --> API
  DC --> GUI
  ARg --> CR
  CR --> API

  %% Notes
  classDef note fill:#fff7cc,stroke:#b59f3b,color:#333;
  N1:::note[Env/Secrets:\nMUSIC_GPT_API_KEY, OPENAI_API_KEY, GEMINI_API_KEY, ELEVENLABS_API_KEY, MUSIC_GENERATION_API_KEY]
  AAPI -. uses .-> N1
