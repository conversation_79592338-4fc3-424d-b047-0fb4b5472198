# music-generation
Music Generation from prompts, samples, and lyrics

## API Documentation

This API provides comprehensive music generation, audio processing, and AI-powered creative services. All endpoints require API key authentication.

### Authentication

All endpoints require an API key to be provided. The API key validation can be controlled via environment variables:
- `MUSIC_GENERATION_API_KEY`: The expected API key (default: "api-key")
- `VALIDATE_API_KEY`: Set to "false" to disable API key validation (default: "true")

Authentication errors return HTTP 403 with the message "Invalid API Key".

---

## REST Endpoints

### 1. Auto Prompt Generation

**POST** `/auto-prompt`

Generates enhanced music prompts from basic user input using AI.

**Request Body:**
```json
{
  "prompt": "string (required) - Basic prompt or theme",
  "model_creativity": 0.2,  // float (optional) - Creativity level 0.0-1.0
  "agent_description": "string (optional) - Custom agent description"
}
```

**Response:**
```json
{
  "main_prompt": "Enhanced prompt for music generation",
  "music_genre": "Specific music genre",
  "lyrics": "Generated song lyrics"
}
```

**Error Responses:**
- `500`: "Error Generating Prompt: {error_message}"

---

### 2. Artist Generation

**POST** `/artist-generation`

Generates comprehensive AI artist profiles including biography, appearance, and creative direction.

**Request Body:**
```json
{
  "prompt": "string (required) - Artist concept or theme",
  "model_creativity": 0.2,  // float (optional) - Creativity level 0.0-1.0
  "agent_description": "string (optional) - Custom agent description"
}
```

**Response:**
```json
{
  "artist_alias": "Stage name",
  "alias": "Short nickname",
  "given_name": "Legal/birth name",
  "type": "Solo artist|Duo|Band",
  "gender": "Male|Female|Non-binary|Other",
  "age": 25,
  "origin_city": "City name",
  "personality": "Detailed personality description",
  "vibe": "Artistic vibe description",
  "catchphrase": ["5 signature phrases"],
  "appearance": "Physical appearance description",
  "body_build": "Slim|Athletic|Muscular|Average|Curvy|Stocky",
  "skin_tone": "Complexion description",
  "hair": "Hair style and color",
  "style": "Fashion style",
  "face": "Facial features",
  "accessories": "Signature accessories",
  "artist_bio_short": "Short biography (max 250 chars)",
  "hobbies": ["5 hobbies"],
  "animals": "Pet information (optional)",
  "random_activities": ["10-15 daily activities"],
  "relationship_status": "Single|Dating|Engaged|Married|Divorced|It's complicated",
  "holiday_preference": ["5 preferred destinations"],
  "sports": "Sports activities (optional)",
  "food": "Food preferences",
  "possible_backgrounds": ["5 background descriptions"],
  "stage_design": ["3 stage design descriptions"],
  "action_shot_scene": ["5 action scene descriptions"],
  "press_kit_image": ["3 press kit image descriptions"],
  "action_stage": ["3 on-stage action descriptions"],
  "artwork_description_style": ["2 design style descriptions"]
}
```

**Error Responses:**
- `500`: "Error Generating artist: {error_message}"

---

### 3. Album Cover Art Generation

**POST** `/album-cover-art`

Generates album cover artwork using AI image generation.

**Request Body:**
```json
{
  "prompt": "string (required) - Album cover concept",
  "model_creativity": 0.2,  // float (optional) - Creativity level 0.0-1.0
  "model": "imagen"  // string (optional) - Image generation model
}
```

**Response:**
```json
{
  "error": false,
  "image_base64": "base64-encoded-image-data"
}
```

**Error Responses:**
- `500`: "Error Generating Album Cover: {error_message}"

---

### 4. Spoken Word Script Generation

**POST** `/spoken-word-script`

Generates scripts for spoken word pieces (meditative or motivational content).

**Request Body:**
```json
{
  "prompt": "string (required) - Script theme or concept",
  "model_creativity": 0.2,  // float (optional) - Creativity level 0.0-1.0
  "agent_description": "string (optional) - Custom agent description"
}
```

**Response:**
```json
{
  "script": "Generated script with pause markers like <break time=\"2s\" />"
}
```

**Error Responses:**
- `500`: "Error Generating script: {error_message}"

---

### 5. Voice Generation

**POST** `/generate-voice`

Generates custom voice profiles for text-to-speech.

**Request Body:**
```json
{
  "voice_description": "string (required) - Description of desired voice characteristics"
}
```

**Response:**
```json
{
  "voice_id": "generated-voice-identifier",
  "voice_characteristics": "Voice profile details"
}
```

**Error Responses:**
- `500`: "Error Generating voice: {error_message}"

---

### 6. Voice Over Generation

**POST** `/generate-voice-over`

Converts text scripts to audio using specified voice profiles.

**Request Body:**
```json
{
  "script": "string (required) - Text script to convert to audio",
  "voice_id": "string (required) - ElevenLabs Voice profile identifier",
  "language_code": "en"  // string (optional) - Language code (default: "en")
}
```

**Response:**
```json
{
  "error": false,
  "audio_base64": "base64-encoded-audio-data"
}
```

**Error Responses:**
- `500`: "Error Generating voice over: {error_message}"

---

### 7. Audio Translation

**POST** `/translate-wav-audio`

Translates spoken audio from one language to another.

**Request Body:**
```json
{
  "audio_base64": "string (required) - Base64-encoded audio data",
  "target_language": "string (required) - Target language for translation"
}
```

**Response:**
```json
{
  "error": false,
  "audio_base64": "base64-encoded-translated-audio-data"
}
```

**Error Responses:**
- `500`: "Error translating: {error_message}"

---

### 8. Text Translation

**POST** `/translate-text`

Translates text from one language to another using OpenAI's GPT models.

**Request Body:**
```json
{
  "text": "string (required, 1-10000 chars) - Text to translate",
  "target_language": "string (required) - Target language name",
  "source_language": "string (optional) - Source language (defaults to English)",
  "context": "string (optional, max 500 chars) - Additional context for translation",
  "quality": "standard",  // string (optional) - "standard"|"high"|"creative"
  "model_creativity": 0.2  // float (optional, 0.0-1.0) - Creativity level
}
```

**Supported Languages:**
Spanish, French, German, Italian, Portuguese, Russian, Chinese (Simplified), Chinese (Traditional), Japanese, Korean, Arabic, Hindi, Dutch, Swedish, Norwegian, Danish, Finnish, Polish, Czech, Hungarian, Romanian, Bulgarian, Croatian, Serbian, Slovak, Slovenian, Estonian, Latvian, Lithuanian, Greek, Turkish, Hebrew, Thai, Vietnamese, Indonesian, Malay, Filipino, Swahili, English

**Quality Levels:**
- `standard`: Balanced accuracy and naturalness
- `high`: Maximum accuracy with conservative approach
- `creative`: More creative and expressive translations

**Response:**
```json
{
  "error": false,
  "translation": "Translated text",
  "source_language": "Detected/specified source language",
  "target_language": "Target language used",
  "confidence_score": 0.95  // float (optional, 0.0-1.0) - Translation confidence
}
```

**Error Responses:**
- `500`: "Translation failed: {error_message}"

---

## WebSocket Endpoints

All WebSocket endpoints follow a similar pattern:
1. Client connects to the WebSocket endpoint
2. Client sends a JSON message with parameters
3. Server processes the request and sends status updates
4. Server sends final result when processing is complete
5. Connection is closed

### WebSocket Message Format

**Status Messages:**
```json
{
  "error": false,
  "status": "status_description",
  "data": { /* status-specific data */ }
}
```

**Error Messages:**
```json
{
  "error": true,
  "data": "error_message"
}
```

**Completion Messages:**
```json
{
  "error": false,
  "status": "completed",
  "data": { /* final results */ }
}
```

---

### 1. Music Generation

**WebSocket** `/generate-music`

Generates original music from text prompts with real-time status updates.

**Client Request:**
```json
{
  "prompt": "string (required) - Music description/prompt",
  "music_style": "string (optional) - Musical style (e.g., 'Pop', 'Rock', 'Jazz')",
  "lyrics": "string (optional) - Custom lyrics for the song",
  "make_instrumental": false,  // boolean (optional) - Generate instrumental only
  "vocal_only": false  // boolean (optional) - Generate vocals only
}
```

**Status Updates:**
- `"initiating music generation"` - Initial processing started
- `"generating music"` - Music generation in progress with status details

**Final Response:**
```json
{
  "error": false,
  "status": "completed",
  "data": {
    "song_1_url": "https://url-to-generated-song-1.mp3",
    "song_1_wav_url": "https://url-to-generated-song-1.wav",
    "song_1_length": 180.5,  // duration in seconds
    "song_2_url": "https://url-to-generated-song-2.mp3",
    "song_2_wav_url": "https://url-to-generated-song-2.wav",
    "song_2_length": 175.2,
    "music_gpt_task_id": "task-identifier"
  }
}
```

---

### 2. Music Remixing

**WebSocket** `/remix-music`

Remixes existing audio with new prompts and styles.

**Client Request:**
```json
{
  "audio_base64": "string (required) - Base64-encoded audio data",
  "prompt": "string (required) - Remix description/style",
  "lyrics": "string (optional) - New lyrics for the remix"
}
```

**Status Updates:**
- `"initiating music remixing"` - Processing started
- `"generating music"` - Remix generation in progress

**Final Response:**
```json
{
  "error": false,
  "status": "completed",
  "data": {
    "song_1_url": "https://url-to-remixed-song-1.mp3",
    "song_1_wav_url": "https://url-to-remixed-song-1.wav",
    "song_1_length": 190.3,
    "song_2_url": "https://url-to-remixed-song-2.mp3",
    "song_2_wav_url": "https://url-to-remixed-song-2.wav",
    "song_2_length": 185.7,
    "music_gpt_task_id": "task-identifier"
  }
}
```

**Note:** Currently accepts base64-encoded audio in a single chunk. Not optimized for large files.

---

### 3. Music Extension

**WebSocket** `/extend-music`

Extends existing music tracks beyond their current duration.

**Client Request:**
```json
{
  "audio_base64": "string (required) - Base64-encoded audio data",
  "extend_after_seconds": 120,  // integer (required) - Extend after this timestamp
  "prompt": "string (optional) - Description for the extension",
  "lyrics": "string (optional) - Lyrics for the extended portion"
}
```

**Status Updates:**
- `"initiating music extension"` - Processing started
- `"generating music"` - Extension generation in progress

**Final Response:**
```json
{
  "error": false,
  "status": "completed",
  "data": {
    "song_1_url": "https://url-to-extended-song-1.mp3",
    "song_1_wav_url": "https://url-to-extended-song-1.wav",
    "song_1_length": 240.8,
    "song_2_url": "https://url-to-extended-song-2.mp3",
    "song_2_wav_url": "https://url-to-extended-song-2.wav",
    "song_2_length": 235.1,
    "music_gpt_task_id": "task-identifier"
  }
}
```

**Note:** Currently accepts base64-encoded audio in a single chunk. Not optimized for large files.

---

### 4. Vocal Extraction

**WebSocket** `/extract-vocals`

Extracts vocals from music tracks, separating them from instrumental components.

**Client Request:**
```json
{
  "audio_base64": "string (required) - Base64-encoded audio data",
  "preprocessing_options": "string (optional) - Audio preprocessing settings"
}
```

**Status Updates:**
- `"initiating vocal extraction"` - Processing started
- `"generating music"` - Vocal extraction in progress

**Final Response:**
```json
{
  "error": false,
  "status": "completed",
  "data": {
    "vocals_url": "https://url-to-extracted-vocals.mp3",
    "vocals_wav_url": "https://url-to-extracted-vocals.wav",
    "instrumental_url": "https://url-to-instrumental.mp3",
    "instrumental_wav_url": "https://url-to-instrumental.wav",
    "music_gpt_task_id": "task-identifier"
  }
}
```

**Note:** Currently accepts base64-encoded audio in a single chunk. Not optimized for large files.

---

### 5. Sound Generation

**WebSocket** `/sound-generation`

Generates custom sound effects and audio elements from text descriptions.

**Client Request:**
```json
{
  "prompt": "string (required) - Sound description/prompt",
  "audio_length_seconds": 30  // integer (optional) - Desired duration in seconds
}
```

**Status Updates:**
- `"initiating music generation"` - Processing started
- `"generating music"` - Sound generation in progress

**Final Response:**
```json
{
  "error": false,
  "status": "completed",
  "data": {
    "sound_url": "https://url-to-generated-sound.mp3",
    "sound_wav_url": "https://url-to-generated-sound.wav",
    "sound_length": 30.0,
    "music_gpt_task_id": "task-identifier"
  }
}
```

---

## Error Handling

### HTTP Status Codes
- `200`: Success
- `403`: Invalid API Key
- `422`: Validation Error (invalid request parameters)
- `500`: Internal Server Error

### WebSocket Error Handling
WebSocket connections may encounter the following error scenarios:

1. **Connection Errors**: WebSocket disconnects are logged and handled gracefully
2. **Processing Errors**: Errors during music generation are sent as error messages
3. **Timeout Errors**: Long-running operations may timeout (typically 600 seconds max)
4. **Validation Errors**: Missing required parameters result in ValueError exceptions

### Common Error Messages
- `"prompt is required"` - Missing required prompt parameter
- `"missing required params. please pass at least: {params}"` - Missing required WebSocket parameters
- `"No task_id found in result"` - Music generation service didn't return a task ID
- `"Conversion {id} did not complete within {time} seconds"` - Operation timeout

---

## Usage Examples

### REST API Example (cURL)

```bash
# Generate auto prompt
curl -X POST "http://localhost:8000/auto-prompt" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "upbeat summer vibes",
    "model_creativity": 0.5
  }'

# Generate album cover
curl -X POST "http://localhost:8000/album-cover-art" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "synthwave sunset cityscape",
    "model_creativity": 0.7,
    "model": "imagen"
  }'

# Translate text
curl -X POST "http://localhost:8000/translate-text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello, how are you today?",
    "target_language": "Spanish",
    "quality": "high",
    "model_creativity": 0.3
  }'
```

### WebSocket Example (JavaScript)

```javascript
// Music Generation WebSocket
const ws = new WebSocket('ws://localhost:8000/generate-music');

ws.onopen = function() {
    // Send generation request
    ws.send(JSON.stringify({
        prompt: "energetic rock anthem with guitar solos",
        music_style: "Rock",
        lyrics: "We are the champions of our destiny",
        make_instrumental: false
    }));
};

ws.onmessage = function(event) {
    const response = JSON.parse(event.data);

    if (response.error) {
        console.error('Error:', response.data);
        return;
    }

    if (response.status === 'completed') {
        console.log('Music generated!');
        console.log('Song 1:', response.data.song_1_url);
        console.log('Song 2:', response.data.song_2_url);
    } else {
        console.log('Status:', response.status);
        console.log('Data:', response.data);
    }
};

ws.onerror = function(error) {
    console.error('WebSocket error:', error);
};
```

### Python Example

```python
import requests
import websocket
import json

# REST API Example
def generate_artist():
    url = "http://localhost:8000/artist-generation"
    payload = {
        "prompt": "indie folk singer-songwriter from Portland",
        "model_creativity": 0.6
    }

    response = requests.post(url, json=payload)
    if response.status_code == 200:
        artist_data = response.json()
        print(f"Generated artist: {artist_data['artist_alias']}")
        return artist_data
    else:
        print(f"Error: {response.status_code} - {response.text}")

# WebSocket Example
def on_message(ws, message):
    data = json.loads(message)
    if data.get('error'):
        print(f"Error: {data['data']}")
    elif data.get('status') == 'completed':
        print("Music generation completed!")
        print(f"Song URLs: {data['data']}")
    else:
        print(f"Status: {data['status']}")

def on_open(ws):
    request = {
        "prompt": "chill lo-fi hip hop beats",
        "music_style": "Lo-Fi Hip Hop",
        "make_instrumental": True
    }
    ws.send(json.dumps(request))

# Connect to WebSocket
ws = websocket.WebSocketApp("ws://localhost:8000/generate-music",
                          on_message=on_message,
                          on_open=on_open)
ws.run_forever()
```

---

## Technical Notes

### Audio Format Support
- **Input**: Base64-encoded audio data (MP3, WAV supported)
- **Output**: URLs to MP3 and WAV files hosted by the music generation service
- **Limitations**: Current WebSocket implementation accepts audio in single chunks, not optimized for large files

### Processing Times
- **Music Generation**: 30-120 seconds depending on complexity
- **Music Remixing**: 45-180 seconds
- **Music Extension**: 30-90 seconds
- **Vocal Extraction**: 20-60 seconds
- **Sound Generation**: 15-45 seconds
- **Text Translation**: 1-5 seconds
- **Image Generation**: 10-30 seconds

### Rate Limits
Rate limiting is not currently implemented but may be added in future versions.

### CORS Support
The API includes CORS middleware configured to allow all origins (`*`) for development purposes. In production, this should be restricted to specific domains.

---

## Environment Variables

Required environment variables for full functionality:

```bash
# API Configuration
MUSIC_GENERATION_API_KEY=your-api-key-here
VALIDATE_API_KEY=true

# External Service Keys
MUSIC_GPT_API_KEY=your-musicgpt-api-key
OPENAI_API_KEY=your-openai-api-key

# OpenAI Configuration (Azure)
OPENAI_URL=https://your-azure-openai-endpoint.com/
OPENAI_API_VERSION=2024-12-01-preview
OPENAI_DEPLOYMENT_NAME=your-deployment-name
```

---

## Service Dependencies

This API integrates with several external services:

1. **MusicGPT API** (`api.musicgpt.com`) - Core music generation, remixing, and audio processing
2. **OpenAI/Azure OpenAI** - Text translation, prompt generation, and AI text processing
3. **Google Imagen** (optional) - Album cover art generation
4. **Voice Generation Services** - Text-to-speech and voice synthesis

Ensure all required API keys and endpoints are properly configured before deployment.
