# MusicGPT Webhook Integration - Performance Optimization Guide

## Overview

The optimized `generate_music_ws()` function now uses MusicGPT webhooks for real-time completion callbacks, providing significant performance improvements over the previous polling-based approach.

## Performance Improvements

### Before (Polling-based)
- **API Calls**: 40+ status check calls per generation (every 10 seconds for ~6 minutes)
- **Resource Usage**: WebSocket connection blocked during entire generation
- **Latency**: 10-second delay between completion and notification
- **Server Load**: High due to continuous polling loops

### After (Webhook-based)
- **API Calls**: 1 submission call + 1 webhook callback = **95% reduction**
- **Resource Usage**: WebSocket connection remains responsive with heartbeat
- **Latency**: Instant notification via webhook callback
- **Server Load**: Minimal - no polling loops required

## Architecture Flow

```mermaid
sequenceDiagram
    participant Client as Client (WebSocket)
    participant API as Music Generation API
    participant MusicGPT as MusicGPT Service
    participant Webhook as Webhook Endpoint

    Client->>API: Connect WebSocket + Send Request
    API->>API: Generate webhook URL
    API->>MusicGPT: POST /MusicAI with webhook_url
    MusicGPT-->>API: task_id, conversion_ids, eta
    API->>Client: Task submitted confirmation
    
    Note over API,Client: Connection stays alive with heartbeat
    
    MusicGPT->>MusicGPT: Generate music (async)
    MusicGPT->>Webhook: POST completion callback
    Webhook->>API: Process webhook payload
    API->>Client: Send completion data via WebSocket
    API->>API: Cleanup connection
```

## Key Components

### 1. WebSocket Connection Manager
- **Purpose**: Manages WebSocket connections mapped to MusicGPT task IDs
- **Features**: 
  - Automatic cleanup on disconnect
  - Heartbeat to keep connections alive
  - Error handling and recovery

### 2. Webhook Endpoint (`/webhook/musicgpt`)
- **Purpose**: Receives MusicGPT completion callbacks
- **Security**: Should be secured with API key validation in production
- **Payload**: Handles MusicGPT webhook format with task_id, status, audio_url

### 3. Enhanced MusicGPT Client
- **New Method**: `generate_music_with_webhook()`
- **Webhook Integration**: Automatically includes webhook_url in API calls
- **Backward Compatibility**: Original `generate_music()` method still available

## Configuration

### Environment Variables

```bash
# Webhook base URL (required for production)
WEBHOOK_BASE_URL=https://your-domain.com

# Optional: Webhook security
WEBHOOK_SECRET_KEY=your-secret-key

# MusicGPT API configuration
MUSICGPT_API_KEY=your-musicgpt-api-key
MUSICGPT_BASE_URL=https://api.musicgpt.com/api/public/v1
```

### Webhook URL Generation

The webhook URL is automatically generated based on your deployment:

```python
def get_webhook_url() -> str:
    base_url = os.getenv("WEBHOOK_BASE_URL", "https://your-domain.com")
    return f"{base_url}/webhook/musicgpt"
```

## Usage Examples

### Client-Side WebSocket Connection

```javascript
const ws = new WebSocket('ws://localhost:8000/generate-music');

ws.onopen = function() {
    // Send music generation request
    ws.send(JSON.stringify({
        prompt: "energetic rock anthem with electric guitar",
        music_style: "Rock",
        lyrics: "We are the champions of the world",
        make_instrumental: false
    }));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.status) {
        case 'initiating':
            console.log('Starting music generation...');
            break;
            
        case 'submitted':
            console.log(`Task submitted: ${data.data.music_gpt_task_id}`);
            console.log(`ETA: ${data.data.eta_seconds} seconds`);
            break;
            
        case 'heartbeat':
            console.log('Connection alive, waiting for completion...');
            break;
            
        case 'completed':
            console.log('Music generation completed!');
            console.log(`Audio URL: ${data.data.song_1_url}`);
            console.log(`Title: ${data.data.song_1_name}`);
            console.log(`Cost: $${data.data.conversion_cost}`);
            // Download or play the generated music
            break;
            
        case 'failed':
            console.error(`Generation failed: ${data.data.error_message}`);
            break;
    }
};
```

### Server-Side Webhook Handling

The webhook endpoint automatically handles MusicGPT callbacks:

```python
# Webhook payload example from MusicGPT
{
    "task_id": "12345678-abcd-1234-efgh-567890abcdef",
    "status": "COMPLETED",
    "status_msg": "Conversion successful",
    "conversion_type": "MUSIC_AI",
    "audio_url": "https://musicgpt.s3.amazonaws.com/audiofile.mp3",
    "title": "Generated Song",
    "conversion_cost": 1.25,
    "createdAt": "2025-01-01T12:00:00Z",
    "updatedAt": "2025-01-01T12:05:00Z"
}
```

## Error Handling

### Connection Management
- **WebSocket Disconnects**: Automatic cleanup of task mappings
- **Webhook Failures**: Graceful handling with warning logs
- **API Errors**: Proper error propagation to client

### Timeout Handling
- **Heartbeat**: 30-second intervals to keep connections alive
- **Webhook Timeout**: MusicGPT handles timeout internally
- **Connection Cleanup**: Automatic cleanup after completion/failure

## Monitoring and Debugging

### Logging
```python
# Key log messages to monitor
logger.info(f"WebSocket connected for task {task_id}")
logger.info(f"Received webhook for task {task_id}: {status}")
logger.info(f"Music generation task {task_id} submitted with webhook {webhook_url}")
```

### Health Checks
- Monitor webhook endpoint availability
- Track WebSocket connection counts
- Monitor MusicGPT API response times

## Production Deployment

### Security Considerations
1. **Webhook Authentication**: Validate webhook signatures
2. **Rate Limiting**: Implement rate limiting on webhook endpoint
3. **HTTPS**: Use HTTPS for webhook URLs
4. **API Key Protection**: Secure MusicGPT API keys

### Scaling Considerations
1. **Connection Limits**: Monitor WebSocket connection limits
2. **Memory Usage**: Connection manager uses minimal memory
3. **Load Balancing**: Webhook callbacks work with load balancers
4. **Database**: Consider persisting task states for recovery

### Monitoring Metrics
- **Webhook Success Rate**: Track successful webhook deliveries
- **Generation Completion Time**: Monitor end-to-end performance
- **Connection Duration**: Track WebSocket connection lifetimes
- **Error Rates**: Monitor API and webhook error rates

## Migration from Polling

### Backward Compatibility
The original polling-based endpoints remain available:
- Existing clients continue to work without changes
- Gradual migration possible
- A/B testing supported

### Migration Steps
1. Deploy webhook infrastructure
2. Update clients to use new WebSocket flow
3. Monitor performance improvements
4. Gradually deprecate polling endpoints

## Troubleshooting

### Common Issues
1. **Webhook Not Received**: Check WEBHOOK_BASE_URL configuration
2. **Connection Drops**: Verify heartbeat functionality
3. **Task Not Found**: Check task_id mapping in connection manager
4. **API Errors**: Verify MusicGPT API key and endpoint configuration

### Debug Mode
Enable detailed logging for troubleshooting:
```python
logging.getLogger("api.src.main").setLevel(logging.DEBUG)
```

This optimization provides a foundation for high-performance, scalable music generation with real-time user feedback and minimal resource usage.
