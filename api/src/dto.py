from pydantic import BaseModel, Field
from typing import Optional


class PromptingParams(BaseModel):
    prompt: str
    model_creativity: float = 0.2
    model: str = None
    agent_description: str = None


class VoiceGenerationParams(BaseModel):
    voice_description: str


class VoiceOverParams(BaseModel):
    script: str
    voice_id: str
    language_code: str = "en"


class TranslateAudioParams(BaseModel):
    audio_base64: str
    target_language: str


class TextTranslationParams(BaseModel):
    text: str = Field(..., description="Text to translate", min_length=1, max_length=10000)
    target_language: str = Field(..., description="Target language for translation")
    source_language: Optional[str] = Field(None, description="Source language (optional, defaults to English if not provided)")
    context: Optional[str] = Field(None, description="Additional context to help with translation", max_length=500)
    quality: str = Field("standard", description="Translation quality level: standard, high, or creative")
    model_creativity: float = Field(0.2, description="Model creativity level", ge=0.0, le=1.0)
