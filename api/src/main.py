import asyncio
import base64
from contextlib import asynccontextmanager
from fastapi import (
    FastAPI,
    Response,
    HTTPException,
    WebSocket,
    Request
)
from starlette.websockets import WebSocketDisconnect
from fastapi.websockets import WebSocketState
from fastapi.middleware.cors import CORSMiddleware
import logging
import os
import gzip
from typing import Dict, Any, Optional
import uuid
from datetime import datetime
from pydantic import BaseModel

from .dto import (
    PromptingParams,
    VoiceGenerationParams,
    VoiceOverParams,
    TranslateAudioParams,
    TextTranslationParams
)
from .generative_ai import (
    AlbumImageGenerator,
    MusicGPTClient,
    AutoPrompter,
    ArtistGenerator,
    SpokenWordGenerator,
    TranslationService
)
from .generative_ai.translation import TranslationQuality


logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# ============================================================================
# WEBHOOK MODELS AND CONNECTION MANAGER
# ============================================================================

class MusicGPTWebhookPayload(BaseModel):
    """MusicGPT webhook payload model"""
    task_id: str
    status: str
    status_msg: str
    conversion_type: str
    audio_url: Optional[str] = None
    title: Optional[str] = None
    conversion_cost: Optional[float] = None
    createdAt: str
    updatedAt: str

class WebSocketConnectionManager:
    """Manages WebSocket connections for webhook callbacks"""

    def __init__(self):
        # Map task_id to WebSocket connection
        self.active_connections: Dict[str, WebSocket] = {}
        # Map task_id to generation metadata
        self.task_metadata: Dict[str, Dict[str, Any]] = {}
        # Map WebSocket connection to task_ids for cleanup
        self.connection_tasks: Dict[str, set] = {}

    async def connect(self, websocket: WebSocket, task_id: str, metadata: Dict[str, Any]):
        """Register WebSocket connection for a task"""
        await websocket.accept()
        self.active_connections[task_id] = websocket
        self.task_metadata[task_id] = metadata

        # Track connection for cleanup
        conn_id = str(id(websocket))
        if conn_id not in self.connection_tasks:
            self.connection_tasks[conn_id] = set()
        self.connection_tasks[conn_id].add(task_id)

        logger.info(f"WebSocket connected for task {task_id}")

    def disconnect(self, websocket: WebSocket):
        """Clean up WebSocket connection"""
        conn_id = str(id(websocket))
        if conn_id in self.connection_tasks:
            # Clean up all tasks for this connection
            for task_id in self.connection_tasks[conn_id]:
                self.active_connections.pop(task_id, None)
                self.task_metadata.pop(task_id, None)
            del self.connection_tasks[conn_id]

        logger.info(f"WebSocket disconnected: {conn_id}")

    async def send_message(self, task_id: str, message: Dict[str, Any]):
        """Send message to WebSocket for specific task"""
        if task_id in self.active_connections:
            websocket = self.active_connections[task_id]
            try:
                if websocket.application_state == WebSocketState.CONNECTED:
                    await websocket.send_json(message)
                    return True
                else:
                    # Clean up disconnected WebSocket
                    self.active_connections.pop(task_id, None)
                    self.task_metadata.pop(task_id, None)
            except Exception as e:
                logger.error(f"Error sending message to task {task_id}: {e}")
                # Clean up failed connection
                self.active_connections.pop(task_id, None)
                self.task_metadata.pop(task_id, None)
        return False

    async def handle_webhook_callback(self, webhook_payload: MusicGPTWebhookPayload):
        """Handle incoming webhook callback from MusicGPT"""
        task_id = webhook_payload.task_id
        logger.info(f"Received webhook for task {task_id}: {webhook_payload.status}")

        if task_id not in self.active_connections:
            logger.warning(f"No active WebSocket connection for task {task_id}")
            return False

        # Get original metadata
        metadata = self.task_metadata.get(task_id, {})

        # Prepare WebSocket message based on status
        if webhook_payload.status == "COMPLETED":
            message = {
                "error": False,
                "status": "completed",
                "data": {
                    "song_1_url": webhook_payload.audio_url,
                    "song_1_name": webhook_payload.title,
                    "music_gpt_task_id": task_id,
                    "conversion_cost": webhook_payload.conversion_cost,
                    "webhook_received_at": datetime.utcnow().isoformat(),
                    **metadata  # Include original generation parameters
                }
            }
        elif webhook_payload.status == "FAILED":
            message = {
                "error": True,
                "status": "failed",
                "data": {
                    "error_message": webhook_payload.status_msg,
                    "music_gpt_task_id": task_id,
                    "webhook_received_at": datetime.utcnow().isoformat()
                }
            }
        else:
            # In-progress status
            message = {
                "error": False,
                "status": "processing",
                "data": {
                    "status_message": webhook_payload.status_msg,
                    "music_gpt_task_id": task_id,
                    "webhook_received_at": datetime.utcnow().isoformat()
                }
            }

        # Send message to WebSocket
        success = await self.send_message(task_id, message)

        # Clean up completed/failed tasks
        if webhook_payload.status in ["COMPLETED", "FAILED"]:
            self.active_connections.pop(task_id, None)
            self.task_metadata.pop(task_id, None)

        return success

# Global connection manager
connection_manager = WebSocketConnectionManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Startup")

    yield  # separate startup/shutdown

    logger.info("Shutting Down")


app = FastAPI(lifespan=lifespan)
origins = ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def validate_api_key(api_key: str):
    validate = os.getenv("VALIDATE_API_KEY", "true")
    if validate.lower() == "false":
        return api_key

    expected_key = os.getenv("MUSIC_GENERATION_API_KEY", "api-key")
    if api_key != expected_key:
        raise HTTPException(status_code=403, detail="Invalid API Key")

    return api_key


@app.get("/ping")
async def ping():
    return Response(
        status_code=200,
        content="pong"
    )


@app.post("/webhook/musicgpt")
async def musicgpt_webhook(request: Request):
    """
    Webhook endpoint for MusicGPT callbacks
    """
    try:
        # Parse webhook payload
        payload_data = await request.json()
        webhook_payload = MusicGPTWebhookPayload(**payload_data)

        logger.info(f"Received MusicGPT webhook: {webhook_payload.task_id} - {webhook_payload.status}")

        # Handle the webhook callback
        success = await connection_manager.handle_webhook_callback(webhook_payload)

        if success:
            return {"status": "success", "message": "Webhook processed successfully"}
        else:
            return {"status": "warning", "message": "No active WebSocket connection found for task"}

    except Exception as e:
        logger.error(f"Error processing MusicGPT webhook: {e}")
        raise HTTPException(status_code=500, detail=f"Webhook processing failed: {str(e)}")


def get_webhook_url() -> str:
    """Get the webhook URL for MusicGPT callbacks"""
    # In production, this should be your actual domain
    base_url = os.getenv("WEBHOOK_BASE_URL", "https://your-domain.com")
    return f"{base_url}/webhook/musicgpt"


@app.websocket("/generate-music")
async def generate_music_ws(websocket: WebSocket):
    """
    Optimized WebSocket Endpoint for Generating Music with Webhook Integration

    This endpoint now uses MusicGPT webhooks for real-time callbacks instead of polling,
    significantly improving performance and reducing API calls.
    """
    logger.info("WS Connection Accepted for Music Generation")

    try:
        # Accept connection but don't register yet - wait for task_id
        await websocket.accept()
        await asyncio.sleep(0.1)  # Ensure connection stability

        # Receive generation request
        data = await websocket.receive_json()

        if "prompt" not in data:
            await websocket.send_json({
                "error": True,
                "data": "prompt is required"
            })
            return

        logger.info(f"Initiating optimized music generation with: {data}")

        # Send initial status
        await websocket.send_json({
            "error": False,
            "status": "initiating",
            "data": {
                "message": "Starting music generation with webhook integration",
                "prompt": data["prompt"],
                "music_style": data.get("music_style"),
                "webhook_enabled": True
            }
        })

        try:
            # Create MusicGPT client without WebSocket (we'll handle callbacks via webhook)
            client = MusicGPTClient(websocket=None, logger=logger)

            # Generate webhook URL
            webhook_url = get_webhook_url()

            # Call MusicGPT API with webhook URL
            result = client.generate_music_with_webhook(
                prompt=data["prompt"],
                music_style=data.get("music_style"),
                lyrics=data.get("lyrics"),
                make_instrumental=data.get("make_instrumental", False),
                vocal_only=data.get("vocal_only", False),
                voice_id=data.get("voice_id"),
                webhook_url=webhook_url
            )

            task_id = result.get('task_id')
            if not task_id:
                raise ValueError(f"No task_id received from MusicGPT API: {result}")

            # Register WebSocket connection with task_id for webhook callbacks
            await connection_manager.connect(websocket, task_id, {
                "prompt": data["prompt"],
                "music_style": data.get("music_style"),
                "lyrics": data.get("lyrics"),
                "make_instrumental": data.get("make_instrumental", False),
                "vocal_only": data.get("vocal_only", False),
                "voice_id": data.get("voice_id"),
                "eta": result.get("eta"),
                "conversion_id_1": result.get("conversion_id_1"),
                "conversion_id_2": result.get("conversion_id_2")
            })

            # Send task submission confirmation
            await websocket.send_json({
                "error": False,
                "status": "submitted",
                "data": {
                    "message": "Music generation task submitted successfully",
                    "music_gpt_task_id": task_id,
                    "eta_seconds": result.get("eta"),
                    "conversion_id_1": result.get("conversion_id_1"),
                    "conversion_id_2": result.get("conversion_id_2"),
                    "webhook_url": webhook_url,
                    "submitted_at": datetime.now().isoformat()
                }
            })

            logger.info(f"Music generation task {task_id} submitted with webhook {webhook_url}")

            # Keep connection alive for webhook callbacks
            # The webhook will handle sending completion/error messages
            try:
                while websocket.application_state == WebSocketState.CONNECTED:
                    # Send periodic heartbeat to keep connection alive
                    await asyncio.sleep(30)
                    if task_id in connection_manager.active_connections:
                        await websocket.send_json({
                            "error": False,
                            "status": "heartbeat",
                            "data": {
                                "message": "Connection alive, waiting for webhook callback",
                                "task_id": task_id,
                                "timestamp": datetime.now().isoformat()
                            }
                        })
                    else:
                        # Task completed or failed, connection no longer needed
                        break

            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected for task {task_id}")
                connection_manager.disconnect(websocket)

        except Exception as e:
            error_msg = f"Error generating music: {e}"
            logger.error(error_msg)

            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "status": "failed",
                    "data": {
                        "error_message": error_msg,
                        "error_type": type(e).__name__
                    }
                })

    except WebSocketDisconnect:
        logger.info("WebSocket connection closed during setup")
        connection_manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"Unexpected error in generate_music_ws: {e}")
        try:
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "data": f"Unexpected error: {e}"
                })
        except:
            pass
    finally:
        # Ensure cleanup
        connection_manager.disconnect(websocket)
        if websocket.application_state == WebSocketState.CONNECTED:
            await websocket.close()


# TODO: Consolidate WS code
@app.websocket("/remix-music")
async def remix_music_ws(websocket: WebSocket):
    """
    Websocket Endpoint for Remixing Music
    """
    # NOTE: Right now this endpoint takes in base64 encoded audio
    # data in one-chunk for simplicity. This is not ideal for large files,
    # and should be optimized alter on to recieve chunked byte payloads
    logger.info("WS Connection Accepted")
    try:
        await websocket.accept()
        # Small delay to ensure connection is fully established
        await asyncio.sleep(0.1)
        data = await websocket.receive_json()
        try:
            await MusicGPTClient(
                websocket,
                logger=logger
            ).remix_music_and_send_data_over_ws(data)
        except Exception as e:
            msg = f"Error Remixing Music: {e}"
            logger.error(msg)
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "data": msg
                })

    except WebSocketDisconnect:
        logger.info("WS connection closed")

    if websocket.application_state == WebSocketState.CONNECTED:
        await websocket.close()


@app.websocket("/extend-music")
async def extend_music_ws(websocket: WebSocket):
    """
    Websocket Endpoint for Extending Music
    """
    # NOTE: Right now this endpoint takes in base64 encoded audio
    # data in one-chunk for simplicity. This is not ideal for large files,
    # and should be optimized alter on to recieve chunked byte payloads
    logger.info("WS Connection Accepted")
    try:
        await websocket.accept()
        # Small delay to ensure connection is fully established
        await asyncio.sleep(0.1)
        data = await websocket.receive_json()
        try:
            await MusicGPTClient(
                websocket,
                logger=logger
            ).extend_music_and_send_data_over_ws(data)
        except Exception as e:
            msg = f"Error Extending Music: {e}"
            logger.error(msg)
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "data": msg
                })

    except WebSocketDisconnect:
        logger.info("WS connection closed")

    if websocket.application_state == WebSocketState.CONNECTED:
        await websocket.close()


@app.websocket("/extract-vocals")
async def extract_vocals_ws(websocket: WebSocket):
    """
    Websocket Endpoint for Extracting Vocals from Music
    """
    # NOTE: Right now this endpoint takes in base64 encoded audio
    # data in one-chunk for simplicity. This is not ideal for large files,
    # and should be optimized alter on to recieve chunked byte payloads
    logger.info("WS Connection Accepted")
    try:
        await websocket.accept()
        # Small delay to ensure connection is fully established
        await asyncio.sleep(0.1)
        data = await websocket.receive_json()
        try:
            await MusicGPTClient(
                websocket,
                logger=logger
            ).extract_vocals_and_send_data_over_ws(data)
        except Exception as e:
            msg = f"Error extracting vocals: {e}"
            logger.error(msg)
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "data": msg
                })

    except WebSocketDisconnect:
        logger.info("WS connection closed")

    if websocket.application_state == WebSocketState.CONNECTED:
        await websocket.close()


@app.websocket("/sound-generation")
async def sound_generation_ws(websocket: WebSocket):
    """
    Websocket Endpoint for sound generation
    """
    # NOTE: Right now this endpoint takes in base64 encoded audio
    # data in one-chunk for simplicity. This is not ideal for large files,
    # and should be optimized alter on to recieve chunked byte payloads
    logger.info("WS Connection Accepted")
    try:
        await websocket.accept()
        # Small delay to ensure connection is fully established
        await asyncio.sleep(0.1)
        data = await websocket.receive_json()
        try:
            await MusicGPTClient(
                websocket,
                logger=logger
            ).generate_sound_and_send_data_over_ws(data)
        except Exception as e:
            msg = f"Error generating sounds: {e}"
            logger.error(msg)
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "data": msg
                })

    except WebSocketDisconnect:
        logger.info("WS connection closed")

    if websocket.application_state == WebSocketState.CONNECTED:
        await websocket.close()


@app.post("/auto-prompt")
async def auto_prompt(params: PromptingParams):
    auto_prompter = AutoPrompter(params.prompt, params.model_creativity)
    logger.info(f"Generating Prompt for: {params.prompt}, creativity: {params.model_creativity}")
    try:
        resp = auto_prompter.generate_prompt()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error Generating Prompt: {e}")

    logger.info(f"Generated Prompt: {resp}")

    return resp


@app.post("/artist-generation")
async def artist_generator(params: PromptingParams):
    artist_generator = ArtistGenerator(params.prompt, params.model_creativity, params.agent_description)
    logger.info(f"Generating artist for: {params.prompt}, creativity: {params.model_creativity}, agent_description: {params.agent_description}")
    try:
        resp = artist_generator.generate_prompt()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error Generating artist: {e}")

    logger.info(f"Generated artist: {resp}")

    return resp


@app.post("/album-cover-art")
async def album_cover_art(params: PromptingParams):
    model = params.model or "imagen"
    album_image_gen = AlbumImageGenerator(params.prompt, params.model_creativity, model)
    logger.info(f"Generating Album cover for: {params.prompt}, creativity: {params.model_creativity}, model: {model}")
    try:
        resp = album_image_gen.generate_album_cover()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error Generating Album Cover: {e}")

    return {
        "error": False,
        "image_base64": base64.b64encode(resp).decode("utf-8")
    }


@app.post("/spoken-word-script")
async def spoken_word_script(params: PromptingParams):
    spoken_word_generator = SpokenWordGenerator()
    logger.info(f"Generating spoken word script for: {params.prompt}, creativity: {params.model_creativity}, agent_description: {params.agent_description}")
    try:
        resp = spoken_word_generator.generate_script(params.prompt, params.model_creativity)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error Generating script: {e}")

    logger.info(f"Generated artist: {resp}")

    return resp


@app.post("/generate-voice")
async def generate_voice(params: VoiceGenerationParams):
    spoken_word_generator = SpokenWordGenerator()
    logger.info(f"Generating voice for: {params.voice_description}")
    try:
        resp = spoken_word_generator.generate_voice(params.voice_description)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error Generating voice: {e}")

    return resp


@app.post("/generate-voice-over")
async def generate_voice_over(params: VoiceOverParams):
    spoken_word_generator = SpokenWordGenerator()
    logger.info(f"Generating voice over for: {params.script}")
    try:
        audio_chunks = []
        for chunk in spoken_word_generator.generate_voice_over(
            params.script,
            params.voice_id,
            params.language_code
        ):
            audio_chunks.append(chunk)
        complete_audio = b''.join(audio_chunks)
        if params.compress_audio:
            complete_audio = gzip.compress(complete_audio)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error Generating voice over: {e}")

    return {
        "error": False,
        "audio_base64": base64.b64encode(complete_audio).decode("utf-8"),
        "compressed": params.compress_audio
    }


@app.websocket("/generate-voice-over")
async def generate_voice_over_ws(websocket: WebSocket):
    """
    Websocket Endpoint for Voice Over Generation with streaming audio chunks
    """
    logger.info("WS Connection Accepted")
    try:
        await websocket.accept()
        await asyncio.sleep(0.1)
        data = await websocket.receive_json()

        if "script" not in data or "voice_id" not in data:
            await websocket.send_json({
                "error": True,
                "data": "missing required params: script and voice_id"
            })
            return

        spoken_word_generator = SpokenWordGenerator()
        logger.info(f"Generating voice over for: {data['script'][:100]}...")

        await websocket.send_json({
            "error": False,
            "status": "initiating voice over generation",
            "data": {
                "script_length": len(data["script"]),
                "voice_id": data["voice_id"]
            }
        })

        try:
            chunk_count = 0
            for chunk in spoken_word_generator.generate_voice_over(
                data["script"],
                data["voice_id"],
                data.get("language_code", "en")
            ):
                chunk_count += 1
                if data.get("compress_audio", False):
                    chunk = gzip.compress(chunk)

                # Send audio chunk
                await websocket.send_json({
                    "error": False,
                    "status": "streaming_audio",
                    "data": {
                        "audio_chunk": base64.b64encode(chunk).decode("utf-8"),
                        "chunk_number": chunk_count,
                        "compressed": data.get("compress_audio", False)
                    }
                })

            # Send completion message
            await websocket.send_json({
                "error": False,
                "status": "completed",
                "data": {
                    "total_chunks": chunk_count,
                    "message": "Voice over generation completed"
                }
            })

        except Exception as e:
            msg = f"Error generating voice over: {e}"
            logger.error(msg)
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "data": msg
                })

    except WebSocketDisconnect:
        logger.info("WS connection closed")

    if websocket.application_state == WebSocketState.CONNECTED:
        await websocket.close()


@app.post("/translate-wav-audio")
async def translate_audio(params: TranslateAudioParams):
    spoken_word_generator = SpokenWordGenerator()
    logger.info(f"Translating audio to: {params.target_language}")
    try:
        audio_chunks = []
        for chunk in spoken_word_generator.translate_audio(
            params.audio_base64,
            params.target_language
        ):
            audio_chunks.append(chunk)
        complete_audio = b''.join(audio_chunks)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error translating: {e}")

    return {
        "error": False,
        "audio_base64": base64.b64encode(complete_audio).decode("utf-8")
    }


@app.post("/translate-text")
async def translate_text(params: TextTranslationParams):
    """
    Translate text from one language to another using OpenAI's GPT models.
    """
    translation_service = TranslationService()
    logger.info(f"Translating text to {params.target_language}: {params.text[:100]}...")

    try:
        quality_map = {
            "standard": TranslationQuality.STANDARD,
            "high": TranslationQuality.HIGH,
            "creative": TranslationQuality.CREATIVE
        }
        quality = quality_map.get(params.quality.lower(), TranslationQuality.STANDARD)

        # Perform translation
        result = translation_service.translate_text(
            text=params.text,
            target_language=params.target_language,
            source_language=params.source_language,
            context=params.context,
            quality=quality,
            model_creativity=params.model_creativity
        )

        logger.info("Translation completed successfully")

        return {
            "error": False,
            "translation": result.translated_text,
            "source_language": result.source_language,
            "target_language": result.target_language,
            "confidence_score": result.confidence_score,
        }

    except ValueError as e:
        logger.error(f"Validation error in translation: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error translating text: {e}")
        raise HTTPException(status_code=500, detail=f"Error translating text: {e}")
