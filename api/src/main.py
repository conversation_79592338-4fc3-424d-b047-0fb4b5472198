import asyncio
import base64
from contextlib import asynccontextmanager
from fastapi import (
    FastAP<PERSON>,
    Response,
    HTTPException,
    WebSocket
)
from starlette.websockets import WebSocketDisconnect
from fastapi.websockets import WebSocketState
from fastapi.middleware.cors import CORSMiddleware
import logging
import os

from .dto import (
    PromptingParams,
    VoiceGenerationParams,
    VoiceOverParams,
    TranslateAudioParams,
    TextTranslationParams
)
from .generative_ai import (
    AlbumImageGenerator,
    MusicGPTClient,
    AutoPrompter,
    ArtistGenerator,
    SpokenWordGenerator,
    TranslationService
)
from .generative_ai.translation import TranslationQuality


logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Startup")

    yield  # separate startup/shutdown

    logger.info("Shutting Down")


app = FastAPI(lifespan=lifespan)
origins = ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def validate_api_key(api_key: str):
    validate = os.getenv("VALIDATE_API_KEY", "true")
    if validate.lower() == "false":
        return api_key

    expected_key = os.getenv("MUSIC_GENERATION_API_KEY", "api-key")
    if api_key != expected_key:
        raise HTTPException(status_code=403, detail="Invalid API Key")

    return api_key


@app.get("/ping")
async def ping():
    return Response(
        status_code=200,
        content="pong"
    )


@app.websocket("/generate-music")
async def generate_music_ws(websocket: WebSocket):
    """
    Websocket Endpoint for Generating Music
    """
    logger.info("WS Connection Accepted")
    try:
        await websocket.accept()
        # Small delay to ensure connection is fully established
        await asyncio.sleep(0.1)
        data = await websocket.receive_json()
        try:
            await MusicGPTClient(
                websocket,
                logger=logger
            ).generate_music_and_send_data_over_ws(data)
        except Exception as e:
            msg = f"Error Generating Music: {e}"
            logger.error(msg)
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "data": msg
                })

    except WebSocketDisconnect:
        logger.info("WS connection closed")

    if websocket.application_state == WebSocketState.CONNECTED:
        await websocket.close()


# TODO: Consolidate WS code
@app.websocket("/remix-music")
async def remix_music_ws(websocket: WebSocket):
    """
    Websocket Endpoint for Remixing Music
    """
    # NOTE: Right now this endpoint takes in base64 encoded audio
    # data in one-chunk for simplicity. This is not ideal for large files,
    # and should be optimized alter on to recieve chunked byte payloads
    logger.info("WS Connection Accepted")
    try:
        await websocket.accept()
        # Small delay to ensure connection is fully established
        await asyncio.sleep(0.1)
        data = await websocket.receive_json()
        try:
            await MusicGPTClient(
                websocket,
                logger=logger
            ).remix_music_and_send_data_over_ws(data)
        except Exception as e:
            msg = f"Error Remixing Music: {e}"
            logger.error(msg)
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "data": msg
                })

    except WebSocketDisconnect:
        logger.info("WS connection closed")

    if websocket.application_state == WebSocketState.CONNECTED:
        await websocket.close()


@app.websocket("/extend-music")
async def extend_music_ws(websocket: WebSocket):
    """
    Websocket Endpoint for Extending Music
    """
    # NOTE: Right now this endpoint takes in base64 encoded audio
    # data in one-chunk for simplicity. This is not ideal for large files,
    # and should be optimized alter on to recieve chunked byte payloads
    logger.info("WS Connection Accepted")
    try:
        await websocket.accept()
        # Small delay to ensure connection is fully established
        await asyncio.sleep(0.1)
        data = await websocket.receive_json()
        try:
            await MusicGPTClient(
                websocket,
                logger=logger
            ).extend_music_and_send_data_over_ws(data)
        except Exception as e:
            msg = f"Error Extending Music: {e}"
            logger.error(msg)
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "data": msg
                })

    except WebSocketDisconnect:
        logger.info("WS connection closed")

    if websocket.application_state == WebSocketState.CONNECTED:
        await websocket.close()


@app.websocket("/extract-vocals")
async def extract_vocals_ws(websocket: WebSocket):
    """
    Websocket Endpoint for Extracting Vocals from Music
    """
    # NOTE: Right now this endpoint takes in base64 encoded audio
    # data in one-chunk for simplicity. This is not ideal for large files,
    # and should be optimized alter on to recieve chunked byte payloads
    logger.info("WS Connection Accepted")
    try:
        await websocket.accept()
        # Small delay to ensure connection is fully established
        await asyncio.sleep(0.1)
        data = await websocket.receive_json()
        try:
            await MusicGPTClient(
                websocket,
                logger=logger
            ).extract_vocals_and_send_data_over_ws(data)
        except Exception as e:
            msg = f"Error extracting vocals: {e}"
            logger.error(msg)
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "data": msg
                })

    except WebSocketDisconnect:
        logger.info("WS connection closed")

    if websocket.application_state == WebSocketState.CONNECTED:
        await websocket.close()


@app.websocket("/sound-generation")
async def sound_generation_ws(websocket: WebSocket):
    """
    Websocket Endpoint for sound generation
    """
    # NOTE: Right now this endpoint takes in base64 encoded audio
    # data in one-chunk for simplicity. This is not ideal for large files,
    # and should be optimized alter on to recieve chunked byte payloads
    logger.info("WS Connection Accepted")
    try:
        await websocket.accept()
        # Small delay to ensure connection is fully established
        await asyncio.sleep(0.1)
        data = await websocket.receive_json()
        try:
            await MusicGPTClient(
                websocket,
                logger=logger
            ).generate_sound_and_send_data_over_ws(data)
        except Exception as e:
            msg = f"Error generating sounds: {e}"
            logger.error(msg)
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "data": msg
                })

    except WebSocketDisconnect:
        logger.info("WS connection closed")

    if websocket.application_state == WebSocketState.CONNECTED:
        await websocket.close()


@app.post("/auto-prompt")
async def auto_prompt(params: PromptingParams):
    auto_prompter = AutoPrompter(params.prompt, params.model_creativity)
    logger.info(f"Generating Prompt for: {params.prompt}, creativity: {params.model_creativity}")
    try:
        resp = auto_prompter.generate_prompt()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error Generating Prompt: {e}")

    logger.info(f"Generated Prompt: {resp}")

    return resp


@app.post("/artist-generation")
async def artist_generator(params: PromptingParams):
    artist_generator = ArtistGenerator(params.prompt, params.model_creativity, params.agent_description)
    logger.info(f"Generating artist for: {params.prompt}, creativity: {params.model_creativity}, agent_description: {params.agent_description}")
    try:
        resp = artist_generator.generate_prompt()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error Generating artist: {e}")

    logger.info(f"Generated artist: {resp}")

    return resp


@app.post("/album-cover-art")
async def album_cover_art(params: PromptingParams):
    model = params.model or "imagen"
    album_image_gen = AlbumImageGenerator(params.prompt, params.model_creativity, model)
    logger.info(f"Generating Album cover for: {params.prompt}, creativity: {params.model_creativity}, model: {model}")
    try:
        resp = album_image_gen.generate_album_cover()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error Generating Album Cover: {e}")

    return {
        "error": False,
        "image_base64": base64.b64encode(resp).decode("utf-8")
    }


@app.post("/spoken-word-script")
async def spoken_word_script(params: PromptingParams):
    spoken_word_generator = SpokenWordGenerator()
    logger.info(f"Generating spoken word script for: {params.prompt}, creativity: {params.model_creativity}, agent_description: {params.agent_description}")
    try:
        resp = spoken_word_generator.generate_script(params.prompt, params.model_creativity)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error Generating script: {e}")

    logger.info(f"Generated artist: {resp}")

    return resp


@app.post("/generate-voice")
async def generate_voice(params: VoiceGenerationParams):
    spoken_word_generator = SpokenWordGenerator()
    logger.info(f"Generating voice for: {params.voice_description}")
    try:
        resp = spoken_word_generator.generate_voice(params.voice_description)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error Generating voice: {e}")

    return resp


@app.post("/generate-voice-over")
async def generate_voice_over(params: VoiceOverParams):
    spoken_word_generator = SpokenWordGenerator()
    logger.info(f"Generating voice over for: {params.script}")
    try:
        audio_chunks = []
        for chunk in spoken_word_generator.generate_voice_over(
            params.script,
            params.voice_id,
            params.language_code
        ):
            audio_chunks.append(chunk)
        complete_audio = b''.join(audio_chunks)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error Generating voice over: {e}")

    return {
        "error": False,
        "audio_base64": base64.b64encode(complete_audio).decode("utf-8")
    }


@app.post("/translate-wav-audio")
async def translate_audio(params: TranslateAudioParams):
    spoken_word_generator = SpokenWordGenerator()
    logger.info(f"Translating audio to: {params.target_language}")
    try:
        audio_chunks = []
        for chunk in spoken_word_generator.translate_audio(
            params.audio_base64,
            params.target_language
        ):
            audio_chunks.append(chunk)
        complete_audio = b''.join(audio_chunks)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error translating: {e}")

    return {
        "error": False,
        "audio_base64": base64.b64encode(complete_audio).decode("utf-8")
    }


@app.post("/translate-text")
async def translate_text(params: TextTranslationParams):
    """
    Translate text from one language to another using OpenAI's GPT models.
    """
    translation_service = TranslationService()
    logger.info(f"Translating text to {params.target_language}: {params.text[:100]}...")

    try:
        quality_map = {
            "standard": TranslationQuality.STANDARD,
            "high": TranslationQuality.HIGH,
            "creative": TranslationQuality.CREATIVE
        }
        quality = quality_map.get(params.quality.lower(), TranslationQuality.STANDARD)

        # Perform translation
        result = translation_service.translate_text(
            text=params.text,
            target_language=params.target_language,
            source_language=params.source_language,
            context=params.context,
            quality=quality,
            model_creativity=params.model_creativity
        )

        logger.info("Translation completed successfully")

        return {
            "error": False,
            "translation": result.translated_text,
            "source_language": result.source_language,
            "target_language": result.target_language,
            "confidence_score": result.confidence_score,
        }

    except ValueError as e:
        logger.error(f"Validation error in translation: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error translating text: {e}")
        raise HTTPException(status_code=500, detail=f"Error translating text: {e}")
