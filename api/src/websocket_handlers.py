"""
Optimized WebSocket handlers for music generation
"""
import asyncio
import json
import logging
from typing import Dict, Any, Optional
from fastapi import WebSocket
from fastapi.websockets import WebSocketState
from .job_queue.redis_queue import MusicJobQueue, JobType, JobStatus

logger = logging.getLogger(__name__)

class WebSocketJobHandler:
    def __init__(self, queue: MusicJobQueue):
        self.queue = queue
        self.active_connections: Dict[str, WebSocket] = {}
        self.job_to_websocket: Dict[str, str] = {}
    
    async def handle_music_generation(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle music generation request via job queue"""
        try:
            # Generate connection ID
            connection_id = f"conn_{id(websocket)}"
            self.active_connections[connection_id] = websocket
            
            # Submit job to queue
            job_id = await self.queue.submit_job(
                job_type=JobType.MUSIC_GENERATION,
                job_data=data,
                user_id=data.get("user_id"),
                priority=data.get("priority", 0)
            )
            
            # Map job to websocket
            self.job_to_websocket[job_id] = connection_id
            
            # Send initial response
            await self.send_message(websocket, {
                "job_id": job_id,
                "status": "queued",
                "message": "Job submitted to queue"
            })
            
            # Start monitoring job progress
            asyncio.create_task(self.monitor_job_progress(job_id, websocket))
            
        except Exception as e:
            logger.error(f"Error handling music generation: {e}")
            await self.send_error(websocket, str(e))
    
    async def handle_music_remix(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle music remix request via job queue"""
        try:
            connection_id = f"conn_{id(websocket)}"
            self.active_connections[connection_id] = websocket
            
            job_id = await self.queue.submit_job(
                job_type=JobType.MUSIC_REMIX,
                job_data=data,
                user_id=data.get("user_id"),
                priority=data.get("priority", 0)
            )
            
            self.job_to_websocket[job_id] = connection_id
            
            await self.send_message(websocket, {
                "job_id": job_id,
                "status": "queued",
                "message": "Remix job submitted to queue"
            })
            
            asyncio.create_task(self.monitor_job_progress(job_id, websocket))
            
        except Exception as e:
            logger.error(f"Error handling music remix: {e}")
            await self.send_error(websocket, str(e))
    
    async def handle_music_extend(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle music extension request via job queue"""
        try:
            connection_id = f"conn_{id(websocket)}"
            self.active_connections[connection_id] = websocket
            
            job_id = await self.queue.submit_job(
                job_type=JobType.MUSIC_EXTEND,
                job_data=data,
                user_id=data.get("user_id"),
                priority=data.get("priority", 0)
            )
            
            self.job_to_websocket[job_id] = connection_id
            
            await self.send_message(websocket, {
                "job_id": job_id,
                "status": "queued",
                "message": "Extension job submitted to queue"
            })
            
            asyncio.create_task(self.monitor_job_progress(job_id, websocket))
            
        except Exception as e:
            logger.error(f"Error handling music extension: {e}")
            await self.send_error(websocket, str(e))
    
    async def handle_vocal_extraction(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle vocal extraction request via job queue"""
        try:
            connection_id = f"conn_{id(websocket)}"
            self.active_connections[connection_id] = websocket
            
            job_id = await self.queue.submit_job(
                job_type=JobType.VOCAL_EXTRACTION,
                job_data=data,
                user_id=data.get("user_id"),
                priority=data.get("priority", 0)
            )
            
            self.job_to_websocket[job_id] = connection_id
            
            await self.send_message(websocket, {
                "job_id": job_id,
                "status": "queued",
                "message": "Vocal extraction job submitted to queue"
            })
            
            asyncio.create_task(self.monitor_job_progress(job_id, websocket))
            
        except Exception as e:
            logger.error(f"Error handling vocal extraction: {e}")
            await self.send_error(websocket, str(e))
    
    async def handle_sound_generation(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle sound generation request via job queue"""
        try:
            connection_id = f"conn_{id(websocket)}"
            self.active_connections[connection_id] = websocket
            
            job_id = await self.queue.submit_job(
                job_type=JobType.SOUND_GENERATION,
                job_data=data,
                user_id=data.get("user_id"),
                priority=data.get("priority", 0)
            )
            
            self.job_to_websocket[job_id] = connection_id
            
            await self.send_message(websocket, {
                "job_id": job_id,
                "status": "queued",
                "message": "Sound generation job submitted to queue"
            })
            
            asyncio.create_task(self.monitor_job_progress(job_id, websocket))
            
        except Exception as e:
            logger.error(f"Error handling sound generation: {e}")
            await self.send_error(websocket, str(e))
    
    async def monitor_job_progress(self, job_id: str, websocket: WebSocket):
        """Monitor job progress and send updates via WebSocket"""
        try:
            last_status = None
            last_progress = None
            
            while True:
                # Check if WebSocket is still connected
                if websocket.application_state != WebSocketState.CONNECTED:
                    logger.info(f"WebSocket disconnected for job {job_id}")
                    break
                
                # Get current job status
                status_info = await self.queue.get_job_status(job_id)
                if not status_info:
                    logger.warning(f"No status found for job {job_id}")
                    break
                
                current_status = status_info.get("status")
                current_progress = status_info.get("progress")
                current_message = status_info.get("message")
                
                # Send update if status or progress changed
                if (current_status != last_status or 
                    current_progress != last_progress):
                    
                    await self.send_message(websocket, {
                        "job_id": job_id,
                        "status": current_status,
                        "progress": float(current_progress) if current_progress else None,
                        "message": current_message
                    })
                    
                    last_status = current_status
                    last_progress = current_progress
                
                # Check if job is complete
                if current_status in ["completed", "failed", "cancelled"]:
                    if current_status == "completed":
                        # Get and send results
                        result = await self.queue.get_job_result(job_id)
                        if result:
                            await self.send_message(websocket, {
                                "job_id": job_id,
                                "status": "completed",
                                "data": result
                            })
                    break
                
                # Wait before next check
                await asyncio.sleep(2)  # More frequent updates than polling
                
        except Exception as e:
            logger.error(f"Error monitoring job {job_id}: {e}")
            await self.send_error(websocket, f"Error monitoring job: {str(e)}")
        finally:
            # Cleanup
            self.cleanup_connection(job_id)
    
    async def send_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """Send message via WebSocket"""
        try:
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": False,
                    **message
                })
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
    
    async def send_error(self, websocket: WebSocket, error_message: str):
        """Send error message via WebSocket"""
        try:
            if websocket.application_state == WebSocketState.CONNECTED:
                await websocket.send_json({
                    "error": True,
                    "message": error_message
                })
        except Exception as e:
            logger.error(f"Error sending WebSocket error: {e}")
    
    def cleanup_connection(self, job_id: str):
        """Clean up connection mappings"""
        if job_id in self.job_to_websocket:
            connection_id = self.job_to_websocket[job_id]
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
            del self.job_to_websocket[job_id]
    
    async def handle_job_cancellation(self, websocket: WebSocket, job_id: str):
        """Handle job cancellation request"""
        try:
            success = await self.queue.cancel_job(job_id)
            await self.send_message(websocket, {
                "job_id": job_id,
                "status": "cancelled" if success else "cancel_failed",
                "message": "Job cancelled" if success else "Job could not be cancelled"
            })
        except Exception as e:
            logger.error(f"Error cancelling job {job_id}: {e}")
            await self.send_error(websocket, f"Error cancelling job: {str(e)}")
    
    async def get_queue_status(self, websocket: WebSocket):
        """Get queue status information"""
        try:
            stats = await self.queue.get_queue_stats()
            await self.send_message(websocket, {
                "queue_stats": stats
            })
        except Exception as e:
            logger.error(f"Error getting queue status: {e}")
            await self.send_error(websocket, f"Error getting queue status: {str(e)}")
