"""
Adaptive polling strategy for different job types and stages
"""
import asyncio
import time
from typing import Dict, Any, <PERSON><PERSON>
from enum import Enum

class JobStage(Enum):
    INITIAL = "initial"
    PROCESSING = "processing" 
    FINALIZING = "finalizing"

class AdaptivePoller:
    """
    Intelligent polling that adapts intervals based on:
    - Job type (music generation vs vocal extraction)
    - Job stage (initial processing vs finalizing)
    - Historical completion times
    - Current system load
    """
    
    def __init__(self):
        # Base intervals for different job types (seconds)
        self.base_intervals = {
            "MUSIC_AI": {
                JobStage.INITIAL: 30,      # Wait longer initially
                JobStage.PROCESSING: 15,   # Medium frequency during processing
                JobStage.FINALIZING: 5     # Check frequently when close to completion
            },
            "REMIX": {
                JobStage.INITIAL: 20,
                JobStage.PROCESSING: 10,
                JobStage.FINALIZING: 5
            },
            "EXTEND": {
                JobStage.INITIAL: 25,
                JobStage.PROCESSING: 12,
                JobStage.FINALIZING: 5
            },
            "EXTRACTION": {
                JobStage.INITIAL: 15,      # Vocal extraction is typically faster
                JobStage.PROCESSING: 8,
                JobStage.FINALIZING: 3
            },
            "SOUND_GENERATOR": {
                JobStage.INITIAL: 10,      # Sound generation is usually quick
                JobStage.PROCESSING: 5,
                JobStage.FINALIZING: 2
            }
        }
        
        # Historical completion times (for learning)
        self.completion_history: Dict[str, list] = {}
        
        # System load factor (1.0 = normal, >1.0 = high load)
        self.load_factor = 1.0
    
    def get_polling_interval(
        self, 
        conversion_type: str, 
        elapsed_time: float,
        current_status: str = "PROCESSING"
    ) -> float:
        """
        Calculate optimal polling interval based on job characteristics
        """
        # Determine job stage based on elapsed time and status
        stage = self._determine_job_stage(conversion_type, elapsed_time, current_status)
        
        # Get base interval
        base_interval = self.base_intervals.get(conversion_type, {}).get(
            stage, 10  # Default fallback
        )
        
        # Apply load factor
        adjusted_interval = base_interval * self.load_factor
        
        # Apply exponential backoff for long-running jobs
        if elapsed_time > 300:  # 5 minutes
            backoff_factor = min(1.5, 1 + (elapsed_time - 300) / 600)
            adjusted_interval *= backoff_factor
        
        # Ensure reasonable bounds
        return max(2, min(adjusted_interval, 60))
    
    def _determine_job_stage(
        self, 
        conversion_type: str, 
        elapsed_time: float, 
        current_status: str
    ) -> JobStage:
        """Determine what stage the job is in"""
        
        # Status-based stage detection
        if current_status in ["QUEUED", "STARTING"]:
            return JobStage.INITIAL
        elif current_status in ["FINALIZING", "UPLOADING"]:
            return JobStage.FINALIZING
        
        # Time-based stage detection using historical data
        avg_completion_time = self._get_average_completion_time(conversion_type)
        
        if elapsed_time < avg_completion_time * 0.3:
            return JobStage.INITIAL
        elif elapsed_time > avg_completion_time * 0.8:
            return JobStage.FINALIZING
        else:
            return JobStage.PROCESSING
    
    def _get_average_completion_time(self, conversion_type: str) -> float:
        """Get average completion time for job type"""
        history = self.completion_history.get(conversion_type, [])
        if not history:
            # Default estimates based on job type
            defaults = {
                "MUSIC_AI": 180,      # 3 minutes
                "REMIX": 120,         # 2 minutes  
                "EXTEND": 150,        # 2.5 minutes
                "EXTRACTION": 60,     # 1 minute
                "SOUND_GENERATOR": 30 # 30 seconds
            }
            return defaults.get(conversion_type, 120)
        
        return sum(history) / len(history)
    
    def record_completion(self, conversion_type: str, completion_time: float):
        """Record job completion time for learning"""
        if conversion_type not in self.completion_history:
            self.completion_history[conversion_type] = []
        
        history = self.completion_history[conversion_type]
        history.append(completion_time)
        
        # Keep only recent history (last 50 jobs)
        if len(history) > 50:
            history.pop(0)
    
    def update_load_factor(self, queue_length: int, active_jobs: int):
        """Update system load factor based on queue metrics"""
        total_load = queue_length + active_jobs
        
        if total_load < 5:
            self.load_factor = 1.0      # Normal load
        elif total_load < 15:
            self.load_factor = 1.2      # Moderate load - poll less frequently
        elif total_load < 30:
            self.load_factor = 1.5      # High load
        else:
            self.load_factor = 2.0      # Very high load
    
    async def adaptive_wait_for_completion(
        self,
        client,
        conversion_type: str,
        task_id: str,
        max_wait_time: int = 600,
        progress_callback=None
    ) -> Dict[str, Any]:
        """
        Wait for completion using adaptive polling
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                details = client.get_conversion_details(
                    conversion_type=conversion_type,
                    task_id=task_id
                )
                
                # Check completion
                if details.get("status") == "COMPLETED":
                    completion_time = time.time() - start_time
                    self.record_completion(conversion_type, completion_time)
                    return details
                
                if details.get("status") == "ERROR":
                    raise Exception(f"Conversion {task_id} failed: {details}")
                
                # Calculate next polling interval
                elapsed_time = time.time() - start_time
                interval = self.get_polling_interval(
                    conversion_type, 
                    elapsed_time, 
                    details.get("status", "PROCESSING")
                )
                
                # Send progress update if callback provided
                if progress_callback:
                    progress = min(0.8, elapsed_time / max_wait_time)
                    await progress_callback(
                        progress, 
                        f"Status: {details.get('status', 'Processing')} (checking again in {interval}s)"
                    )
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                # On error, use exponential backoff
                await asyncio.sleep(min(30, 5 * (1.5 ** (time.time() - start_time) // 60)))
                raise
        
        raise TimeoutError(f"Conversion {task_id} did not complete within {max_wait_time} seconds")

# Global instance
adaptive_poller = AdaptivePoller()
