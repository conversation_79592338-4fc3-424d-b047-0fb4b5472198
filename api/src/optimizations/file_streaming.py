"""
File streaming and chunked upload optimization
"""
import asyncio
import base64
import tempfile
import os
from typing import AsyncGenerator, Optional
from fastapi import WebSocket, UploadFile
import aiofiles

class FileStreamHandler:
    """
    Handles large file uploads via chunked streaming instead of base64 in memory
    """
    
    def __init__(self, chunk_size: int = 1024 * 1024):  # 1MB chunks
        self.chunk_size = chunk_size
        self.temp_dir = tempfile.gettempdir()
    
    async def receive_chunked_file(
        self, 
        websocket: WebSocket, 
        expected_size: Optional[int] = None
    ) -> str:
        """
        Receive a file in chunks via WebSocket and save to temporary file
        
        Protocol:
        1. Client sends: {"type": "file_start", "filename": "audio.mp3", "size": 12345}
        2. Client sends: {"type": "file_chunk", "data": "base64_chunk", "chunk_id": 0}
        3. Client sends: {"type": "file_chunk", "data": "base64_chunk", "chunk_id": 1}
        4. Client sends: {"type": "file_end"}
        """
        temp_file_path = None
        
        try:
            # Wait for file start message
            start_msg = await websocket.receive_json()
            if start_msg.get("type") != "file_start":
                raise ValueError("Expected file_start message")
            
            filename = start_msg.get("filename", "upload.bin")
            file_size = start_msg.get("size")
            
            # Create temporary file
            temp_file_path = os.path.join(
                self.temp_dir, 
                f"upload_{id(websocket)}_{filename}"
            )
            
            received_size = 0
            chunk_id = 0
            
            async with aiofiles.open(temp_file_path, 'wb') as f:
                while True:
                    chunk_msg = await websocket.receive_json()
                    
                    if chunk_msg.get("type") == "file_end":
                        break
                    elif chunk_msg.get("type") == "file_chunk":
                        expected_chunk_id = chunk_msg.get("chunk_id")
                        if expected_chunk_id != chunk_id:
                            raise ValueError(f"Expected chunk {chunk_id}, got {expected_chunk_id}")
                        
                        chunk_data = base64.b64decode(chunk_msg.get("data", ""))
                        await f.write(chunk_data)
                        
                        received_size += len(chunk_data)
                        chunk_id += 1
                        
                        # Send progress update
                        if file_size:
                            progress = received_size / file_size
                            await websocket.send_json({
                                "type": "upload_progress",
                                "progress": progress,
                                "received_bytes": received_size
                            })
                    else:
                        raise ValueError(f"Unexpected message type: {chunk_msg.get('type')}")
            
            # Verify file size if provided
            if file_size and received_size != file_size:
                raise ValueError(f"File size mismatch: expected {file_size}, got {received_size}")
            
            await websocket.send_json({
                "type": "upload_complete",
                "temp_file_path": temp_file_path,
                "received_bytes": received_size
            })
            
            return temp_file_path
            
        except Exception as e:
            # Clean up on error
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
            raise
    
    async def convert_file_to_base64_stream(
        self, 
        file_path: str
    ) -> AsyncGenerator[str, None]:
        """
        Convert file to base64 in chunks to avoid memory issues
        """
        async with aiofiles.open(file_path, 'rb') as f:
            while True:
                chunk = await f.read(self.chunk_size)
                if not chunk:
                    break
                yield base64.b64encode(chunk).decode('utf-8')
    
    async def stream_file_to_api(
        self, 
        file_path: str, 
        api_endpoint: str,
        additional_data: dict = None
    ) -> dict:
        """
        Stream file to external API using multipart upload
        """
        import aiohttp
        
        additional_data = additional_data or {}
        
        async with aiohttp.ClientSession() as session:
            with aiofiles.open(file_path, 'rb') as f:
                data = aiohttp.FormData()
                
                # Add file
                data.add_field('audio_file', f, filename=os.path.basename(file_path))
                
                # Add additional fields
                for key, value in additional_data.items():
                    data.add_field(key, str(value))
                
                async with session.post(api_endpoint, data=data) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        raise Exception(f"API request failed: {response.status} - {await response.text()}")
    
    def cleanup_temp_file(self, file_path: str):
        """Clean up temporary file"""
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
        except Exception as e:
            # Log but don't raise - cleanup failures shouldn't break the flow
            import logging
            logging.warning(f"Failed to cleanup temp file {file_path}: {e}")

class MemoryEfficientBase64Handler:
    """
    Handle base64 operations more efficiently for large files
    """
    
    @staticmethod
    async def decode_base64_to_temp_file(base64_data: str) -> str:
        """
        Decode base64 data to temporary file without loading all into memory
        """
        temp_file_path = tempfile.mktemp()
        
        # Decode in chunks
        chunk_size = 1024 * 1024  # 1MB chunks
        
        async with aiofiles.open(temp_file_path, 'wb') as f:
            for i in range(0, len(base64_data), chunk_size):
                chunk = base64_data[i:i + chunk_size]
                decoded_chunk = base64.b64decode(chunk)
                await f.write(decoded_chunk)
        
        return temp_file_path
    
    @staticmethod
    async def encode_file_to_base64_chunked(file_path: str) -> str:
        """
        Encode file to base64 in memory-efficient way
        """
        result = []
        chunk_size = 1024 * 1024  # 1MB chunks
        
        async with aiofiles.open(file_path, 'rb') as f:
            while True:
                chunk = await f.read(chunk_size)
                if not chunk:
                    break
                result.append(base64.b64encode(chunk).decode('utf-8'))
        
        return ''.join(result)

# Global instances
file_stream_handler = FileStreamHandler()
memory_efficient_handler = MemoryEfficientBase64Handler()
