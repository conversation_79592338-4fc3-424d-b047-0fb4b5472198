import os
from elevenlabs import Eleven<PERSON>abs
import time
import tempfile
import wave


ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")
DEFAULT_VOICE = "JBFqnCBsd6RMkjVDRZzb"
MODEL = "eleven_turbo_v2_5"


class ElevenLabsClient:
    def __init__(self):
        self._client = ElevenLabs(api_key=ELEVENLABS_API_KEY)

    def design_voice(
        self,
        voice_description: str
    ):
        return self._client.text_to_voice.design(
            voice_description=voice_description,
            auto_generate_text=True
        )

    def generate_voice(
        self,
        voice_description: str,
        voice_id: str,
        voice_name: str = "Generated Voice"
    ) -> str:
        if len(voice_description) > 999:
            voice_description = voice_description[:999]

        return self._client.text_to_voice.create(
            voice_description=voice_description,
            generated_voice_id=voice_id,
            voice_name=voice_name
        )

    def generate_audio(
        self,
        text: str,
        voice: str = DEFAULT_VOICE,
    ) -> bytes:
        return self._client.text_to_speech.convert(
            voice_id=voice or DEFAULT_VOICE,
            text=text,
            model_id=MODEL,
            output_format="pcm_44100"
        )

    def translate_audio_from_english(
        self,
        audio: bytes,
        target_language: str
    ):
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            with wave.open(temp_file.name, 'wb') as wav_file:
                wav_file.setnchannels(1)
                wav_file.setsampwidth(2)
                wav_file.setframerate(44100)
                wav_file.writeframes(audio)

            with open(temp_file.name, 'rb') as audio_file:
                dubbed = self._client.dubbing.create(
                    file=audio_file, target_lang=target_language
                )
        os.unlink(temp_file.name)

        while True:
            status = self._client.dubbing.get(dubbed.dubbing_id).status
            if status == "dubbed":
                dubbed_file = self._client.dubbing.audio.get(dubbed.dubbing_id, target_language)
                return dubbed_file
                break
            elif status == "failed":
                raise Exception("Audio dubbing failed")
            elif status == "dubbing":
                print("Audio is still being dubbed...")
                time.sleep(5)
            else:
                raise Exception(f"Unknown status: {status}")
