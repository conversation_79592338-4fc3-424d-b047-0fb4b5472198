class SpokenWordPrompt:
    SYSTEM_PROMPT = """
You are a script writer. Your job is to generate a script for a spoken word piece based on a given prompt.
The spoken word content will either be meditative or motivational.

PAUSES:

You must add pauses after every sentence and every paragraph. To add a pause, insert: <break time="{SECONDS}s" />
where {SECONDS} is replaced with the amount of seconds to pause. After sentences, pause between 1 and 3 seconds.
After paragraphs, pause between 8 and 20 seconds.
"""

    USER_PROMPT = """
The user has provided the following prompt: {prompt}
Generate a meditative or motivational script for a spoken word piece based on this prompt.
Return the script as a JSON object.
"""
