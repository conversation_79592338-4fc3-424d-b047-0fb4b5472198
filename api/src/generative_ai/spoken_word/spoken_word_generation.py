import base64
from pydantic import BaseModel

from .prompt import Spoken<PERSON>ordPrompt
from .eleven_labs_client import ElevenLabsClient
from ..gemini_client import GeminiClient


class SpokenWordResponse(BaseModel):
    script: str


class SpokenWordGenerator:
    def __init__(self):
        self.gemini_client = GeminiClient()
        self.eleven_labs_client = ElevenLabsClient()

    def generate_script(self, prompt: str, model_creativity: float = 0.2):
        return self.gemini_client.generate_json_response(
            SpokenWordPrompt.USER_PROMPT.format(prompt=prompt),
            SpokenWordPrompt.SYSTEM_PROMPT,
            SpokenWordResponse,
            model_creativity
        )

    def generate_voice(self, voice_description: str):
        voices = self.eleven_labs_client.design_voice(voice_description)
        # NOTE: pick first one for now
        first_id = voices.previews[0].generated_voice_id

        return self.eleven_labs_client.generate_voice(voice_description, first_id)

    def generate_voice_over(self, script: str, voice_id: str, language_code: str = "en"):
        return self.eleven_labs_client.generate_audio(
            script,
            voice_id,
            # language_code
        )

    def translate_audio(self, audio_base64: str, target_language: str):
        audio_bytes = base64.b64decode(audio_base64)
        return self.eleven_labs_client.translate_audio_from_english(
            audio_bytes,
            target_language
        )
