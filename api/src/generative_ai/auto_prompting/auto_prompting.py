from pydantic import BaseModel
import enum

from .prompt import MusicGenAutoPrompt
from ..gemini_client import GeminiClient


class MusicGenre(enum.Enum):
    pass


class MusicPromptsResponse(BaseModel):
    main_prompt: str
    music_genre: str
    lyrics: str


class AutoPrompter:
    def __init__(self, user_prompt: str, model_creativity: float = 0.2):
        self.user_prompt = user_prompt
        self.model_creativity = model_creativity
        self.gemini_client = GeminiClient()

    def generate_prompt(self):
        user_prompt = MusicGenAutoPrompt.USER_PROMPT.format(prompt=self.user_prompt)
        response = self.gemini_client.generate_json_response(
            user_prompt,
            MusicGenAutoPrompt.SYSTEM_PROMPT,
            MusicPromptsResponse,
            self.model_creativity
        )

        return response
