import os
import json
from openai import AzureOpenAI
from pydantic import BaseModel
from typing import Dict, Any


OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_URL = "https://touchcast-napster-dev-eus2.openai.azure.com/"
OPENAI_API_VERSION = "2024-12-01-preview"
OPENAI_DEPLOYMENT_NAME = "standard-gpt-o3-2025-04-16"
DEFAULT_MODEL_NAME = "o3"


class OpenAIClient:
    def __init__(self):
        if not OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        print(OPENAI_API_KEY, OPENAI_URL, flush=True)
        self._client = AzureOpenAI(
            api_key=OPENAI_API_KEY,
            azure_endpoint=OPENAI_URL,
            api_version=OPENAI_API_VERSION,
            azure_deployment=OPENAI_DEPLOYMENT_NAME
        )

    def generate_json_response(
        self,
        user_prompt: str,
        system_prompt: str,
        response_schema: BaseModel,
        temperature: float = 0.2,
        model: str = DEFAULT_MODEL_NAME
    ) -> Dict[str, Any]:
        """
        Generate a structured JSON response using OpenAI's GPT models.

        Args:
            user_prompt: The user's input prompt
            system_prompt: System instructions for the model
            response_schema: Pydantic model defining the expected response structure
            temperature: Controls randomness (0.0 to 2.0)
            model: OpenAI model to use (default: gpt-4o)

        Returns:
            Dictionary containing the parsed JSON response

        Raises:
            Exception: If the API call fails or response parsing fails
        """
        try:
            # Get the JSON schema from the Pydantic model
            schema = response_schema.model_json_schema()

            response = self._client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                # NOTE: From endpoint:
                # temperature' does not support 0.2 with this model. Only the default (1) value is supported
                temperature=1,
                response_format={
                    "type": "json_schema",
                    "json_schema": {
                        "name": "response",
                        "schema": schema
                    }
                }
            )

            # Extract the content from the response
            content = response.choices[0].message.content

            # Parse and validate the JSON response
            parsed_response = json.loads(content)

            # Validate against the schema
            validated_response = response_schema(**parsed_response)

            return validated_response.model_dump()

        except json.JSONDecodeError as e:
            raise Exception(f"Failed to parse JSON response: {e}")
        except Exception as e:
            raise Exception(f"OpenAI API call failed: {e}")

    def generate_text_response(
        self,
        user_prompt: str,
        system_prompt: str,
        temperature: float = 0.2,
        model: str = DEFAULT_MODEL_NAME,
        max_tokens: int = 4000
    ) -> str:
        """
        Generate a simple text response using OpenAI's GPT models.

        Args:
            user_prompt: The user's input prompt
            system_prompt: System instructions for the model
            temperature: Controls randomness (0.0 to 2.0)
            model: OpenAI model to use (default: gpt-4o)
            max_tokens: Maximum tokens in the response

        Returns:
            String containing the model's response

        Raises:
            Exception: If the API call fails
        """
        try:
            response = self._client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                # NOTE: From endpoint:
                # temperature' does not support 0.2 with this model. Only the default (1) value is supported
                temperature=1,
                max_tokens=max_tokens
            )

            return response.choices[0].message.content

        except Exception as e:
            raise Exception(f"OpenAI API call failed: {e}")
