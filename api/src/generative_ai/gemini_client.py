import os
import json
from google import genai
from google.genai import types
from pydantic import BaseModel


GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")


class GeminiClient:
    def __init__(self):
        self._client = genai.Client(api_key=GEMINI_API_KEY)

    def generate_json_response(
        self,
        user_prompt: str,
        system_prompt: str,
        response_schema: BaseModel,
        temperature: float = 0.2
    ) -> dict:
        response = self._client.models.generate_content(
            model="gemini-2.5-flash",
            contents=user_prompt,
            config=types.GenerateContentConfig(
                response_mime_type="application/json",
                system_instruction=system_prompt,
                response_schema=response_schema,
                temperature=temperature
            )
        )

        return json.loads(response.text)

    def _generate_image_imagen(self, user_prompt: str) -> bytes:
        response = self._client.models.generate_images(
            model='imagen-4.0-generate-001',
            prompt=user_prompt,
            config=types.GenerateImagesConfig(
                number_of_images=1,
            )
        )
        for generated_image in response.generated_images:
            return generated_image.image.image_bytes

        return None

    def _generate_image_gemini(
        self,
        user_prompt: str,
        temperature: float = 0.2,
        model: str = "gemini-2.0-flash-preview-image-generation"
    ) -> bytes:
        response = self._client.models.generate_content(
            model="gemini-2.0-flash-preview-image-generation",
            contents=user_prompt,
            config=types.GenerateContentConfig(
                response_modalities=['TEXT', 'IMAGE'],
                temperature=temperature
            )
        )

        for part in response.candidates[0].content.parts:
            if part.inline_data is not None:
                return part.inline_data.data

        return None

    def generate_image(
        self,
        user_prompt: str,
        temperature: float = 0.2,
        model: str = "imagen"
    ) -> bytes:
        if model == "imagen":
            return self._generate_image_imagen(user_prompt)
        elif model == "gemini-2.5":
            return self._generate_image_gemini(
                user_prompt,
                temperature,
                "gemini-2.5-flash-image-preview"
            )
        elif model == "gemini-2.0":
            return self._generate_image_gemini(
                user_prompt,
                temperature,
                "gemini-2.0-flash-preview-image-generation"
            )
        else:
            raise ValueError(f"Invalid image generation backend: {model}")
