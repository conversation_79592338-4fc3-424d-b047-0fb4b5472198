from .prompt import AlbumImageGenPrompt
from ..gemini_client import GeminiClient


class AlbumImageGenerator:
    def __init__(
        self,
        user_prompt: str,
        model_creativity: float = 0.2,
        model: str = "imagen"
    ):
        self.user_prompt = user_prompt
        self.model_creativity = model_creativity
        self.gemini_client = GeminiClient()

    def generate_album_cover(self):
        user_prompt = AlbumImageGenPrompt.USER_PROMPT.format(song_prompt=self.user_prompt)
        response = self.gemini_client.generate_image(
            user_prompt,
            self.model_creativity
        )

        return response
