from pydantic import BaseModel, Field
from typing import Optional, List
import enum

from .prompt import TranslationPrompt
from ..openai_client import OpenAIClient


class SupportedLanguage(enum.Enum):
    """Supported languages for translation"""
    SPANISH = "Spanish"
    FRENCH = "French"
    GERMAN = "German"
    ITALIAN = "Italian"
    PORTUGUESE = "Portuguese"
    RUSSIAN = "Russian"
    CHINESE_SIMPLIFIED = "Chinese (Simplified)"
    CHINESE_TRADITIONAL = "Chinese (Traditional)"
    JAPANESE = "Japanese"
    KOREAN = "Korean"
    ARABIC = "Arabic"
    HINDI = "Hindi"
    DUTCH = "Dutch"
    SWEDISH = "Swedish"
    NORWEGIAN = "Norwegian"
    DANISH = "Danish"
    FINNISH = "Finnish"
    POLISH = "Polish"
    CZECH = "Czech"
    HUNGARIAN = "Hungarian"
    ROMANIAN = "Romanian"
    BULGARIAN = "Bulgarian"
    CROATIAN = "Croatian"
    SERBIAN = "Serbian"
    SLOVAK = "Slovak"
    SLOVENIAN = "Slovenian"
    ESTONIAN = "Estonian"
    LATVIAN = "Latvian"
    LITHUANIAN = "Lithuanian"
    GREEK = "Greek"
    TURKISH = "Turkish"
    HEBREW = "Hebrew"
    THAI = "Thai"
    VIETNAMESE = "Vietnamese"
    INDONESIAN = "Indonesian"
    MALAY = "Malay"
    FILIPINO = "Filipino"
    SWAHILI = "Swahili"
    ENGLISH = "English"


class TranslationQuality(enum.Enum):
    """Translation quality levels"""
    STANDARD = "standard"
    HIGH = "high"
    CREATIVE = "creative"


class TranslationResponse(BaseModel):
    """Response model for translation results"""
    translated_text: str = Field(description="The translated text")
    source_language: str = Field(description="Source language used for translation")
    target_language: str = Field(description="Target language for translation")
    confidence_score: Optional[float] = Field(
        description="Confidence score for the translation (0.0 to 1.0)",
        ge=0.0,
        le=1.0,
        default=None
    )


class TranslationService:
    """Service for translating text using OpenAI's GPT models"""

    def __init__(self):
        self.openai_client = OpenAIClient()

    def translate_text(
        self,
        text: str,
        target_language: str,
        source_language: Optional[str] = None,
        context: Optional[str] = None,
        quality: TranslationQuality = TranslationQuality.STANDARD,
        model_creativity: float = 0.2
    ) -> TranslationResponse:
        """
        Translate text from one language to another.

        Args:
            text: Text to translate
            target_language: Target language for translation
            source_language: Source language (optional, defaults to English if not provided)
            context: Additional context to help with translation (optional)
            quality: Translation quality level
            model_creativity: Model creativity/temperature (0.0 to 1.0)

        Returns:
            TranslationResponse containing the translation and metadata

        Raises:
            ValueError: If the target language is not supported
            Exception: If translation fails
        """
        if not self._is_supported_language(target_language):
            raise ValueError(f"Unsupported target language: {target_language}")

        if source_language is None:
            source_language = "English"

        temperature = self._get_temperature_for_quality(quality, model_creativity)

        user_prompt = TranslationPrompt.format_user_prompt(
            text=text,
            target_language=target_language,
            source_language=source_language,
            context=context
        )

        # Generate translation using OpenAI
        try:
            response = self.openai_client.generate_json_response(
                user_prompt=user_prompt,
                system_prompt=TranslationPrompt.SYSTEM_PROMPT,
                response_schema=TranslationResponse,
                temperature=temperature
            )

            return TranslationResponse(**response)

        except Exception as e:
            raise Exception(f"Translation failed: {e}")

    def get_supported_languages(self) -> List[str]:
        """
        Get a list of supported languages.

        Returns:
            List of supported language names
        """
        return [lang.value for lang in SupportedLanguage]

    def _is_supported_language(self, language: str) -> bool:
        """Check if a language is supported"""
        supported_languages = [lang.value.lower() for lang in SupportedLanguage]
        return language.lower() in supported_languages

    def _get_temperature_for_quality(self, quality: TranslationQuality, base_creativity: float) -> float:
        """Get appropriate temperature based on quality setting"""
        if quality == TranslationQuality.STANDARD:
            return min(base_creativity, 0.3)
        elif quality == TranslationQuality.HIGH:
            return min(base_creativity, 0.1)
        elif quality == TranslationQuality.CREATIVE:
            return min(base_creativity + 0.2, 0.7)
        else:
            return base_creativity
