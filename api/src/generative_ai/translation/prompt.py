class TranslationPrompt:
    SYSTEM_PROMPT = """
You are a professional translator with expertise in multiple languages and cultural nuances. Your job is to provide accurate, contextually appropriate translations that preserve the original meaning, tone, and style.

Key Requirements:
- Maintain the original tone and style (formal, informal, technical, creative, etc.)
- Preserve cultural context and idiomatic expressions when possible
- Provide natural-sounding translations that read fluently in the target language
- Handle technical terms, proper nouns, and specialized vocabulary appropriately
- Detect and preserve formatting, punctuation, and structure
- For ambiguous terms, choose the most contextually appropriate translation
- Maintain consistency in terminology throughout the text

Quality Standards:
- Prioritize accuracy and naturalness over literal translation
- Consider regional variations and target audience
- Preserve the original intent and emotional tone
- Handle colloquialisms and slang appropriately for the target culture

Your response should be a JSON object containing the translation details.
"""

    USER_PROMPT = """
Please translate the following text:

Source Text: {text}
Target Language: {target_language}
{source_language_instruction}
{context_instruction}

Provide a high-quality translation that maintains the original meaning, tone, and style while being natural and fluent in the target language.
"""

    @classmethod
    def format_user_prompt(
        cls,
        text: str,
        target_language: str,
        source_language: str,
        context: str = None
    ) -> str:
        """
        Format the user prompt with the provided parameters.

        Args:
            text: Text to translate
            target_language: Target language for translation
            source_language: Source language (required, defaults to English in service)
            context: Additional context for translation (optional)

        Returns:
            Formatted user prompt string
        """
        # Source language is now always provided (defaults to English in service)
        source_language_instruction = f"Source Language: {source_language}"

        # Handle context instruction
        if context:
            context_instruction = f"Context/Notes: {context}"
        else:
            context_instruction = ""

        return cls.USER_PROMPT.format(
            text=text,
            target_language=target_language,
            source_language_instruction=source_language_instruction,
            context_instruction=context_instruction
        )
