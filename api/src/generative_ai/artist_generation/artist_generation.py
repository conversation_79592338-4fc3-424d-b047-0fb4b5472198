import uuid
from pydantic import BaseModel, <PERSON>
from typing import List, Optional
import enum

from .prompt import ArtistGenerationPrompt
from ..gemini_client import GeminiClient


class ArtistType(enum.Enum):
    solo_artist = "Solo artist"
    duo = "Duo"
    band = "Band"


class ArtistGender(enum.Enum):
    male = "Male"
    female = "Female"
    non_binary = "Non-binary"
    other = "Other"


class BodyBuild(enum.Enum):
    slim = "Slim"
    athletic = "Athletic"
    muscular = "Muscular"
    average = "Average"
    curvy = "Curvy"
    stocky = "Stocky"


class RelationshipStatus(enum.Enum):
    single = "Single"
    dating = "Dating"
    engaged = "Engaged"
    married = "Married"
    divorced = "Divorced"
    complicated = "It's complicated"


class ArtistGenerationResponse(BaseModel):
    # Basic Identity
    artist_alias: str = <PERSON>(description="Main stage name")
    alias: str = <PERSON>(description="Short nickname or tag")
    given_name: str = Field(description="Birth/legal name (fictional)")
    type: ArtistType = Field(description="Solo artist, duo, or band")
    gender: ArtistGender = Field(description="Gender identity")
    age: int = Field(ge=16, le=80, description="Age in years")
    origin_city: str = Field(description="Where they are from (must be non-fictional)")

    # Music Career
    amount_of_albums_released: int = Field(ge=1, le=10, description="Number of albums released")
    album_names: List[str] = Field(min_length=1, max_length=10, description="List of album names. Should correspond to amount_of_albums_released")
    genre: str = Field(description="Main musical genre(s)")
    sub_genre: str = Field(description="Subgenres")
    role: str = Field(description="Role - for solo acts always 'Main Act'")
    language: str = Field(description="Main vocal/performance language(s) - always include English")
    voice: str = Field(description="Vocal style and tone description including at least information about Timbre and Range/register")

    # Background & Story
    background: str = Field(min_length=1250, max_length=1500, description="Artist backstory")
    favorite_artists: str = Field(description="Musical inspirations and influences")
    narrative_arc: str = Field(min_length=500, max_length=750, description="Short artist journey arc describing career stage")
    iconic_symbol_logo: str = Field(min_length=200, max_length=250, description="Description of artist/band logo or symbol")

    # Personality & Brand
    personality: str = Field(min_length=500, max_length=1000, description="Character traits")
    vibe: str = Field(description="General artistic/energetic vibe")
    catchphrase: List[str] = Field(min_length=5, max_length=5, description="5 things they'd be known for saying")
    keywords: str = Field(description="Searchable brand identity terms")
    label_persona_market_positioning: str = Field(description="How RhapsodyAI positions them")
    fanbase_identity: str = Field(description="What their fans are called or how community identifies")

    # Physical Appearance
    appearance: str = Field(description="Overall look description")
    body_build: BodyBuild = Field(description="Physical build type")
    skin_tone: str = Field(description="Description of complexion")
    hair: str = Field(description="Hair style, length, color")
    style: str = Field(description="Clothing/fashion style")
    face: str = Field(description="Facial features description")
    accessories: str = Field(description="Signature wearable items")

    # Performance & Sound
    stage_presence_live_performance_style: str = Field(description="Live performance style")
    signature_sound_trait: str = Field(description="The sonic element that makes them instantly recognizable")
    performance_venues_circuits: str = Field(description="Types of stages they play")
    social_cultural_impact: Optional[str] = Field(description="What they stand for in society (optional)")

    # Bio & Personal Life
    artist_bio_short: str = Field(max_length=250, description="Short biography")
    hobbies: List[str] = Field(min_length=5, max_length=5, description="5 hobbies")
    animals: Optional[str] = Field(description="Pets or animal connections (optional)")
    random_activities: List[str] = Field(min_length=10, max_length=15, description="Daily life activities")
    relationship_status: RelationshipStatus = Field(description="Current relationship status")
    holiday_preference: List[str] = Field(min_length=5, max_length=5, description="5 preferred destinations")
    sports: Optional[str] = Field(description="Sports activities (if any)")
    food: str = Field(description="Food preferences and habits")
    side_hustles: Optional[str] = Field(description="Side businesses or activities (optional)")
    base_city: str = Field(description="Current city of residence (must be actual city)")

    # Visual & Creative Direction
    possible_backgrounds: List[str] = Field(min_length=5, max_length=5, description="5 background descriptions for use with artist")
    stage_design: List[str] = Field(min_length=3, max_length=3, description="3 stage design descriptions")
    action_shot_scene: List[str] = Field(min_length=5, max_length=5, description="5 action scene descriptions")
    press_kit_image: List[str] = Field(min_length=3, max_length=3, description="3 press kit image descriptions")
    action_stage: List[str] = Field(min_length=3, max_length=3, description="3 on-stage action descriptions")
    artwork_description_style: List[str] = Field(min_length=2, max_length=2, description="2 overall design style descriptions")


class ArtistGenerator:
    def __init__(
        self,
        user_prompt: str,
        model_creativity: float = 0.2,
        agent_description: str = None
    ):
        self.user_prompt = user_prompt
        self.model_creativity = model_creativity
        self.gemini_client = GeminiClient()
        self.agent_description = agent_description or ArtistGenerationPrompt.DEFAULT_AGENT_DESCRIPTION

    def generate_prompt(self):
        user_prompt = ArtistGenerationPrompt.USER_PROMPT.format(prompt=self.user_prompt)
        response = self.gemini_client.generate_json_response(
            user_prompt,
            ArtistGenerationPrompt.SYSTEM_PROMPT.format(
                agent_description=self.agent_description
            ),
            ArtistGenerationResponse,
            self.model_creativity
        )
        response["id"] = str(uuid.uuid4())

        return response
