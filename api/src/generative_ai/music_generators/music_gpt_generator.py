import base64
import json
from fastapi import WebSocket
from fastapi.websockets import WebSocketState
import requests
from requests.adapters import HTT<PERSON><PERSON>pter
from urllib3.util.retry import Retry
import time
import asyncio
import os
from typing import Optional, Dict, Any


API_KEY = os.getenv("MUSIC_GPT_API_KEY")

# Timeout configurations (in seconds)
MUSICGPT_TIMEOUTS = {
    "connection": int(os.getenv("MUSICGPT_CONNECTION_TIMEOUT", "30")),    # 30 seconds for connection
    "read": int(os.getenv("MUSICGPT_READ_TIMEOUT", "300")),              # 5 minutes for read
    "generation": int(os.getenv("MUSICGPT_GENERATION_TIMEOUT", "300")),  # 5 minutes for generation requests
    "status_check": int(os.getenv("MUSICGPT_STATUS_TIMEOUT", "30")),     # 30 seconds for status checks
}


class MusicGPTClient:
    """Client for interacting with the MusicGPT API."""

    def __init__(self, ws_conn: WebSocket = None, logger=None):
        """
        Initialize the MusicGPT client.

        Args:
            ws_conn: WebSocket connection for real-time updates
            logger: Logger instance for debugging
        """
        self.ws_conn = ws_conn
        self.logger = logger
        self.base_url = "https://api.musicgpt.com/api/public/v1"
        self.headers = {
            "Authorization": API_KEY
        }

        # Configure HTTP session with retries and timeouts
        self.session = requests.Session()

        # Configure retry strategy for retryable errors only (simplified)
        retry_strategy = Retry(
            total=2,  # Reduced from 5 - just handle immediate network issues
            backoff_factor=1,  # Exponential backoff: 1, 2 seconds
            status_forcelist=[429, 500, 502, 503, 504],  # Only retry on these status codes
            allowed_methods=["POST", "GET"],  # Allow retries on POST and GET
            respect_retry_after_header=True,  # Respect Retry-After header for 429
            raise_on_status=False  # Don't raise exception on retry exhaustion
        )

        # Mount adapter with retry strategy
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

    async def send_ws_message(self, status: str, data: Dict[str, Any]) -> None:
        if self.ws_conn is not None:
            ws_state = self.ws_conn.application_state
            if ws_state == WebSocketState.CONNECTED:
                self.logger.info(f"Sending WS Message: {status=}: {data=}")
                await self.ws_conn.send_json({
                    "error": False,
                    "status": status,
                    "data": data
                })
            else:
                self.logger.warning(f"WebSocket not connected (state: {ws_state}), cannot send message: {status=}")
                raise Exception(f"WebSocket not connected (state: {ws_state}), cannot send message: {status=}")
        else:
            self.logger.warning(f"WebSocket connection is None, cannot send message: {status=}")

    def generate_music(
        self,
        prompt: str,
        music_style: Optional[str] = None,
        lyrics: Optional[str] = None,
        make_instrumental: bool = False,
        vocal_only: bool = False,
        voice_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate music using AI.

        Args:
            prompt: Text prompt describing the music to generate
            music_style: Musical style (e.g., "Pop", "Rock", "Jazz")
            lyrics: Custom lyrics for the song
            make_instrumental: If True, generate instrumental only
            vocal_only: If True, generate vocals only
            voice_id: Specific voice to use (e.g., "Drake")

        Returns:
            Dictionary containing task_id, conversion_ids, and other metadata
        """
        url = f"{self.base_url}/MusicAI"

        payload = {
            "prompt": prompt
        }

        # Add optional parameters
        if music_style:
            payload["music_style"] = music_style
        if lyrics:
            payload["lyrics"] = lyrics
        if make_instrumental:
            payload["make_instrumental"] = True
        if vocal_only:
            payload["vocal_only"] = True
        if voice_id:
            payload["voice_id"] = voice_id

        self.logger.info(f"Generating Music with Payload: {payload}")
        timeout = (MUSICGPT_TIMEOUTS["connection"], MUSICGPT_TIMEOUTS["generation"])
        response = self.session.post(url, json=payload, headers=self.headers, timeout=timeout)
        if response.ok:
            return response.json()

        raise Exception(f"Failed to generate music: {response.text}")

    def generate_sound(
        self,
        prompt: str,
        audio_length_seconds: int
    ) -> Dict[str, Any]:
        """
        Generate music using AI.

        Args:
            prompt: Text prompt describing the music to generate
            audio_length_seconds: Length of the generated audio in seconds

        Returns:
            Dictionary containing task_id, conversion_ids, and other metadata
        """
        url = f"{self.base_url}/sound_generator"

        payload = {
            "prompt": prompt
        }

        # Add optional parameters
        if audio_length_seconds:
            payload["audio_length"] = audio_length_seconds

        self.logger.info(f"Generating Sound with Payload: {payload}")
        # NOTE: this endpoint specifically will not accept json payload
        # https://docs.musicgpt.com/api-documentation/endpoint/soundgenerator?playground=open
        timeout = (MUSICGPT_TIMEOUTS["connection"], MUSICGPT_TIMEOUTS["generation"])
        response = self.session.post(url, data=payload, headers=self.headers, timeout=timeout)
        if response.ok:
            return response.json()

        raise Exception(f"Failed to generate sound: {response.text}")

    def extend_music(
        self,
        audio_base64: str,
        extend_after_seconds: int,
        prompt: Optional[str] = None,
        lyrics: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Extending music using AI.

        Args:
            audio_base64: Base64 encoded audio data
            extend_after_seconds: Extend the music after this many seconds
            prompt: Text prompt describing the music to generate
            lyrics: Custom lyrics for the song

        Returns:
            Dictionary containing task_id, conversion_ids, and other metadata
        """
        url = f"{self.base_url}/extend"

        self.logger.info(f"Decoding base64 audio data (length: {len(audio_base64)} chars)")
        audio_data = base64.b64decode(audio_base64)
        self.logger.info(f"Decoded audio data size: {len(audio_data)} bytes")

        files = {
            "audio_file": ("audio.mp3", audio_data)
        }

        data = {
            "extend_after": extend_after_seconds,
        }

        if prompt:
            data["prompt"] = prompt
        if lyrics:
            data["lyrics"] = lyrics

        self.logger.info(f"Extending Music with Data: {data}")

        response = requests.post(
            url,
            files=files,
            data=data,
            headers=self.headers
        )
        if response.ok:
            return response.json()

        raise Exception(f"Failed to extend music: {response.text}")

    def remix_music(
        self,
        audio_base64: str,
        prompt: str,
        lyrics: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Extending music using AI.

        Args:
            audio_base64: Base64 encoded audio data
            prompt: Text prompt describing the music to generate
            lyrics: Custom lyrics for the song

        Returns:
            Dictionary containing task_id, conversion_ids, and other metadata
        """
        url = f"{self.base_url}/Remix"

        self.logger.info(f"Decoding base64 audio data (length: {len(audio_base64)} chars)")
        audio_data = base64.b64decode(audio_base64)
        self.logger.info(f"Decoded audio data size: {len(audio_data)} bytes")

        files = {
            "audio_file": ("audio.mp3", audio_data)
        }

        data = {
            "prompt": prompt
        }

        if lyrics:
            data["lyrics"] = lyrics

        self.logger.info(f"Remixing Music with Data: {data}")
        response = requests.post(
            url,
            files=files,
            data=data,
            headers=self.headers
        )
        if response.ok:
            return response.json()

        raise Exception(f"Failed to remix music: {response.text}")

    def extract_vocals(
        self,
        audio_base64: str,
        preprocessing_options: Optional[list[str]] = None
    ) -> Dict[str, Any]:
        """
        Extract vocals from music.

        Args:
            audio_base64: Base64 encoded audio data
            preprocessing_options: Optional preprocessing options

        Returns:
            Dictionary containing task_id, conversion_ids, and other metadata
        """
        url = f"{self.base_url}/Extraction"
        audio_data = base64.b64decode(audio_base64)
        files = {
            "audio_file": ("audio.mp3", audio_data)
        }
        data = {
            "stems": json.dumps(["vocals", "instrumental"])
        }
        if preprocessing_options:
            data["preprocessing_options"] = json.dumps(preprocessing_options)

        self.logger.info(f"Extracting Vocals from Music with Data: {data}")
        response = requests.post(
            url,
            files=files,
            data=data,
            headers=self.headers
        )
        if response.ok:
            return response.json()

        raise Exception(f"Failed to extract vocals: {response.text}")

    def get_conversion_details(
        self,
        conversion_type: str,
        task_id: Optional[str] = None,
        conversion_id: Optional[str] = None
    ) -> Dict[str, Any]:
        if not task_id and not conversion_id:
            raise ValueError("Must provide either task_id or conversion_id")

        if task_id and conversion_id:
            raise ValueError("Provide only task_id OR conversion_id, not both")

        url = f"{self.base_url}/byId?conversionType={conversion_type}"
        if task_id:
            url += f"&task_id={task_id}"
        else:
            url += f"&conversion_id={conversion_id}"

        self.logger.info(f"Getting Conversion Details: {url}")
        timeout = (MUSICGPT_TIMEOUTS["connection"], MUSICGPT_TIMEOUTS["status_check"])
        response = self.session.get(
            url,
            headers=self.headers,
            timeout=timeout
        )
        if response.ok:
            data = response.json()
            if data.get("success"):
                return data["conversion"]
        else:
            self.logger.error(f"Response content: {response.text}")

        raise Exception(f"Failed to get conversion details: {response.text}")

    async def wait_for_completion(
        self,
        conversion_type: str,
        task_id: Optional[str] = None,
        conversion_id: Optional[str] = None,
        max_wait_time: int = 600,
        check_interval: int = 10,
    ) -> Dict[str, Any]:
        self.logger.info(f"Waiting for completion of {conversion_type} with {task_id=}, {conversion_id=}")
        start_time = time.time()
        identifier = task_id or conversion_id

        while time.time() - start_time < max_wait_time:
            details = self.get_conversion_details(
                conversion_type=conversion_type,
                task_id=task_id,
                conversion_id=conversion_id
            )

            # Check if conversion is complete
            if details.get("status") == "COMPLETED":
                self.logger.info(f"✓ Conversion {identifier} completed!")
                return details

            if details.get("status") == "ERROR":
                raise Exception(f"Conversion {identifier} failed: {details}")

            # Check status
            status = details.get("status", "UNKNOWN")
            self.logger.info(f"Status: {status} - Waiting {check_interval} seconds...")

            await self.send_ws_message("generating music", {"status": status})
            await asyncio.sleep(check_interval)

        raise TimeoutError(f"Conversion {identifier} did not complete within {max_wait_time} seconds")

    async def generate_music_and_send_data_over_ws(
        self,
        generation_data: Dict[str, Any]
    ) -> None:
        if "prompt" not in generation_data:
            raise ValueError("prompt is required")

        self.logger.info(f"Initiating Music Generation with: {generation_data}")
        await self.send_ws_message(
            "initiating music generation",
            generation_data
        )

        result = self.generate_music(
            prompt=generation_data["prompt"],
            music_style=generation_data.get("music_style"),
            lyrics=generation_data.get("lyrics"),
            make_instrumental=generation_data.get("make_instrumental", False),
            vocal_only=generation_data.get("vocal_only", False),
            voice_id=generation_data.get("voice_id")
        )
        task_id = result.get('task_id')
        await asyncio.sleep(30)
        details = await self.wait_for_completion(
            conversion_type="MUSIC_AI",
            task_id=task_id,
        )
        self.logger.info(f"Generating Music Details: {details}")
        await self.send_ws_message(
            "completed",
            {
                "song_1_url": details.get("conversion_path_1"),
                "song_1_wav_url": details.get("conversion_path_wav_1"),
                "song_1_name": details.get("title_1"),
                "song_1_lyrics": details.get("lyrics_1"),
                "song_1_length": details.get("conversion_duration_1"),
                "song_2_url": details.get("conversion_path_2"),
                "song_2_wav_url": details.get("conversion_path_wav_2"),
                "song_2_name": details.get("title_2"),
                "song_2_lyrics": details.get("lyrics_2"),
                "song_2_length": details.get("conversion_duration_2"),
                "album_cover_url": details.get("album_cover_path"),
                "album_cover_thumbnail_url": details.get("album_cover_thumbnail"),
                "music_gpt_task_id": task_id
            }
        )

    async def generate_sound_and_send_data_over_ws(
        self,
        generation_data: Dict[str, Any]
    ) -> None:
        if "prompt" not in generation_data:
            raise ValueError("prompt is required")

        self.logger.info(f"Initiating Music Generation with: {generation_data}")
        await self.send_ws_message(
            "initiating music generation",
            generation_data
        )

        result = self.generate_sound(
            prompt=generation_data["prompt"],
            audio_length_seconds=generation_data.get("audio_length_seconds"),
        )
        task_id = result.get('task_id')
        await asyncio.sleep(15)
        details = await self.wait_for_completion(
            conversion_type="SOUND_GENERATOR",
            task_id=task_id,
        )
        self.logger.info(f"Generating Music Details: {details}")
        await self.send_ws_message(
            "completed",
            {
                "sound_url": details.get("conversion_path"),
                "sound_wav_url": details.get("conversion_path_wav"),
                "sound_length": details.get("conversion_duration"),
                "music_gpt_task_id": task_id
            }
        )

    async def extend_music_and_send_data_over_ws(
        self,
        extend_data: Dict[str, Any]
    ) -> None:
        required_params = ("audio_base64", "extend_after_seconds")
        if any(param not in extend_data for param in required_params):
            raise ValueError(f"missing required params. please pass at least: {required_params}")

        await self.send_ws_message(
            "initiating music extension",
            {
                "extend_after_seconds": extend_data["extend_after_seconds"],
                "prompt": extend_data.get("prompt"),
                "lyrics": extend_data.get("lyrics")
            }
        )

        result = self.extend_music(
            audio_base64=extend_data["audio_base64"],
            extend_after_seconds=int(extend_data["extend_after_seconds"]),
            prompt=extend_data.get("prompt"),
            lyrics=extend_data.get("lyrics")
        )
        self.logger.info(f"Extend Music Result: {result}")
        task_id = result.get('task_id')
        if not task_id:
            raise ValueError(f"No task_id found in extend_music result: {result}")
        self.logger.info(f"Waiting for completion of EXTEND with {task_id=}")
        await asyncio.sleep(30)
        details = await self.wait_for_completion(
            conversion_type="EXTEND",
            task_id=task_id,
        )
        self.logger.info(f"Extend Music Details: {details}")
        await self.send_ws_message(
            "completed",
            {
                "song_1_url": details.get("conversion_path_1"),
                "song_1_wav_url": details.get("conversion_path_wav_1"),
                "song_1_length": details.get("conversion_duration_1"),
                "song_2_url": details.get("conversion_path_2"),
                "song_2_wav_url": details.get("conversion_path_wav_2"),
                "song_2_length": details.get("conversion_duration_2"),
                "music_gpt_task_id": task_id
            }
        )

    async def remix_music_and_send_data_over_ws(
        self,
        extend_data: Dict[str, Any]
    ) -> None:
        required_params = ("audio_base64", "prompt")
        if any(param not in extend_data for param in required_params):
            raise ValueError(f"missing required params. please pass at least: {required_params}")

        await self.send_ws_message(
            "initiating music remixing",
            {
                "prompt": extend_data["prompt"],
                "lyrics": extend_data.get("lyrics")
            }
        )

        result = self.remix_music(
            audio_base64=extend_data["audio_base64"],
            prompt=extend_data["prompt"],
            lyrics=extend_data.get("lyrics")
        )
        self.logger.info(f"Remixing Music Result: {result}")
        task_id = result.get('task_id')
        if not task_id:
            raise ValueError(f"No task_id found in remix_music result: {result}")

        self.logger.info(f"Waiting for completion of REMIX with {task_id=}")
        await asyncio.sleep(30)
        details = await self.wait_for_completion(
            conversion_type="REMIX",
            task_id=task_id,
        )
        self.logger.info(f"Remixing Music Details: {details}")
        await self.send_ws_message(
            "completed",
            {
                "song_1_url": details.get("conversion_path_1"),
                "song_1_wav_url": details.get("conversion_path_wav_1"),
                "song_1_length": details.get("conversion_duration_1"),
                "song_2_url": details.get("conversion_path_2"),
                "song_2_wav_url": details.get("conversion_path_wav_2"),
                "song_2_length": details.get("conversion_duration_2"),
                "music_gpt_task_id": task_id
            }
        )

    async def extract_vocals_and_send_data_over_ws(
        self,
        extract_data: Dict[str, Any]
    ) -> None:
        required_params = ("audio_base64",)
        if any(param not in extract_data for param in required_params):
            raise ValueError(f"missing required params. please pass at least: {required_params}")

        await self.send_ws_message(
            "initiating vocal extraction",
            {
                "preprocessing_options": extract_data.get("preprocessing_options")
            }
        )

        result = self.extract_vocals(
            audio_base64=extract_data["audio_base64"],
            preprocessing_options=extract_data.get("preprocessing_options")
        )
        self.logger.info(f"Extracting Vocals from Music Result: {result}")
        task_id = result.get('task_id')
        if not task_id:
            raise ValueError(f"No task_id found in extract_vocals result: {result}")

        self.logger.info(f"Waiting for completion of vocal extraction with {task_id=}")
        await asyncio.sleep(30)
        details = await self.wait_for_completion(
            conversion_type="EXTRACTION",
            task_id=task_id,
        )
        self.logger.info(f"Vocal Extraction Details: {details}")
        song_url = details.get("conversion_path")
        song_wav_url = details.get("conversion_path_wav")
        if isinstance(song_url, str):
            song_url = json.loads(song_url)

        if isinstance(song_wav_url, str):
            song_wav_url = json.loads(song_wav_url)

        # NOTE: song_1 is the vocal, song_2 is the instrumental
        await self.send_ws_message(
            "completed",
            {
                "song_1_url": song_url["vocals"],
                "song_1_wav_url": song_wav_url["vocals"],
                "song_1_length": details.get("conversion_duration"),
                "song_2_url": song_url["instrumental"],
                "song_2_wav_url": song_wav_url["instrumental"],
                "song_2_length": details.get("conversion_duration"),
                "music_gpt_task_id": task_id
            }
        )
