"""
Redis-based job queue for music generation tasks
"""
import json
import uuid
import asyncio
from typing import Dict, Any, Optional, List
from enum import Enum
import redis.asyncio as redis
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class JobStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing" 
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class JobType(Enum):
    MUSIC_GENERATION = "music_generation"
    MUSIC_REMIX = "music_remix"
    MUSIC_EXTEND = "music_extend"
    VOCAL_EXTRACTION = "vocal_extraction"
    SOUND_GENERATION = "sound_generation"

class MusicJobQueue:
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis = redis.from_url(redis_url)
        self.job_queue_key = "music_jobs:queue"
        self.job_data_prefix = "music_jobs:data:"
        self.job_status_prefix = "music_jobs:status:"
        self.job_results_prefix = "music_jobs:results:"
        self.active_jobs_key = "music_jobs:active"
        
    async def submit_job(
        self, 
        job_type: JobType, 
        job_data: Dict[str, Any],
        user_id: Optional[str] = None,
        priority: int = 0
    ) -> str:
        """Submit a new job to the queue"""
        job_id = str(uuid.uuid4())
        
        job_metadata = {
            "job_id": job_id,
            "job_type": job_type.value,
            "user_id": user_id,
            "priority": priority,
            "created_at": datetime.utcnow().isoformat(),
            "status": JobStatus.PENDING.value
        }
        
        # Store job data and metadata
        await self.redis.hset(
            f"{self.job_data_prefix}{job_id}",
            mapping={
                "metadata": json.dumps(job_metadata),
                "data": json.dumps(job_data)
            }
        )
        
        # Add to queue with priority (higher priority = lower score)
        await self.redis.zadd(
            self.job_queue_key,
            {job_id: -priority}
        )
        
        # Set initial status
        await self.set_job_status(job_id, JobStatus.PENDING)
        
        logger.info(f"Submitted job {job_id} of type {job_type.value}")
        return job_id
    
    async def get_next_job(self) -> Optional[Dict[str, Any]]:
        """Get the next job from the queue"""
        # Get highest priority job (lowest score)
        result = await self.redis.zpopmin(self.job_queue_key)
        if not result:
            return None
            
        job_id = result[0][0].decode('utf-8')
        
        # Get job data
        job_data = await self.redis.hgetall(f"{self.job_data_prefix}{job_id}")
        if not job_data:
            logger.warning(f"Job {job_id} data not found")
            return None
            
        metadata = json.loads(job_data[b'metadata'].decode('utf-8'))
        data = json.loads(job_data[b'data'].decode('utf-8'))
        
        # Mark as processing
        await self.set_job_status(job_id, JobStatus.PROCESSING)
        await self.redis.sadd(self.active_jobs_key, job_id)
        
        return {
            "job_id": job_id,
            "job_type": JobType(metadata["job_type"]),
            "metadata": metadata,
            "data": data
        }
    
    async def set_job_status(
        self, 
        job_id: str, 
        status: JobStatus, 
        progress: Optional[float] = None,
        message: Optional[str] = None
    ):
        """Update job status"""
        status_data = {
            "status": status.value,
            "updated_at": datetime.utcnow().isoformat()
        }
        
        if progress is not None:
            status_data["progress"] = progress
        if message:
            status_data["message"] = message
            
        await self.redis.hset(
            f"{self.job_status_prefix}{job_id}",
            mapping={k: json.dumps(v) if isinstance(v, (dict, list)) else str(v) 
                    for k, v in status_data.items()}
        )
        
        # Set expiration for completed/failed jobs (24 hours)
        if status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
            await self.redis.expire(f"{self.job_status_prefix}{job_id}", 86400)
            await self.redis.srem(self.active_jobs_key, job_id)
    
    async def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get current job status"""
        status_data = await self.redis.hgetall(f"{self.job_status_prefix}{job_id}")
        if not status_data:
            return None
            
        return {
            k.decode('utf-8'): v.decode('utf-8') 
            for k, v in status_data.items()
        }
    
    async def set_job_result(self, job_id: str, result: Dict[str, Any]):
        """Store job result"""
        await self.redis.hset(
            f"{self.job_results_prefix}{job_id}",
            mapping={"result": json.dumps(result)}
        )
        
        # Set expiration (24 hours)
        await self.redis.expire(f"{self.job_results_prefix}{job_id}", 86400)
    
    async def get_job_result(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job result"""
        result_data = await self.redis.hget(f"{self.job_results_prefix}{job_id}", "result")
        if not result_data:
            return None
        return json.loads(result_data.decode('utf-8'))
    
    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a pending job"""
        # Remove from queue if pending
        removed = await self.redis.zrem(self.job_queue_key, job_id)
        
        # Update status
        await self.set_job_status(job_id, JobStatus.CANCELLED)
        
        return bool(removed)
    
    async def get_queue_stats(self) -> Dict[str, int]:
        """Get queue statistics"""
        pending_count = await self.redis.zcard(self.job_queue_key)
        active_count = await self.redis.scard(self.active_jobs_key)
        
        return {
            "pending": pending_count,
            "active": active_count
        }
    
    async def cleanup_expired_jobs(self):
        """Clean up old job data"""
        # This would be called periodically by a cleanup task
        # Implementation depends on specific retention requirements
        pass
