"""
Background worker for processing music generation jobs
"""
import asyncio
import logging
from typing import Dict, Any
from .redis_queue import MusicJobQueue, JobType, JobStatus
from ..generative_ai.music_generators.music_gpt_generator import MusicGPTClient

logger = logging.getLogger(__name__)

class MusicJobWorker:
    def __init__(self, queue: MusicJobQueue, worker_id: str = "worker-1"):
        self.queue = queue
        self.worker_id = worker_id
        self.running = False
        self.current_job_id = None
        
    async def start(self):
        """Start the worker"""
        self.running = True
        logger.info(f"Worker {self.worker_id} starting...")
        
        while self.running:
            try:
                # Get next job
                job = await self.queue.get_next_job()
                if not job:
                    # No jobs available, wait and retry
                    await asyncio.sleep(1)
                    continue
                
                self.current_job_id = job["job_id"]
                logger.info(f"Worker {self.worker_id} processing job {self.current_job_id}")
                
                # Process the job
                await self.process_job(job)
                
            except Exception as e:
                logger.error(f"Worker {self.worker_id} error: {e}")
                if self.current_job_id:
                    await self.queue.set_job_status(
                        self.current_job_id, 
                        JobStatus.FAILED,
                        message=str(e)
                    )
                await asyncio.sleep(5)  # Wait before retrying
            finally:
                self.current_job_id = None
    
    async def stop(self):
        """Stop the worker"""
        self.running = False
        logger.info(f"Worker {self.worker_id} stopping...")
    
    async def process_job(self, job: Dict[str, Any]):
        """Process a single job"""
        job_id = job["job_id"]
        job_type = job["job_type"]
        job_data = job["data"]
        
        try:
            # Create MusicGPT client without WebSocket
            client = MusicGPTClient(ws_conn=None, logger=logger)
            
            # Process based on job type
            if job_type == JobType.MUSIC_GENERATION:
                result = await self.process_music_generation(client, job_id, job_data)
            elif job_type == JobType.MUSIC_REMIX:
                result = await self.process_music_remix(client, job_id, job_data)
            elif job_type == JobType.MUSIC_EXTEND:
                result = await self.process_music_extend(client, job_id, job_data)
            elif job_type == JobType.VOCAL_EXTRACTION:
                result = await self.process_vocal_extraction(client, job_id, job_data)
            elif job_type == JobType.SOUND_GENERATION:
                result = await self.process_sound_generation(client, job_id, job_data)
            else:
                raise ValueError(f"Unknown job type: {job_type}")
            
            # Store result and mark as completed
            await self.queue.set_job_result(job_id, result)
            await self.queue.set_job_status(job_id, JobStatus.COMPLETED, progress=1.0)
            
            logger.info(f"Job {job_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Job {job_id} failed: {e}")
            await self.queue.set_job_status(
                job_id, 
                JobStatus.FAILED, 
                message=str(e)
            )
            raise
    
    async def process_music_generation(
        self, 
        client: MusicGPTClient, 
        job_id: str, 
        job_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process music generation job"""
        await self.queue.set_job_status(
            job_id, 
            JobStatus.PROCESSING, 
            progress=0.1, 
            message="Initiating music generation"
        )
        
        # Submit to MusicGPT API
        result = client.generate_music(
            prompt=job_data["prompt"],
            music_style=job_data.get("music_style"),
            lyrics=job_data.get("lyrics"),
            make_instrumental=job_data.get("make_instrumental", False),
            vocal_only=job_data.get("vocal_only", False),
            voice_id=job_data.get("voice_id")
        )
        
        task_id = result.get('task_id')
        await asyncio.sleep(30)  # Initial wait
        
        await self.queue.set_job_status(
            job_id, 
            JobStatus.PROCESSING, 
            progress=0.3, 
            message="Music generation in progress"
        )
        
        # Wait for completion with progress updates
        details = await self.wait_for_completion_with_progress(
            client, "MUSIC_AI", task_id, job_id
        )
        
        return {
            "song_1_url": details.get("conversion_path_1"),
            "song_1_wav_url": details.get("conversion_path_wav_1"),
            "song_1_name": details.get("title_1"),
            "song_1_lyrics": details.get("lyrics_1"),
            "song_1_length": details.get("conversion_duration_1"),
            "song_2_url": details.get("conversion_path_2"),
            "song_2_wav_url": details.get("conversion_path_wav_2"),
            "song_2_name": details.get("title_2"),
            "song_2_lyrics": details.get("lyrics_2"),
            "song_2_length": details.get("conversion_duration_2"),
            "album_cover_url": details.get("album_cover_path"),
            "album_cover_thumbnail_url": details.get("album_cover_thumbnail"),
            "music_gpt_task_id": task_id
        }
    
    async def process_music_remix(
        self, 
        client: MusicGPTClient, 
        job_id: str, 
        job_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process music remix job"""
        await self.queue.set_job_status(
            job_id, 
            JobStatus.PROCESSING, 
            progress=0.1, 
            message="Initiating music remix"
        )
        
        result = client.remix_music(
            audio_base64=job_data["audio_base64"],
            prompt=job_data["prompt"],
            lyrics=job_data.get("lyrics")
        )
        
        task_id = result.get('task_id')
        await asyncio.sleep(30)
        
        details = await self.wait_for_completion_with_progress(
            client, "REMIX", task_id, job_id
        )
        
        return {
            "song_1_url": details.get("conversion_path_1"),
            "song_1_wav_url": details.get("conversion_path_wav_1"),
            "song_1_length": details.get("conversion_duration_1"),
            "song_2_url": details.get("conversion_path_2"),
            "song_2_wav_url": details.get("conversion_path_wav_2"),
            "song_2_length": details.get("conversion_duration_2"),
            "music_gpt_task_id": task_id
        }
    
    async def process_music_extend(
        self, 
        client: MusicGPTClient, 
        job_id: str, 
        job_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process music extension job"""
        # Similar implementation to remix
        pass
    
    async def process_vocal_extraction(
        self, 
        client: MusicGPTClient, 
        job_id: str, 
        job_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process vocal extraction job"""
        # Similar implementation
        pass
    
    async def process_sound_generation(
        self, 
        client: MusicGPTClient, 
        job_id: str, 
        job_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process sound generation job"""
        # Similar implementation
        pass
    
    async def wait_for_completion_with_progress(
        self,
        client: MusicGPTClient,
        conversion_type: str,
        task_id: str,
        job_id: str,
        max_wait_time: int = 600,
        check_interval: int = 10
    ) -> Dict[str, Any]:
        """Wait for completion with progress updates"""
        import time
        start_time = time.time()
        progress_base = 0.3
        
        while time.time() - start_time < max_wait_time:
            details = client.get_conversion_details(
                conversion_type=conversion_type,
                task_id=task_id
            )
            
            if details.get("status") == "COMPLETED":
                await self.queue.set_job_status(
                    job_id, 
                    JobStatus.PROCESSING, 
                    progress=0.9, 
                    message="Generation completed, preparing results"
                )
                return details
            
            if details.get("status") == "ERROR":
                raise Exception(f"Conversion {task_id} failed: {details}")
            
            # Update progress based on elapsed time
            elapsed = time.time() - start_time
            estimated_progress = progress_base + (elapsed / max_wait_time) * 0.6
            estimated_progress = min(estimated_progress, 0.8)  # Cap at 80%
            
            await self.queue.set_job_status(
                job_id,
                JobStatus.PROCESSING,
                progress=estimated_progress,
                message=f"Status: {details.get('status', 'Processing')}"
            )
            
            await asyncio.sleep(check_interval)
        
        raise TimeoutError(f"Conversion {task_id} did not complete within {max_wait_time} seconds")
