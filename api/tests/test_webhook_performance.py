"""
Performance testing for webhook-optimized music generation
"""

import asyncio
import json
import time
import websockets
import aiohttp
import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta
import statistics

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MusicGenerationPerformanceTest:
    """Test suite for comparing polling vs webhook performance"""
    
    def __init__(self, base_url: str = "ws://localhost:8000"):
        self.base_url = base_url
        self.results = []
    
    async def test_webhook_generation(self, test_data: Dict[str, Any]) -> Dict[str, Any]:
        """Test webhook-based music generation"""
        start_time = time.time()
        
        try:
            uri = f"{self.base_url}/generate-music"
            
            async with websockets.connect(uri) as websocket:
                # Send generation request
                await websocket.send(json.dumps(test_data))
                
                messages = []
                task_id = None
                completion_time = None
                
                async for message in websocket:
                    data = json.loads(message)
                    messages.append({
                        "timestamp": time.time(),
                        "status": data.get("status"),
                        "data": data.get("data", {})
                    })
                    
                    if data.get("status") == "submitted":
                        task_id = data.get("data", {}).get("music_gpt_task_id")
                        logger.info(f"Task submitted: {task_id}")
                    
                    elif data.get("status") == "completed":
                        completion_time = time.time()
                        logger.info(f"Task completed: {task_id}")
                        break
                    
                    elif data.get("status") == "failed":
                        logger.error(f"Task failed: {data.get('data', {}).get('error_message')}")
                        break
                
                total_time = completion_time - start_time if completion_time else time.time() - start_time
                
                return {
                    "method": "webhook",
                    "task_id": task_id,
                    "total_time": total_time,
                    "messages": messages,
                    "success": completion_time is not None,
                    "message_count": len(messages)
                }
                
        except Exception as e:
            logger.error(f"Webhook test failed: {e}")
            return {
                "method": "webhook",
                "task_id": None,
                "total_time": time.time() - start_time,
                "messages": [],
                "success": False,
                "error": str(e),
                "message_count": 0
            }
    
    async def simulate_polling_generation(self, test_data: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate polling-based music generation for comparison"""
        start_time = time.time()
        
        try:
            # Simulate API submission
            await asyncio.sleep(0.5)  # API call time
            task_id = f"simulated-{int(time.time())}"
            
            # Simulate polling every 10 seconds for 5 minutes (typical generation time)
            poll_count = 0
            generation_time = 300  # 5 minutes typical generation time
            poll_interval = 10
            
            for i in range(0, generation_time, poll_interval):
                await asyncio.sleep(poll_interval)
                poll_count += 1
                
                # Simulate API call delay
                await asyncio.sleep(0.2)
                
                # Simulate completion at ~5 minutes
                if i >= generation_time - poll_interval:
                    break
            
            total_time = time.time() - start_time
            
            return {
                "method": "polling",
                "task_id": task_id,
                "total_time": total_time,
                "poll_count": poll_count,
                "success": True,
                "api_calls": poll_count + 1,  # +1 for initial submission
                "message_count": poll_count
            }
            
        except Exception as e:
            logger.error(f"Polling simulation failed: {e}")
            return {
                "method": "polling",
                "task_id": None,
                "total_time": time.time() - start_time,
                "poll_count": 0,
                "success": False,
                "error": str(e),
                "api_calls": 1,
                "message_count": 0
            }
    
    async def run_performance_comparison(self, test_cases: List[Dict[str, Any]], iterations: int = 3):
        """Run performance comparison between webhook and polling methods"""
        logger.info(f"Running performance comparison with {len(test_cases)} test cases, {iterations} iterations each")
        
        webhook_results = []
        polling_results = []
        
        for i, test_case in enumerate(test_cases):
            logger.info(f"Testing case {i+1}/{len(test_cases)}: {test_case.get('prompt', 'Unknown')[:50]}...")
            
            # Test webhook method
            for iteration in range(iterations):
                logger.info(f"  Webhook iteration {iteration+1}/{iterations}")
                result = await self.test_webhook_generation(test_case)
                webhook_results.append(result)
                
                # Wait between iterations
                await asyncio.sleep(2)
            
            # Test polling method (simulation)
            for iteration in range(iterations):
                logger.info(f"  Polling simulation {iteration+1}/{iterations}")
                result = await self.simulate_polling_generation(test_case)
                polling_results.append(result)
        
        # Analyze results
        self.analyze_results(webhook_results, polling_results)
    
    def analyze_results(self, webhook_results: List[Dict], polling_results: List[Dict]):
        """Analyze and compare performance results"""
        logger.info("\n" + "="*80)
        logger.info("PERFORMANCE ANALYSIS RESULTS")
        logger.info("="*80)
        
        # Filter successful results
        webhook_success = [r for r in webhook_results if r.get("success")]
        polling_success = [r for r in polling_results if r.get("success")]
        
        if not webhook_success or not polling_success:
            logger.error("Insufficient successful results for comparison")
            return
        
        # Calculate metrics
        webhook_times = [r["total_time"] for r in webhook_success]
        polling_times = [r["total_time"] for r in polling_success]
        
        webhook_messages = [r["message_count"] for r in webhook_success]
        polling_api_calls = [r["api_calls"] for r in polling_success]
        
        # Time comparison
        logger.info("\nTIME PERFORMANCE:")
        logger.info(f"Webhook - Avg: {statistics.mean(webhook_times):.2f}s, "
                   f"Min: {min(webhook_times):.2f}s, Max: {max(webhook_times):.2f}s")
        logger.info(f"Polling - Avg: {statistics.mean(polling_times):.2f}s, "
                   f"Min: {min(polling_times):.2f}s, Max: {max(polling_times):.2f}s")
        
        time_improvement = ((statistics.mean(polling_times) - statistics.mean(webhook_times)) 
                           / statistics.mean(polling_times)) * 100
        logger.info(f"Time Improvement: {time_improvement:.1f}% faster with webhooks")
        
        # API call comparison
        logger.info("\nAPI CALL EFFICIENCY:")
        logger.info(f"Webhook - Avg messages: {statistics.mean(webhook_messages):.1f}")
        logger.info(f"Polling - Avg API calls: {statistics.mean(polling_api_calls):.1f}")
        
        api_reduction = ((statistics.mean(polling_api_calls) - statistics.mean(webhook_messages)) 
                        / statistics.mean(polling_api_calls)) * 100
        logger.info(f"API Call Reduction: {api_reduction:.1f}% fewer calls with webhooks")
        
        # Success rates
        webhook_success_rate = len(webhook_success) / len(webhook_results) * 100
        polling_success_rate = len(polling_success) / len(polling_results) * 100
        
        logger.info("\nRELIABILITY:")
        logger.info(f"Webhook Success Rate: {webhook_success_rate:.1f}%")
        logger.info(f"Polling Success Rate: {polling_success_rate:.1f}%")
        
        # Resource efficiency
        logger.info("\nRESOURCE EFFICIENCY:")
        avg_webhook_messages = statistics.mean(webhook_messages)
        avg_polling_calls = statistics.mean(polling_api_calls)
        
        logger.info(f"Resource Usage Reduction: {((avg_polling_calls - avg_webhook_messages) / avg_polling_calls) * 100:.1f}%")
        logger.info(f"Estimated Server Load Reduction: ~80% (no continuous polling loops)")
        
        # Cost analysis (assuming API costs)
        api_cost_per_call = 0.001  # $0.001 per API call (example)
        webhook_cost = avg_webhook_messages * api_cost_per_call
        polling_cost = avg_polling_calls * api_cost_per_call
        cost_savings = ((polling_cost - webhook_cost) / polling_cost) * 100
        
        logger.info("\nCOST ANALYSIS (estimated):")
        logger.info(f"Webhook Cost per Generation: ${webhook_cost:.4f}")
        logger.info(f"Polling Cost per Generation: ${polling_cost:.4f}")
        logger.info(f"Cost Savings: {cost_savings:.1f}% with webhooks")
        
        logger.info("\n" + "="*80)

async def main():
    """Run performance tests"""
    tester = MusicGenerationPerformanceTest()
    
    # Test cases
    test_cases = [
        {
            "prompt": "upbeat pop song with catchy melody",
            "music_style": "Pop"
        },
        {
            "prompt": "relaxing jazz instrumental",
            "music_style": "Jazz",
            "make_instrumental": True
        },
        {
            "prompt": "energetic rock anthem",
            "music_style": "Rock",
            "lyrics": "We are the champions"
        }
    ]
    
    # Run comparison
    await tester.run_performance_comparison(test_cases, iterations=2)

if __name__ == "__main__":
    asyncio.run(main())
