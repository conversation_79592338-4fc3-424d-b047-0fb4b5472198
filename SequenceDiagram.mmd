sequenceDiagram
    participant U as User
    participant G as Gradio GUI<br/>(MusicGenerationInterface)
    participant A as FastAPI<br/>(/generate-music WS)
    participant M as MusicGPTClient
    participant S as MusicGPT API<br/>(api.musicgpt.com)
    participant F as File Server<br/>(Generated URLs)

    Note over U,F: Music Generation WebSocket Flow

    U->>G: 1. Fill form and click Generate Music
    Note right of U: prompt, music_style, lyrics,<br/>make_instrumental, vocal_only, voice_id

    G->>A: 2. WebSocket connect to /generate-music
    A-->>G: 3. accept() connection
    Note over A: Small delay (0.1s) to ensure<br/>connection is established

    G->>A: 4. send JSON payload
    Note right of G: Request includes prompt,<br/>music_style, lyrics, and options

    A->>M: 5. generate_music_and_send_data_over_ws(data)
    
    M-->>G: 6. WS status: "initiating music generation"
    G-->>U: 7. Progress: "Sent generation request..."

    M->>S: 8. POST /MusicAI
    Note right of M: Headers: Authorization with MUSIC_GPT_API_KEY<br/>Payload: prompt, music_style, lyrics, etc.
    S-->>M: 9. Response with task_id

    Note over M: await sleep(30) - initial processing time

    loop Poll every 10 seconds (max 600s timeout)
        M->>S: 10. GET /byId?conversionType=MUSIC_AI&task_id
        S-->>M: 11. Status response (PROCESSING, QUEUED, etc.)
        M-->>G: 12. WS status: "generating music"
        Note right of M: Includes current status from API
        G-->>U: 13. Progress: "Generating music... Status: PROCESSING"
    end

    S-->>M: 14. Status: COMPLETED with URLs and metadata
    Note right of S: Response includes song URLs (MP3 & WAV),<br/>song names, lyrics, durations,<br/>and album cover URL

    M-->>G: 15. WS status: "completed" with all data
    Note right of M: song_1_url, song_1_wav_url, song_1_name,<br/>song_1_lyrics, song_1_length, song_2_url,<br/>song_2_wav_url, song_2_name, song_2_lyrics,<br/>song_2_length, album_cover_url

    G-->>U: 16. Progress: "Downloading generated files..."

    par Download Song 1
        G->>F: 17a. GET song_1_url
        F-->>G: 17b. MP3 audio data
        G->>G: 17c. Save to temp_audio/
    and Download Song 2
        G->>F: 18a. GET song_2_url
        F-->>G: 18b. MP3 audio data
        G->>G: 18c. Save to temp_audio/
    and Download Album Cover
        G->>F: 19a. GET album_cover_url
        F-->>G: 19b. JPG image data
        G->>G: 19c. Save to temp_audio/
    end

    G-->>U: 20. Progress: "Music generation completed!"
    G-->>U: 21. Display results with audio players and metadata

    A->>A: 22. Close WebSocket connection

    Note over U,F: Error Handling: WebSocket disconnects,<br/>API timeouts (600s max), MusicGPT API errors,<br/>File download failures