sequenceDiagram
    participant U as User
    participant G as Gradio GUI (gui/src/gui.py)
    participant A as FastAPI WS /generate-music (api/src/main.py)
    participant M as MusicGPTClient (music_generators)
    participant S as MusicGPT API (api.musicgpt.com)

    U->>G: Click "Generate Music"
    G->>A: WS connect
    A-->>G: accept()
    G->>A: send JSON {prompt, music_style?, lyrics?, make_instrumental?, vocal_only?, voice_id?}

    A->>M: generate_music_and_send_data_over_ws(data)
    M-->>G: WS status: "initiating music generation"

    M->>S: POST /MusicAI (Authorization: MUSIC_GPT_API_KEY, payload)
    S-->>M: { task_id }

    Note over M: await sleep(30) then poll every 10s (max 600s)
    loop Poll until COMPLETED
        M->>S: GET /byId?conversionType=MUSIC_AI&task_id={task_id}
        S-->>M: { status: QUEUED|PROCESSING|... }
        M-->>G: WS status: "generating music" + {status}
    end

    S-->>M: { status: COMPLETED, conversion_path_1, ... }
    M-->>G: WS status: "completed" + {
        song_1_url, song_1_wav_url, song_1_name, song_1_lyrics, song_1_length,
        song_2_url, song_2_wav_url, song_2_name, song_2_lyrics, song_2_length,
        album_cover_url, music_gpt_task_id
    }

    G->>G: Download files via HTTP GET to temp_audio/
    G-->>U: Display players, titles, lyrics, durations, cover image
