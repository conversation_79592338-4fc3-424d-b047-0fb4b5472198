services:
  # PostgreSQL Database for job management
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: job_management
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - ./test/postgres:/var/lib/postgresql/data
      - ./iac/terraform/modules/job-management-database/database_schema.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - music-generation
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d job_management"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Google Cloud Storage Emulator (for local development)
  gcs-emulator:
    image: fsouza/fake-gcs-server:latest
    command: ["-scheme", "http", "-host", "0.0.0.0", "-port", "4443", "-public-host", "gcs-emulator:4443", "-filesystem-root", "/data"]
    ports:
      - "4443:4443"
    volumes:
      - ./test/gcs:/data
    networks:
      - music-generation
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:4443/storage/v1/b"]
      interval: 30s
      timeout: 5s
      retries: 3

  # Google Cloud Tasks Emulator (for local development)
  # Using a proper FastAPI application to simulate Cloud Tasks endpoints
  cloud-tasks-emulator:
    image: python:3.12-slim
    command: >
      sh -c "
        pip install fastapi uvicorn httpx &&
        python /app/cloud_tasks_emulator.py
      "
    volumes:
      - ./job_management/cloud_tasks_emulator.py:/app/cloud_tasks_emulator.py
    ports:
      - "8123:8123"
    networks:
      - music-generation
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8123/')"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  api:
    build:
      context: .
      dockerfile: ./api/Dockerfile
    expose:
      - 8000
    ports:
      - 8000:8000
    env_file:
      - path: "./api/.env"
        required: false
    volumes:
      - ./test/logs/api:/app/logs
    restart: always
    networks:
      - music-generation
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/ping"]
      interval: 30s
      timeout: 5s
      retries: 5

  job-management:
    build:
      context: .
      dockerfile: ./job_management/Dockerfile
    expose:
      - 8001
    ports:
      - 8001:8001
    env_file:
      - path: "./job_management/.env"
        required: true
    volumes:
      - ./test/logs/job-management:/app/logs
      - ./test/temp:/app/temp
    depends_on:
      db:
        condition: service_healthy
      gcs-emulator:
        condition: service_healthy
      cloud-tasks-emulator:
        condition: service_healthy
    restart: always
    networks:
      - music-generation
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  gui:
    build:
      context: .
      dockerfile: ./gui/Dockerfile
    expose:
      - 7860
    ports:
      - 7860:7860
    env_file:
      - path: "./gui/.env"
        required: false
    environment:
      MUSIC_GENERATION_WS_URL: "ws://api:8000"
      JOB_MANAGEMENT_URL: "http://job-management:8001"
    volumes:
      - ./test/logs/gui:/app/logs
    depends_on:
      api:
        condition: service_healthy
      job-management:
        condition: service_healthy
    restart: always
    networks:
      - music-generation
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860"]
      interval: 30s
      timeout: 5s
      retries: 5

networks:
  music-generation:
    name: music-generation
