services:
  api:
    build:
      context: .
      dockerfile: ./api/Dockerfile
    expose:
      - 8000
    ports:
      - 8000:8000
    env_file:
      - path: "./api/.env"
        required: false
    restart: always
    networks:
      - music-generation
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/ping"]
      interval: 30s
      timeout: 5s
      retries: 5

  gui:
    build:
      context: .
      dockerfile: ./gui/Dockerfile
    expose:
      - 7860
    ports:
      - 7860:7860
    env_file:
      - path: "./gui/.env"
        required: false
    environment:
      MUSIC_GENERATION_WS_URL: "ws://api:8000"
    restart: always
    networks:
      - music-generation
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860"]
      interval: 30s
      timeout: 5s
      retries: 5

networks:
  music-generation:
    name: music-generation
