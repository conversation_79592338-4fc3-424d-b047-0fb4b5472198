#!/usr/bin/env python3
"""
Google Cloud Tasks Emulator for Local Development

This module provides a lightweight mock implementation of the Google Cloud Tasks API
for local development and testing purposes. It simulates the basic Cloud Tasks
endpoints needed by the job management service.

Features:
- Health check endpoint
- Task creation simulation
- Queue information retrieval
- Task listing (empty for simplicity)

Usage:
    python cloud_tasks_emulator.py

The emulator will start on port 8123 and provide the following endpoints:
- GET / - Health check
- POST /v2/projects/{project}/locations/{location}/queues/{queue}/tasks - Create task
- GET /v2/projects/{project}/locations/{location}/queues/{queue} - Get queue info
- GET /v2/projects/{project}/locations/{location}/queues/{queue}/tasks - List tasks
"""

from fastapi import FastAPI, Request, HTTPException
from typing import Dict, Any, List, Optional
import uvicorn
import json
import logging
import asyncio
import time
import httpx
from datetime import datetime, timedelta
from collections import defaultdict, deque

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Cloud Tasks Emulator",
    description="Mock Google Cloud Tasks API for local development",
    version="1.0.0"
)

# In-memory storage for tasks (for demonstration purposes)
tasks_storage: Dict[str, List[Dict[str, Any]]] = {}

# Queue configuration matching Terraform settings
QUEUE_CONFIGS = {
    "musicgpt-queue-local": {
        "max_dispatches_per_second": 5,
        "max_concurrent_dispatches": 10,
        "max_attempts": 3,
        "max_retry_duration": 1800,  # 30 minutes
        "min_backoff": 10,
        "max_backoff": 180,
        "max_doublings": 3
    },
    "task-queue-local": {
        "max_dispatches_per_second": 100,
        "max_concurrent_dispatches": 200,
        "max_attempts": 3,
        "max_retry_duration": 600,  # 10 minutes
        "min_backoff": 5,
        "max_backoff": 300,
        "max_doublings": 5
    },
    "job-queue-local": {
        "max_dispatches_per_second": 20,
        "max_concurrent_dispatches": 100,
        "max_attempts": 5,
        "max_retry_duration": 3600,  # 1 hour
        "min_backoff": 5,
        "max_backoff": 300,
        "max_doublings": 5
    },
    "priority-queue-local": {
        "max_dispatches_per_second": 50,
        "max_concurrent_dispatches": 50,
        "max_attempts": 2,
        "max_retry_duration": 300,  # 5 minutes
        "min_backoff": 1,
        "max_backoff": 60,
        "max_doublings": 3
    }
}

# Rate limiting tracking
class RateLimiter:
    def __init__(self):
        self.dispatch_times: Dict[str, deque] = defaultdict(deque)
        self.concurrent_tasks: Dict[str, int] = defaultdict(int)
        self.task_queue: Dict[str, deque] = defaultdict(deque)
        self.processing_tasks: Dict[str, set] = defaultdict(set)

    def can_dispatch(self, queue_name: str) -> bool:
        """Check if a task can be dispatched based on rate limits"""
        config = QUEUE_CONFIGS.get(queue_name, {})
        max_per_second = config.get("max_dispatches_per_second", 10)
        max_concurrent = config.get("max_concurrent_dispatches", 50)

        now = time.time()

        # Clean old dispatch times (older than 1 second)
        dispatch_times = self.dispatch_times[queue_name]
        while dispatch_times and dispatch_times[0] < now - 1.0:
            dispatch_times.popleft()

        # Check rate limit (dispatches per second)
        if len(dispatch_times) >= max_per_second:
            return False

        # Check concurrent limit
        if self.concurrent_tasks[queue_name] >= max_concurrent:
            return False

        return True

    def record_dispatch(self, queue_name: str, task_id: str):
        """Record a task dispatch"""
        now = time.time()
        self.dispatch_times[queue_name].append(now)
        self.concurrent_tasks[queue_name] += 1
        self.processing_tasks[queue_name].add(task_id)

    def complete_task(self, queue_name: str, task_id: str):
        """Mark a task as completed"""
        if task_id in self.processing_tasks[queue_name]:
            self.processing_tasks[queue_name].remove(task_id)
            self.concurrent_tasks[queue_name] = max(0, self.concurrent_tasks[queue_name] - 1)

# Global rate limiter instance
rate_limiter = RateLimiter()

# Task processing simulation
async def simulate_task_processing(queue_name: str, task_id: str, task_data: Dict[str, Any]):
    """Execute task by making HTTP request to job management service"""
    try:
        logger.info(f"Processing task {task_id} in queue {queue_name}")

        # Extract the actual payload from the Cloud Tasks structure
        # Cloud Tasks sends data in the format: {"http_request": {"body": "...", "url": "...", "headers": {...}}}
        if "http_request" in task_data and "body" in task_data["http_request"]:
            # Parse the JSON body to get the actual task payload
            import json
            actual_payload = json.loads(task_data["http_request"]["body"])
        else:
            # Fallback for direct payload (shouldn't happen in real Cloud Tasks)
            actual_payload = task_data

        # Make HTTP request to job management service to actually execute the task
        job_management_url = "http://job-management:8001/tasks/process"

        async with httpx.AsyncClient(timeout=300.0) as client:  # 5 minute timeout for music generation
            response = await client.post(job_management_url, json=actual_payload)

            if response.status_code == 200:
                logger.info(f"Task {task_id} completed successfully")
            else:
                logger.error(f"Task {task_id} failed with status {response.status_code}: {response.text}")
                raise Exception(f"Task execution failed: {response.status_code}")

    except Exception as e:
        logger.error(f"Task {task_id} processing error: {e}")
        raise  # Re-raise to trigger retry logic if needed
    finally:
        # Always mark task as completed to free up concurrent slot
        rate_limiter.complete_task(queue_name, task_id)


@app.get("/")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "cloud-tasks-emulator",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }


@app.post("/v2/projects/{project}/locations/{location}/queues/{queue}/tasks")
async def create_task(
    project: str,
    location: str,
    queue: str,
    request: Request
):
    """
    Create a new task in the specified queue with rate limiting.

    This endpoint simulates the Google Cloud Tasks create_task API with
    proper rate limiting and concurrent dispatch controls.
    """
    try:
        # Parse the request body
        task_data = await request.json()

        # Generate a unique task name
        task_id = f"task-{datetime.utcnow().strftime('%Y%m%d-%H%M%S-%f')}"
        task_name = f"projects/{project}/locations/{location}/queues/{queue}/tasks/{task_id}"

        # Store the task (for debugging and tracking)
        queue_key = f"{project}/{location}/{queue}"
        if queue_key not in tasks_storage:
            tasks_storage[queue_key] = []

        task_record = {
            "name": task_name,
            "task_id": task_id,
            "created_at": datetime.utcnow().isoformat(),
            "data": task_data,
            "status": "queued"
        }
        tasks_storage[queue_key].append(task_record)

        # Check rate limits before dispatching
        if rate_limiter.can_dispatch(queue):
            # Record the dispatch
            rate_limiter.record_dispatch(queue, task_id)

            # Start task processing asynchronously (fire and forget)
            asyncio.create_task(simulate_task_processing(queue, task_id, task_data))

            logger.info(f"Task dispatched immediately: {task_name}")
            task_record["status"] = "dispatched"
            task_record["dispatched_at"] = datetime.utcnow().isoformat()
        else:
            # Queue the task for later processing
            config = QUEUE_CONFIGS.get(queue, {})
            max_concurrent = config.get("max_concurrent_dispatches", 50)
            current_concurrent = rate_limiter.concurrent_tasks[queue]

            logger.info(f"Task queued due to rate limits: {task_name}")
            logger.info(f"Queue {queue}: {current_concurrent}/{max_concurrent} concurrent tasks")

            # Add to processing queue for later dispatch
            rate_limiter.task_queue[queue].append((task_id, task_data, task_record))

        # Log the task creation
        logger.info(f"Task created in queue {queue}: {task_name}")
        logger.debug(f"Task data: {json.dumps(task_data, indent=2)}")

        # Return the task name (as per Cloud Tasks API)
        return {"name": task_name}

    except Exception as e:
        logger.error(f"Error creating task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/v2/projects/{project}/locations/{location}/queues/{queue}")
async def get_queue(project: str, location: str, queue: str):
    """
    Get information about a specific queue.
    
    This endpoint simulates the Google Cloud Tasks get_queue API.
    """
    queue_name = f"projects/{project}/locations/{location}/queues/{queue}"
    queue_key = f"{project}/{location}/{queue}"
    
    # Get task count for this queue
    task_count = len(tasks_storage.get(queue_key, []))
    
    logger.info(f"Queue info requested for: {queue_name}")
    
    return {
        "name": queue_name,
        "state": "RUNNING",
        "task_count": task_count,
        "created_at": datetime.utcnow().isoformat()
    }


@app.get("/v2/projects/{project}/locations/{location}/queues/{queue}/tasks")
async def list_tasks(project: str, location: str, queue: str):
    """
    List tasks in a specific queue.
    
    This endpoint simulates the Google Cloud Tasks list_tasks API.
    For simplicity, it returns the stored tasks (if any).
    """
    queue_key = f"{project}/{location}/{queue}"
    tasks = tasks_storage.get(queue_key, [])
    
    logger.info(f"Task list requested for queue: {queue}, found {len(tasks)} tasks")
    
    return {
        "tasks": tasks,
        "queue": f"projects/{project}/locations/{location}/queues/{queue}",
        "total_count": len(tasks)
    }


@app.get("/debug/storage")
async def debug_storage():
    """Debug endpoint to view all stored tasks"""
    return {
        "storage": tasks_storage,
        "total_queues": len(tasks_storage),
        "total_tasks": sum(len(tasks) for tasks in tasks_storage.values())
    }


@app.get("/debug/rate-limits")
async def debug_rate_limits():
    """Debug endpoint to view current rate limiting status"""
    return {
        "concurrent_tasks": dict(rate_limiter.concurrent_tasks),
        "queued_tasks": {queue: len(tasks) for queue, tasks in rate_limiter.task_queue.items()},
        "processing_tasks": {queue: len(tasks) for queue, tasks in rate_limiter.processing_tasks.items()},
        "queue_configs": QUEUE_CONFIGS
    }


# Background task processor
async def process_queued_tasks():
    """Background task to process queued tasks when rate limits allow"""
    while True:
        try:
            for queue_name in list(rate_limiter.task_queue.keys()):
                queue = rate_limiter.task_queue[queue_name]

                # Process tasks from queue if rate limits allow
                while queue and rate_limiter.can_dispatch(queue_name):
                    task_id, task_data, task_record = queue.popleft()

                    # Record the dispatch
                    rate_limiter.record_dispatch(queue_name, task_id)

                    # Start task processing
                    asyncio.create_task(simulate_task_processing(queue_name, task_id, task_data))

                    # Update task record
                    task_record["status"] = "dispatched"
                    task_record["dispatched_at"] = datetime.utcnow().isoformat()

                    logger.info(f"Dispatched queued task {task_id} from queue {queue_name}")

            # Sleep for a short interval before checking again
            await asyncio.sleep(0.5)  # Check every 500ms

        except Exception as e:
            logger.error(f"Error in background task processor: {e}")
            await asyncio.sleep(1)  # Wait longer on error


@app.on_event("startup")
async def startup_event():
    """Start background task processor"""
    logger.info("Starting Cloud Tasks Emulator with rate limiting")
    logger.info(f"Queue configurations: {json.dumps(QUEUE_CONFIGS, indent=2)}")

    # Start background task processor
    asyncio.create_task(process_queued_tasks())


if __name__ == "__main__":
    logger.info("Starting Cloud Tasks Emulator on port 8123")
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8123,
        log_level="info"
    )
