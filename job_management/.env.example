# Job Management Service Environment Variables

# Database Configuration - OPTIMIZED FOR HIGH LOAD
# Note: Use 'db' as hostname when running with docker-compose, 'localhost' for standalone
DATABASE_URL=**************************************/job_management
# CRITICAL: Increased pool sizes for 1000 concurrent jobs
DB_POOL_SIZE=50
DB_MAX_OVERFLOW=100
DB_POOL_TIMEOUT=10
DB_POOL_RECYCLE=1800

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=local-dev-project
GCS_STORAGE_BUCKET=music-generation-storage
GCS_ASSETS_PREFIX=assets/
GCS_TEMP_PREFIX=temp/
GCS_RESULTS_PREFIX=results/

# Cloud Tasks Configuration
JOB_QUEUE_NAME=job-queue-local
TASK_QUEUE_NAME=task-queue-local
MUSICGPT_QUEUE_NAME=musicgpt-queue-local
ELEVENLABS_QUEUE_NAME=elevenlabs-queue-local
PRIORITY_QUEUE_NAME=priority-queue-local
CLOUD_TASKS_LOCATION=us-central1

# Emulator Configuration (Docker Compose only)
STORAGE_EMULATOR_HOST=gcs-emulator:4443
CLOUD_TASKS_EMULATOR_HOST=cloud-tasks-emulator:8123

# Application Configuration
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# HTTP Client Timeout Configuration (in seconds)
HTTP_CONNECTION_TIMEOUT=30      # Connection timeout
HTTP_READ_TIMEOUT=900          # Read timeout (15 minutes) - increased for long ElevenLabs scripts
HTTP_TOTAL_TIMEOUT=1800        # Total operation timeout (30 minutes)
HTTP_DOWNLOAD_TIMEOUT=600      # Download timeout (10 minutes)

# Music Generation Timeout Configuration
MUSIC_GEN_TIMEOUT=180          # WebSocket timeout for music generation (3 minutes)
