# Music Generation Job Management Service

Microservice that manages music generation workflows: accepts jobs, orchestrates tasks inline, routes work to the right Cloud Tasks queue, applies API‑specific rate/concurrency controls, and stores assets in GCS.

## Architecture (current)

- Core components
  - Job Manager: persists jobs, selects queue, cancellation, progress updates
  - Task Executor: executes the entire workflow inline (no per‑subtask queue fan‑out)
  - Queue Managers: slot/rate management for MusicGPT (max 10 concurrent) and ElevenLabs (max 10 concurrent)
  - Storage Manager: single‑bucket asset management (assets/, temp/, results/)
- Queues and routing
  - Queues: job_queue, task_queue, musicgpt_queue, elevenlabs_queue, priority_queue
  - Routing (JobManager.get_queue_for_job):
    - Any MusicGPT tasks (music_generation, sound_generation, music_extension, music_remixing) → musicgpt_queue
    - ElevenLabs tasks present and no MusicGPT tasks → elevenlabs_queue
    - Otherwise → task_queue
    - Priority events (cancellations, progress) → priority_queue
    - Fallback → job_queue
- Execution model
  - A job is enqueued once to the chosen queue; TaskExecutor.process_job runs all workflow tasks sequentially inline within that queue worker
  - Resource constraints trigger requeue of the entire job back to the same queue with delay (preserves backpressure)
- Storage layout (single bucket)
  - assets/music/{job_id}/..., assets/images/{job_id}/...
  - temp/processing/{job_id}/...
  - results/{job_id}/job_result.json and per‑task result files

## Repository structure

```
job_management/
├── src/
│   ├── main.py                  # FastAPI app and routes
│   ├── models.py                # Database models
│   ├── schemas.py               # Enums & request/response models
│   └── services/
│       ├── job_manager.py       # Job lifecycle & queue routing
│       ├── task_executor.py     # Inline workflow execution
│       ├── queue_manager.py     # Cloud Tasks + MusicGPT/ElevenLabs slot mgmt
│       ├── storage_manager.py   # GCS operations and result storage
│       ├── workflow_context.py  # Cross‑task context mgmt
│       ├── workflow_tracker.py  # Task start/complete/requeue tracking
│       └── executors/           # Per‑domain executors (musicgpt, elevenlabs, media, content)
├── cloud_tasks_emulator.py      # Local Cloud Tasks emulator
└── requirements.txt
```

## Capabilities

- Workflow jobs only (single‑task jobs removed): prompt_enhancement → music/sound/etc → album_cover, etc.
- Inline orchestration with slot/rate enforcement for external services
- Real‑time updates: WebSocket /jobs/{job_id}/status

## API (key endpoints)

- POST /jobs — submit a workflow job
- GET /jobs/{job_id} — job status and summary
- GET /jobs/{job_id}/detailed — task breakdown and results
- POST /jobs/{job_id}/cancel — cancel a running job
- WebSocket /jobs/{job_id}/status — real‑time progress
- GET /analytics/jobs/stats — aggregate job statistics
- GET /analytics/jobs/recent — recent jobs with optional status filter
- Internal: POST /tasks/process — Cloud Tasks callback (queue workers call this)
- GET /health — health check

## Workflow orchestration and queues

- Submission flow
  1) POST /jobs persists the job, then JobManager.queue_job enqueues a task to the selected queue
  2) Cloud Tasks (or emulator) invokes POST /tasks/process with {action: process_job, job_id, queue}
  3) TaskExecutor.process_job executes all workflow tasks inline; results and context are persisted per task
- Requeue/backpressure
  - If a task hits resource constraints (no slots/rate limit), the executor returns a requeue payload and JobManager.requeue_job_with_delay re‑enqueues the job to the same queue after a delay
- Queue characteristics (guidance)
  - musicgpt_queue: ~5 dispatches/sec, up to 10 concurrent MusicGPT operations (slot‑guarded)
  - task_queue: general fast tasks (e.g., prompt enhancement, text translation)
  - elevenlabs_queue: ElevenLabs voice/translation tasks, capped at 10 concurrent (slot table)
  - priority_queue: urgent signals (cancel, progress) processed immediately

## Asset management patterns

- File formats: prefer WAV over MP3 when both are available; executors often upload WAV by default
- Naming: unique, descriptive filenames per task (e.g., generated_sound.wav, extended_song_1.wav, remixed_song_2.mp3)
- Storage keys in results: per‑task, non‑overlapping keys; internal fields like primary_track/tracks remain; JobManager.get_job_asset_urls returns a concise mapping to the preferred assets (WAV if available)
- Paths: gs://{bucket}/assets/music/{job_id}/{filename}, gs://{bucket}/assets/images/{job_id}/{filename}, results in gs://{bucket}/results/{job_id}/...

## Local development

- Services and ports
  - API (content/music services): http://localhost:8000
  - Job Management API: http://localhost:8001
  - PostgreSQL: localhost:5432
  - GCS emulator: http://localhost:4443
  - Cloud Tasks emulator: http://localhost:8123
- Start
  - docker-compose up -d
  - Health: curl http://localhost:8001/health
  - Docs: open http://localhost:8001/docs
- Useful commands
  - Logs: docker-compose logs job-management
  - DB shell: docker-compose exec db psql -U postgres -d job_management
  - Reset: docker-compose down -v && docker-compose up -d --build

### Required env (.env for job_management)

- DATABASE_URL=**************************************/job_management
- GCS_STORAGE_BUCKET=music-generation-storage
- STORAGE_EMULATOR_HOST=gcs-emulator:4443 (local)
- CLOUD_TASKS_EMULATOR_HOST=cloud-tasks-emulator:8123 (local)
- JOB_QUEUE_NAME, TASK_QUEUE_NAME, MUSICGPT_QUEUE_NAME, ELEVENLABS_QUEUE_NAME, PRIORITY_QUEUE_NAME, CLOUD_TASKS_LOCATION, GOOGLE_CLOUD_PROJECT

## Notes for developers

- All workflow subtasks execute inline within the queue worker; do not dispatch subtasks to other queues
- Use TaskType enums from schemas.py; only JobType.WORKFLOW is accepted
- When adding a new executor, prefer WAV output and descriptive filenames; store result artifacts and context via StorageManager/WorkflowTracker
- If you change queue names or locations, update .env and verify Cloud Tasks emulator config accordingly


## Queue routing and inline execution (diagram)

Below diagram summarizes the current routing and orchestration model.

```mermaid
flowchart LR
  subgraph Client
    A[POST /jobs]
    WS[WebSocket /jobs/{id}/status]
  end

  A -->|persist job| JM[JobManager]
  JM -->|get_queue_for_job|
    Q{Queue selection}
  Q -->|has MusicGPT tasks| MQ[musicgpt_queue]
  Q -->|has ElevenLabs tasks (no MusicGPT)| EQ[elevenlabs_queue]
  Q -->|else| TQ[task_queue]

  MQ --> CT[(Cloud Tasks)]
  EQ --> CT
  TQ --> CT

  CT -->|POST /tasks/process\naction=process_job| TE[TaskExecutor]

  TE -->|sequential inline execution| T1[Task 1]
  T1 --> T2[Task 2]
  T2 --> T3[Task 3]

  TE -->|store per-task results & context| GCS[(GCS: assets/, results/)]

  TE -->|resource constrained?| RC{No slot / rate limit}
  RC -- yes --> RQ[requeue job with delay]
  RQ --> JM
  JM --> CT

  subgraph Signals
    CXL[POST /jobs/{id}/cancel]
    CXL --> PQ[priority_queue]
    PQ --> CT
  end

  WS --> JM
```

## Minimal examples

- Submit a workflow job (JSON body):

```json
{
  "job_type": "workflow",
  "workflow_name": "music_production",
  "tasks": [
    { "task_type": "prompt_enhancement", "parameters": {"prompt": "Upbeat electronic track"} },
    { "task_type": "music_generation", "parameters": {"make_instrumental": false} },
    { "task_type": "album_cover_generation", "parameters": {"creativity": 0.7} }
  ]
}
```

- Check status:

```bash
curl http://localhost:8001/jobs/{job_id}
```

- WebSocket progress (browser or client): ws://localhost:8001/jobs/{job_id}/status

## Schema reference

- TaskType enum and JobType are defined in src/schemas.py
- Task behavior (context, slot management, error/requeue handling) is defined in src/services/task_registry.py

## How to add a new executor

1) Define the TaskType (if not existing) in src/schemas.py
2) Implement an executor method on TaskExecutor (e.g., _execute_my_task) and place domain logic in services/executors/* if appropriate
3) Add a TaskSpec entry in src/services/task_registry.py with:
   - execute_method name (must match TaskExecutor method)
   - flags: needs_context_validation, slot_managed, store_context
   - requeue/raise behavior (uses_handle_requeue, pure_requeue_retry_after, error_requeue_default_retry_after, raise_on_non_resource_error)
4) If slot-managed, use with_musicgpt_slot or with_elevenlabs_slot from services/task_utils.py
5) Store artifacts via StorageManager with descriptive filenames; prefer WAV over MP3 for audio
6) Verify queue routing if this task implies MusicGPT or ElevenLabs queues
7) Add minimal tests and run locally via docker-compose

