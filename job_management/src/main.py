"""
Music Generation Job Management API Implementation
Independent microservice for job management with modular architecture

Architecture:
- Single GCS bucket with organized folder structure (assets/, temp/, results/)
- 4 specialized queues: Job (20/100), Task (100/200), MusicGPT (5/10), Priority (50/50)
- PostgreSQL database with optimized performance settings
- Cloud Run auto-scaling (1-20 instances) with comprehensive IAM

Supported Tasks:
- Prompt Enhancement: Using Google Gemini AI (<10s per task)
- Music Generation: Using MusicGPT API (2min avg, max 10 parallel requests)
- Album Cover Generation: Using Google Imagen/Gemini (<10s per task)
- Text Translation: Using OpenAI GPT (<10s per task)
- Artist Generation: Using Google Gemini AI (<10s per task)
- Spoken Word Script: Using Google Gemini AI (<10s per task)
- Sound Generation: Using MusicGPT API (2min avg, max 10 parallel requests)
- Future Extensions: Music remixing, batch generation

Queue Optimization:
- MusicGPT Queue: 5 dispatches/sec, 10 concurrent (2min avg tasks)
- Task Queue: 100 dispatches/sec, 200 concurrent (<10s tasks)
- Priority Queue: 50 dispatches/sec, 50 concurrent (<1s urgent tasks)
- Job Queue: 20 dispatches/sec, 100 concurrent (workflow orchestration)

Storage Structure (Single Bucket):
music-generation-storage-{env}-{project_id}/
├── assets/
│   ├── music/{job_id}/           # Final music files (.mp3, .wav)
│   └── images/{job_id}/          # Album covers, thumbnails
├── temp/
│   ├── uploads/                 # User uploads
│   └── processing/{job_id}/      # Intermediate processing files
└── results/
    └── {job_id}/                 # Job metadata and results

API Endpoints:
- POST /jobs - Submit new workflow job
- GET /jobs/{job_id} - Get job status and details
- GET /jobs/{job_id}/detailed - Get detailed job status with task breakdown and results
- POST /jobs/{job_id}/cancel - Cancel running job
- GET /analytics/jobs/recent - List recent jobs for analytics
- WebSocket /jobs/{job_id}/status - Real-time job progress
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks, Request, WebSocket, Depends
from fastapi.responses import JSONResponse
from typing import Optional, Dict, Any, Set
from uuid import UUID
from datetime import datetime, timezone
import asyncio
import logging

# Import our refactored modules
from config import storage_config
from schemas import (
    JobSubmissionRequest, JobResponse, DetailedJobResponse, JobResultsData, JobStatus,
    JobType
)
from utils.database import get_db, init_database, create_tables, get_db_session
from services.job_manager import JobManager
from models import Job
from services.storage_manager import StorageManager
from services.task_executor import TaskExecutor
from services.workflow_tracker import WorkflowTracker

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================

# Configure logger
logger = logging.getLogger(__name__)

# ============================================================================
# FASTAPI APPLICATION
# ============================================================================

app = FastAPI(
    title="Music Generation Job Management API", 
    version="1.0.0",
    description="Independent job management microservice with modular architecture"
)

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    """Initialize database and create tables on startup."""
    init_database()
    create_tables()

def get_client_ip(request: Request) -> str:
    """Extract client IP address from request headers."""
    forwarded_for = request.headers.get("x-forwarded-for")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    return request.client.host if request.client else "unknown"

# ============================================================================
# API ENDPOINTS
# ============================================================================


@app.post("/jobs", response_model=JobResponse)
async def submit_job(
    job_request: JobSubmissionRequest,
    request: Request,
    background_tasks: BackgroundTasks,
    db = Depends(get_db)
):
    """Submit a new workflow job"""
    client_ip = get_client_ip(request)
    user_agent = request.headers.get("user-agent", "")

    # Submit job directly without rate limiting to support high-volume concurrent submissions
    job_manager = JobManager(db)
    job_response = await job_manager.submit_job(job_request, client_ip, user_agent)

    return job_response

@app.get("/jobs/{job_id}", response_model=JobResponse)
async def get_job_status(job_id: UUID, db = Depends(get_db)):
    """Get job status and details"""
    job_manager = JobManager(db)
    job = await job_manager.get_job_status(job_id)

    if not job:
        raise HTTPException(404, "Job not found")

    return job

@app.get("/jobs/{job_id}/detailed", response_model=DetailedJobResponse)
async def get_detailed_job_status(job_id: UUID, db = Depends(get_db)):
    """Get detailed job status including task breakdown for workflows and results for completed jobs"""
    job_manager = JobManager(db)
    job = await job_manager.get_job_status(job_id)

    if not job:
        raise HTTPException(404, "Job not found")

    # Get the actual job record for additional fields
    job_record = db.query(Job).filter(Job.job_id == job_id).first()

    # Get workflow status if this is a workflow job
    workflow_status = None
    if job.job_type == JobType.WORKFLOW:
        workflow_tracker = WorkflowTracker(db)
        workflow_status = workflow_tracker.get_workflow_status(job_id)

    # Get results if job is completed
    results_data = None
    if job.status == JobStatus.COMPLETED:
        try:
            asset_urls = await job_manager.get_job_asset_urls(str(job_id))
            results_data = JobResultsData(
                assets=asset_urls,
                storage_info={
                    "bucket": storage_config.bucket_name,
                    "folder_structure": {
                        "music": f"{storage_config.assets_prefix}music/{job_id}/",
                        "images": f"{storage_config.assets_prefix}images/{job_id}/",
                        "results": f"{storage_config.results_prefix}{job_id}/"
                    }
                }
            )
        except Exception as e:
            logger.warning(f"Failed to get results for completed job {job_id}: {e}")

    # Build detailed response
    detailed_response = DetailedJobResponse(
        job_id=job.job_id,
        job_type=job.job_type,
        status=job.status,
        created_at=job.created_at,
        updated_at=job_record.updated_at if job_record else None,
        started_at=getattr(job_record, 'started_at', None) if job_record else None,
        completed_at=getattr(job_record, 'completed_at', None) if job_record else None,
        progress_percentage=job.progress_percentage,
        estimated_completion=job.estimated_completion,
        error_message=getattr(job_record, 'error_message', None) if job_record else None,
        current_task_type=job.current_task_type,
        current_task_index=job.current_task_index,
        workflow_progress_percentage=job.workflow_progress_percentage,
        tasks=workflow_status.get("tasks") if workflow_status else None,
        results=results_data
    )

    return detailed_response

@app.post("/jobs/{job_id}/cancel")
async def cancel_job(job_id: UUID, db = Depends(get_db)):
    """Cancel a running job (uses priority queue for immediate processing)"""
    job_manager = JobManager(db)
    success = await job_manager.cancel_job(job_id)

    if not success:
        raise HTTPException(404, "Job not found or cannot be cancelled")

    return {
        "job_id": job_id,
        "status": "cancelled",
        "message": "Job cancellation queued for immediate processing",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }





# CRITICAL FIX: Global WebSocket connection manager to prevent memory leaks
class WebSocketManager:
    def __init__(self):
        self.connections: Dict[str, Set[WebSocket]] = {}
        self.connection_count = 0
        self.max_connections = 1000  # Prevent memory exhaustion

    async def add_connection(self, job_id: str, websocket: WebSocket) -> bool:
        if self.connection_count >= self.max_connections:
            logger.warning(f"WebSocket connection limit reached: {self.connection_count}")
            return False

        if job_id not in self.connections:
            self.connections[job_id] = set()
        self.connections[job_id].add(websocket)
        self.connection_count += 1
        return True

    async def remove_connection(self, job_id: str, websocket: WebSocket):
        if job_id in self.connections:
            self.connections[job_id].discard(websocket)
            if not self.connections[job_id]:
                del self.connections[job_id]
        self.connection_count = max(0, self.connection_count - 1)

ws_manager = WebSocketManager()

@app.websocket("/jobs/{job_id}/status")
async def job_status_websocket(websocket: WebSocket, job_id: UUID):
    """WebSocket endpoint for real-time job status updates with connection management"""
    job_id_str = str(job_id)

    # CRITICAL FIX: Check connection limits before accepting
    if not await ws_manager.add_connection(job_id_str, websocket):
        await websocket.close(code=1013, reason="Connection limit exceeded")
        return

    await websocket.accept()
    logger.info(f"WebSocket connected for job {job_id_str}, total connections: {ws_manager.connection_count}")

    try:
        while True:
            # CRITICAL FIX: Get actual job status and send updates
            from services.job_manager import JobManager
            with get_db_session() as db:
                job_manager = JobManager(db)
                job_status = await job_manager.get_job_status(job_id)

                if job_status:
                    await websocket.send_json({
                        "job_id": str(job_id),
                        "status": job_status.status.value,
                        "progress": job_status.progress_percentage,
                        "current_task": job_status.current_task_type,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    })

                    # Stop sending updates if job is complete
                    if job_status.status.value in ["completed", "failed", "cancelled"]:
                        break

            await asyncio.sleep(2)  # Reduced from 5s for better responsiveness

    except Exception as e:
        logger.error(f"WebSocket error for job {job_id_str}: {e}")
    finally:
        # CRITICAL FIX: Always cleanup connection
        await ws_manager.remove_connection(job_id_str, websocket)
        try:
            await websocket.close()
        except:
            pass
        logger.info(f"WebSocket disconnected for job {job_id_str}, remaining connections: {ws_manager.connection_count}")



@app.post("/tasks/process")
async def process_task(task_data: Dict[str, Any], db = Depends(get_db)):
    """Process tasks from Cloud Tasks queues"""
    try:
        action = task_data.get("action")
        job_id = task_data.get("job_id")

        if action == "process_job" and job_id:
            # Handle job processing with TaskExecutor
            logger.info(
                "Processing job from queue",
                extra={
                    "job_id": job_id,
                    "queue": task_data.get('queue'),
                    "attempt": task_data.get('attempt', 1)
                }
            )

            task_executor = TaskExecutor(db)
            queue_name = task_data.get('queue')
            result = await task_executor.process_job(job_id, queue_name)

            status = "completed" if not result.get("error") else "failed"

            # Log the result for monitoring
            if status == "failed":
                logger.error(
                    "Job processing failed",
                    extra={
                        "job_id": job_id,
                        "queue": task_data.get('queue'),
                        "error": result.get("error"),
                        "attempt": task_data.get('attempt', 1)
                    }
                )
                # Let Cloud Tasks handle retries based on queue configuration
                # Return error status to trigger Cloud Tasks retry mechanism
                return JSONResponse(
                    status_code=500,
                    content={
                        "status": "failed",
                        "job_id": job_id,
                        "action": action,
                        "error": result.get("error"),
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )
            else:
                logger.info(
                    "Job processing completed successfully",
                    extra={
                        "job_id": job_id,
                        "queue": task_data.get('queue')
                    }
                )

                # Return minimal success response to Cloud Tasks
                # Large result data is already stored in GCS via TaskExecutor
                return JSONResponse(
                    status_code=200,
                    content={
                        "status": "success",
                        "job_id": job_id,
                        "action": action,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                        # Note: Full result data is stored in GCS, not returned here
                    }
                )

        elif action == "cancel_job" and job_id:
            # Handle job cancellation
            logger.info(
                "Processing job cancellation",
                extra={"job_id": job_id}
            )
            return {
                "status": "accepted",
                "job_id": job_id,
                "action": action,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }



        elif action == "update_progress" and job_id:
            # Handle progress updates
            progress = task_data.get("progress", {})
            print(f"Processing progress update for job {job_id}: {progress}")
            return {
                "status": "accepted",
                "job_id": job_id,
                "action": action,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        else:
            raise HTTPException(400, f"Unknown action: {action}")

    except Exception as e:
        logger.error(
            "Critical error in task processing endpoint",
            extra={
                "job_id": task_data.get("job_id"),
                "action": task_data.get("action"),
                "error": str(e),
                "queue": task_data.get('queue')
            },
            exc_info=True
        )

        # Check if job was actually completed despite the exception
        job_id = task_data.get("job_id")
        if job_id:
            try:
                job = db.query(Job).filter(Job.job_id == UUID(job_id)).first()
                if job and job.status in [JobStatus.COMPLETED.value, JobStatus.FAILED.value]:
                    # Job is already in final state, don't retry
                    logger.warning(
                        "Job already in final state despite exception, returning success to prevent retry",
                        extra={"job_id": job_id, "status": job.status}
                    )
                    return JSONResponse(
                        status_code=200,
                        content={
                            "status": "already_completed",
                            "job_id": job_id,
                            "final_status": job.status,
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        }
                    )
            except Exception as db_check_error:
                logger.error(f"Failed to check job status during exception handling: {db_check_error}")

        # Return 500 to trigger Cloud Tasks retry for genuinely failed jobs
        raise HTTPException(500, f"Task processing failed: {str(e)}")

@app.get("/analytics/jobs/stats")
async def get_job_statistics(db = Depends(get_db)):
    """Get job statistics for analytics"""
    from sqlalchemy import func
    from models import Job

    try:
        # Get job counts by status
        stats_query = db.query(
            Job.status,
            func.count(Job.job_id).label('count')
        ).group_by(Job.status).all()

        # Initialize counters
        total_jobs = 0
        completed_jobs = 0
        failed_jobs = 0
        pending_jobs = 0
        cancelled_jobs = 0

        # Process results
        for status, count in stats_query:
            total_jobs += count
            if status == 'completed':
                completed_jobs = count
            elif status == 'failed':
                failed_jobs = count
            elif status in ['queued', 'running']:
                pending_jobs += count
            elif status == 'cancelled':
                cancelled_jobs = count

        return {
            "total_jobs": total_jobs,
            "completed_jobs": completed_jobs,
            "failed_jobs": failed_jobs,
            "pending_jobs": pending_jobs,
            "cancelled_jobs": cancelled_jobs,
            "success_rate": (completed_jobs / total_jobs * 100) if total_jobs > 0 else 0,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get job statistics: {e}")
        raise HTTPException(500, f"Failed to retrieve job statistics: {str(e)}")

@app.get("/analytics/jobs/recent")
async def get_recent_jobs(
    limit: int = 10,
    status: Optional[JobStatus] = None,
    db = Depends(get_db)
):
    """Get recent jobs for analytics"""
    from models import Job

    try:
        query = db.query(Job).order_by(Job.created_at.desc())

        if status:
            query = query.filter(Job.status == status.value)

        jobs = query.limit(limit).all()

        job_list = []
        for job in jobs:
            job_list.append({
                "job_id": str(job.job_id),
                "job_type": job.job_type,
                "status": job.status,
                "created_at": job.created_at.isoformat() if job.created_at else None,
                "updated_at": job.updated_at.isoformat() if job.updated_at else None,
                "current_task_type": job.current_task_type,
                "workflow_progress_percentage": job.workflow_progress_percentage
            })

        return {
            "jobs": job_list,
            "count": len(job_list),
            "limit": limit,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get recent jobs: {e}")
        raise HTTPException(500, f"Failed to retrieve recent jobs: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": "job-management"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)  # Different port from main API
