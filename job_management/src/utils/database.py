"""
Database connection utilities and helper functions.

This module provides database connection management, session handling,
and common database operations for the job management system.
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
from typing import Generator
import logging

from config import database_config
from models import Base

logger = logging.getLogger(__name__)

# Database engine - initialized once
engine = None
SessionLocal = None


def init_database():
    """Initialize database engine and session factory with high-concurrency settings."""
    global engine, SessionLocal

    if engine is None:
        engine = create_engine(
            database_config.database_url,
            poolclass=QueuePool,
            pool_size=database_config.pool_size,
            max_overflow=database_config.max_overflow,
            pool_timeout=database_config.pool_timeout,
            pool_recycle=database_config.pool_recycle,
            # CRITICAL FIX: Add connection health and performance settings
            pool_pre_ping=database_config.pool_pre_ping,
            pool_reset_on_return=database_config.pool_reset_on_return,
            connect_args={
                "application_name": "job_management_service",
                "connect_timeout": 10
            },
            echo=False  # Set to True for SQL debugging
        )

        SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=engine,
            # CRITICAL FIX: Enable connection expiration for better resource management
            expire_on_commit=False
        )

        logger.info(f"Database engine initialized with pool_size={database_config.pool_size}, max_overflow={database_config.max_overflow}")


# Backward compatibility alias
create_tables = init_database


def get_db() -> Generator[Session, None, None]:
    """
    Database dependency for FastAPI.
    
    Yields a database session that is automatically closed after use.
    """
    if SessionLocal is None:
        init_database()
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """
    Context manager for database sessions with proper transaction handling.

    Usage:
        with get_db_session() as db:
            # Use db session
            pass
    """
    if SessionLocal is None:
        init_database()

    db = SessionLocal()
    try:
        yield db
        # CRITICAL FIX: Explicit commit only if no exception
        db.commit()
    except Exception as e:
        # CRITICAL FIX: Always rollback on exception
        db.rollback()
        logger.error(f"Database transaction failed, rolled back: {e}")
        raise
    finally:
        # CRITICAL FIX: Ensure connection is returned to pool
        try:
            db.close()
        except Exception as e:
            logger.error(f"Error closing database session: {e}")


def health_check() -> bool:
    """
    Check database connectivity.
    
    Returns:
        bool: True if database is accessible, False otherwise
    """
    try:
        with get_db_session() as db:
            db.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False


def close_db_connections():
    """Close all database connections."""
    global engine
    if engine:
        engine.dispose()
        logger.info("Database connections closed")
