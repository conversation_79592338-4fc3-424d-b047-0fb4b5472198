"""
Robust HTTP client utilities with proper timeout and retry handling.

This module provides HTTP client functionality with:
- Tiered timeout configuration (connection, read, total)
- Intelligent retry strategies based on HTTP status codes
- Proper handling of rate limiting (429) with Retry-After
- Exponential backoff for transient failures
"""

import asyncio
import json
import logging
import os
import time
from typing import Dict, Any, Optional, Union
import aiohttp
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

logger = logging.getLogger(__name__)

# Timeout configurations (in seconds)
# Increased defaults to handle long ElevenLabs voice-over generation (4000+ character scripts)
HTTP_TIMEOUTS = {
    "connection": int(os.getenv("HTTP_CONNECTION_TIMEOUT", "30")),     # 30 seconds for connection
    "read": int(os.getenv("HTTP_READ_TIMEOUT", "900")),               # 15 minutes for read (was 5 min)
    "total": int(os.getenv("HTTP_TOTAL_TIMEOUT", "1800")),            # 30 minutes total operation
    "download": int(os.getenv("HTTP_DOWNLOAD_TIMEOUT", "600"))        # 10 minutes for downloads
}


class HTTPClientError(Exception):
    """Base exception for HTTP client errors."""
    def __init__(self, message: str, status_code: Optional[int] = None, response_text: Optional[str] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_text = response_text


class HTTPClientTimeoutError(HTTPClientError):
    """Exception for timeout errors."""
    pass


class HTTPRateLimitError(HTTPClientError):
    """Exception for rate limit errors (429)."""
    def __init__(self, message: str, retry_after: Optional[int] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.retry_after = retry_after


def create_robust_session() -> requests.Session:
    """
    Create a requests.Session with robust retry configuration.
    
    Returns:
        Configured requests.Session with retry strategy
    """
    session = requests.Session()
    
    # Configure retry strategy (simplified)
    retry_strategy = Retry(
        total=2,  # Reduced from 5 - just handle immediate network issues
        backoff_factor=1,  # Exponential backoff: 1, 2 seconds
        status_forcelist=[429, 500, 502, 503, 504],  # Retry on these status codes
        allowed_methods=["GET", "POST", "PUT", "PATCH"],  # Allow retries on these methods
        respect_retry_after_header=True,  # Respect Retry-After header for 429
        raise_on_status=False  # Don't raise exception on retry exhaustion
    )
    
    # Mount adapter with retry strategy
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    return session


async def robust_http_request(
    method: str,
    url: str,
    timeout_type: str = "read",
    max_retries: int = 1,  # Reduced from 3 - let Cloud Tasks handle most retries
    **kwargs
) -> aiohttp.ClientResponse:
    """
    Make HTTP request with robust timeout and retry handling using aiohttp.
    
    Args:
        method: HTTP method (GET, POST, etc.)
        url: Request URL
        timeout_type: Type of timeout to use from HTTP_TIMEOUTS
        max_retries: Maximum number of retries for rate limiting
        **kwargs: Additional arguments for aiohttp request
        
    Returns:
        aiohttp.ClientResponse object
        
    Raises:
        HTTPClientError: For client errors (4xx)
        HTTPClientTimeoutError: For timeout errors
        HTTPRateLimitError: For rate limit errors (429)
    """
    # Configure timeout
    timeout = aiohttp.ClientTimeout(
        connect=HTTP_TIMEOUTS["connection"],
        sock_read=HTTP_TIMEOUTS[timeout_type],
        total=HTTP_TIMEOUTS["total"]
    )
    
    retry_count = 0
    while retry_count <= max_retries:
        try:
            async with aiohttp.ClientSession(timeout=timeout) as session:
                logger.info(f"Making {method} request to {url} (attempt {retry_count + 1}/{max_retries + 1})")
                
                async with session.request(method, url, **kwargs) as response:
                    # Handle rate limiting (429)
                    if response.status == 429:
                        if retry_count >= max_retries:
                            retry_after = response.headers.get('Retry-After')
                            raise HTTPRateLimitError(
                                f"Rate limit exceeded after {max_retries} retries",
                                retry_after=int(retry_after) if retry_after else None,
                                status_code=429
                            )
                        
                        # Extract Retry-After header
                        retry_after = response.headers.get('Retry-After', '60')
                        try:
                            wait_time = int(retry_after)
                        except ValueError:
                            wait_time = 60  # Default to 60 seconds
                        
                        logger.warning(f"Rate limited (429), waiting {wait_time} seconds before retry")
                        await asyncio.sleep(wait_time)
                        retry_count += 1
                        continue
                    
                    # Handle other client errors (4xx) - don't retry
                    elif 400 <= response.status < 500:
                        response_text = await response.text()
                        error_msg = f"Client error {response.status}: {response_text}"
                        logger.error(error_msg)
                        raise HTTPClientError(error_msg, response.status, response_text)
                    
                    # Handle server errors (5xx) - will be retried by aiohttp connector
                    elif response.status >= 500:
                        response_text = await response.text()
                        error_msg = f"Server error {response.status}: {response_text}"
                        logger.error(error_msg)
                        if retry_count >= max_retries:
                            raise HTTPClientError(error_msg, response.status, response_text)
                        
                        # Exponential backoff for server errors
                        wait_time = min(2 ** retry_count, 60)  # Cap at 60 seconds
                        logger.warning(f"Server error, waiting {wait_time} seconds before retry")
                        await asyncio.sleep(wait_time)
                        retry_count += 1
                        continue
                    
                    # Success case
                    return response
                    
        except asyncio.TimeoutError as e:
            error_msg = f"Request timeout after {timeout.total} seconds: {str(e)}"
            logger.error(error_msg)
            raise HTTPClientTimeoutError(error_msg) from e
        except aiohttp.ClientError as e:
            error_msg = f"HTTP client error: {str(e)}"
            logger.error(error_msg)
            if retry_count >= max_retries:
                raise HTTPClientError(error_msg) from e
            
            # Exponential backoff for connection errors
            wait_time = min(2 ** retry_count, 60)
            logger.warning(f"Connection error, waiting {wait_time} seconds before retry")
            await asyncio.sleep(wait_time)
            retry_count += 1
    
    # Should not reach here, but just in case
    raise HTTPClientError(f"Request failed after {max_retries + 1} attempts")


async def robust_download(
    url: str,
    max_retries: int = 1,  # Reduced from 3 - let Cloud Tasks handle retries
    **kwargs
) -> bytes:
    """
    Download file with robust retry handling.

    Args:
        url: Download URL
        max_retries: Maximum number of retries
        **kwargs: Additional arguments for aiohttp request

    Returns:
        Downloaded file content as bytes

    Raises:
        HTTPClientError: For download errors
        HTTPClientTimeoutError: For timeout errors
    """
    # Configure timeout for downloads
    timeout = aiohttp.ClientTimeout(
        connect=HTTP_TIMEOUTS["connection"],
        sock_read=HTTP_TIMEOUTS["download"],
        total=HTTP_TIMEOUTS["total"]
    )

    retry_count = 0
    while retry_count <= max_retries:
        try:
            async with aiohttp.ClientSession(timeout=timeout) as session:
                logger.info(f"Downloading from {url} (attempt {retry_count + 1}/{max_retries + 1})")

                async with session.get(url, **kwargs) as response:
                    # Handle rate limiting (429)
                    if response.status == 429:
                        if retry_count >= max_retries:
                            retry_after = response.headers.get('Retry-After')
                            raise HTTPRateLimitError(
                                f"Rate limit exceeded during download after {max_retries} retries",
                                retry_after=int(retry_after) if retry_after else None,
                                status_code=429
                            )

                        # Extract Retry-After header
                        retry_after = response.headers.get('Retry-After', '60')
                        try:
                            wait_time = int(retry_after)
                        except ValueError:
                            wait_time = 60  # Default to 60 seconds

                        logger.warning(f"Rate limited during download (429), waiting {wait_time} seconds before retry")
                        await asyncio.sleep(wait_time)
                        retry_count += 1
                        continue

                    # Handle other client errors (4xx) - don't retry
                    elif 400 <= response.status < 500:
                        response_text = await response.text()
                        error_msg = f"Download failed with client error {response.status}: {response_text}"
                        logger.error(error_msg)
                        raise HTTPClientError(error_msg, response.status, response_text)

                    # Handle server errors (5xx) - retry
                    elif response.status >= 500:
                        response_text = await response.text()
                        error_msg = f"Download failed with server error {response.status}: {response_text}"
                        logger.error(error_msg)
                        if retry_count >= max_retries:
                            raise HTTPClientError(error_msg, response.status, response_text)

                        # Exponential backoff for server errors
                        wait_time = min(2 ** retry_count, 60)  # Cap at 60 seconds
                        logger.warning(f"Server error during download, waiting {wait_time} seconds before retry")
                        await asyncio.sleep(wait_time)
                        retry_count += 1
                        continue

                    # Check if response is successful
                    elif response.status != 200:
                        error_msg = f"Download failed with status {response.status}: {await response.text()}"
                        logger.error(error_msg)
                        raise HTTPClientError(error_msg, response.status)

                    # Read content within the same session context
                    content = await response.read()

                    # Validate content
                    if not content:
                        error_msg = f"Downloaded content is empty from {url}"
                        logger.error(error_msg)
                        raise HTTPClientError(error_msg)

                    logger.info(f"Successfully downloaded {len(content)} bytes from {url}")
                    return content

        except asyncio.TimeoutError as e:
            error_msg = f"Download timeout after {timeout.total} seconds: {str(e)}"
            logger.error(error_msg)
            if retry_count >= max_retries:
                raise HTTPClientTimeoutError(error_msg) from e

            # Exponential backoff for timeout errors
            wait_time = min(2 ** retry_count, 60)
            logger.warning(f"Download timeout, waiting {wait_time} seconds before retry")
            await asyncio.sleep(wait_time)
            retry_count += 1

        except aiohttp.ClientError as e:
            error_msg = f"HTTP client error during download: {str(e)}"
            logger.error(error_msg)
            if retry_count >= max_retries:
                raise HTTPClientError(error_msg) from e

            # Exponential backoff for connection errors
            wait_time = min(2 ** retry_count, 60)
            logger.warning(f"Connection error during download, waiting {wait_time} seconds before retry")
            await asyncio.sleep(wait_time)
            retry_count += 1

        except (HTTPClientError, HTTPClientTimeoutError, HTTPRateLimitError):
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            error_msg = f"Unexpected error during download from {url}: {str(e)}"
            logger.error(error_msg)
            if retry_count >= max_retries:
                raise HTTPClientError(error_msg) from e

            # Exponential backoff for unexpected errors
            wait_time = min(2 ** retry_count, 60)
            logger.warning(f"Unexpected error during download, waiting {wait_time} seconds before retry")
            await asyncio.sleep(wait_time)
            retry_count += 1

    # Should not reach here, but just in case
    raise HTTPClientError(f"Download failed after {max_retries + 1} attempts")


async def robust_json_request(
    method: str,
    url: str,
    timeout_type: str = "read",
    max_retries: int = 1,  # Reduced from 3 - let Cloud Tasks handle retries
    **kwargs
) -> Dict[str, Any]:
    """
    Make HTTP request and return JSON response with robust retry handling.

    Args:
        method: HTTP method (GET, POST, etc.)
        url: Request URL
        timeout_type: Type of timeout to use from HTTP_TIMEOUTS
        max_retries: Maximum number of retries
        **kwargs: Additional arguments for aiohttp request

    Returns:
        Dict[str, Any]: JSON response data

    Raises:
        HTTPClientError: For client errors (4xx) or JSON parsing errors
        HTTPClientTimeoutError: For timeout errors
        HTTPRateLimitError: For rate limit errors (429)
    """
    # Configure timeout
    timeout = aiohttp.ClientTimeout(
        connect=HTTP_TIMEOUTS["connection"],
        sock_read=HTTP_TIMEOUTS[timeout_type],
        total=HTTP_TIMEOUTS["total"]
    )

    retry_count = 0
    while retry_count <= max_retries:
        try:
            async with aiohttp.ClientSession(timeout=timeout) as session:
                logger.info(f"Making {method} JSON request to {url} (attempt {retry_count + 1}/{max_retries + 1})")

                async with session.request(method, url, **kwargs) as response:
                    # Handle rate limiting (429)
                    if response.status == 429:
                        if retry_count >= max_retries:
                            retry_after = response.headers.get('Retry-After')
                            raise HTTPRateLimitError(
                                f"Rate limit exceeded during JSON request after {max_retries} retries",
                                retry_after=int(retry_after) if retry_after else None,
                                status_code=429
                            )

                        # Extract Retry-After header
                        retry_after = response.headers.get('Retry-After', '60')
                        try:
                            wait_time = int(retry_after)
                        except ValueError:
                            wait_time = 60  # Default to 60 seconds

                        logger.warning(f"Rate limited during JSON request (429), waiting {wait_time} seconds before retry")
                        await asyncio.sleep(wait_time)
                        retry_count += 1
                        continue

                    # Handle other client errors (4xx) - don't retry
                    elif 400 <= response.status < 500:
                        response_text = await response.text()
                        error_msg = f"JSON request failed with client error {response.status}: {response_text}"
                        logger.error(error_msg)
                        raise HTTPClientError(error_msg, response.status, response_text)

                    # Handle server errors (5xx) - retry
                    elif response.status >= 500:
                        response_text = await response.text()
                        error_msg = f"JSON request failed with server error {response.status}: {response_text}"
                        logger.error(error_msg)
                        if retry_count >= max_retries:
                            raise HTTPClientError(error_msg, response.status, response_text)

                        # Exponential backoff for server errors
                        wait_time = min(2 ** retry_count, 60)  # Cap at 60 seconds
                        logger.warning(f"Server error during JSON request, waiting {wait_time} seconds before retry")
                        await asyncio.sleep(wait_time)
                        retry_count += 1
                        continue

                    # Check if response is successful
                    elif response.status != 200:
                        response_text = await response.text()
                        error_msg = f"JSON request failed with status {response.status}: {response_text}"
                        logger.error(error_msg)
                        raise HTTPClientError(error_msg, response.status, response_text)

                    # Read and parse JSON content within the same session context
                    try:
                        json_data = await response.json()
                        logger.info(f"Successfully received JSON response from {url}")
                        return json_data
                    except (aiohttp.ContentTypeError, json.JSONDecodeError) as e:
                        error_msg = f"Failed to parse JSON response from {url}: {str(e)}"
                        logger.error(error_msg)
                        raise HTTPClientError(error_msg) from e

        except asyncio.TimeoutError as e:
            error_msg = f"JSON request timeout after {timeout.total} seconds: {str(e)}"
            logger.error(error_msg)
            if retry_count >= max_retries:
                raise HTTPClientTimeoutError(error_msg) from e

            # Exponential backoff for timeout errors
            wait_time = min(2 ** retry_count, 60)
            logger.warning(f"JSON request timeout, waiting {wait_time} seconds before retry")
            await asyncio.sleep(wait_time)
            retry_count += 1

        except aiohttp.ClientError as e:
            error_msg = f"HTTP client error during JSON request: {str(e)}"
            logger.error(error_msg)
            if retry_count >= max_retries:
                raise HTTPClientError(error_msg) from e

            # Exponential backoff for connection errors
            wait_time = min(2 ** retry_count, 60)
            logger.warning(f"Connection error during JSON request, waiting {wait_time} seconds before retry")
            await asyncio.sleep(wait_time)
            retry_count += 1

        except (HTTPClientError, HTTPClientTimeoutError, HTTPRateLimitError):
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            error_msg = f"Unexpected error during JSON request to {url}: {str(e)}"
            logger.error(error_msg)
            if retry_count >= max_retries:
                raise HTTPClientError(error_msg) from e

            # Exponential backoff for unexpected errors
            wait_time = min(2 ** retry_count, 60)
            logger.warning(f"Unexpected error during JSON request, waiting {wait_time} seconds before retry")
            await asyncio.sleep(wait_time)
            retry_count += 1

    # Should not reach here, but just in case
    raise HTTPClientError(f"JSON request failed after {max_retries + 1} attempts")


class RobustWebSocketClient:
    """WebSocket client with robust timeout and error handling."""
    
    def __init__(self, url: str, timeout_seconds: int = HTTP_TIMEOUTS["total"]):
        self.url = url
        self.timeout = aiohttp.ClientTimeout(total=timeout_seconds)
    
    async def connect_and_process(self, payload: Dict[str, Any], message_handler) -> Any:
        """
        Connect to WebSocket and process messages with timeout.
        
        Args:
            payload: JSON payload to send
            message_handler: Async function to handle incoming messages
            
        Returns:
            Result from message_handler
            
        Raises:
            HTTPClientTimeoutError: For timeout errors
            HTTPClientError: For connection errors
        """
        try:
            logger.info(f"Connecting to WebSocket: {self.url}")
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                async with session.ws_connect(self.url) as ws:
                    # Send initial payload
                    await ws.send_json(payload)
                    logger.info(f"Sent WebSocket payload: {payload}")
                    
                    # Process messages
                    return await message_handler(ws)
                    
        except asyncio.TimeoutError as e:
            error_msg = f"WebSocket timeout after {self.timeout.total} seconds: {str(e)}"
            logger.error(error_msg)
            raise HTTPClientTimeoutError(error_msg) from e
        except aiohttp.ClientError as e:
            error_msg = f"WebSocket connection error: {str(e)}"
            logger.error(error_msg)
            raise HTTPClientError(error_msg) from e
