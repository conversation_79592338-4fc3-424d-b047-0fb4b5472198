"""
Music Generation Job Management Service

This is an independent microservice for managing music generation jobs,
workflows, and task orchestration with intelligent queue management.

Refactored Architecture:
- models.py: Database models (Job, MusicGPTUsage, etc.)
- schemas.py: Pydantic request/response models and enums
- config.py: Configuration classes for storage, queues, database
- services/: Business logic services (JobManager, QueueManager, etc.)
- utils/: Utility modules (database connection, helpers)
- main.py: FastAPI application and route definitions
"""

from .main import app
from .config import storage_config, queue_config, database_config, app_config
from .models import Base, Job, MusicGPTUsage, ActiveMusicGeneration, RateLimit
from .schemas import (
    JobType, JobStatus, TaskType,
    JobSubmissionRequest, JobResponse,
    TaskConfig
)

__version__ = "1.0.0"
__all__ = [
    "app",
    "storage_config", "queue_config", "database_config", "app_config",
    "Base", "Job", "MusicGPTUsage", "ActiveMusicGeneration", "RateLimit",
    "JobType", "JobStatus", "TaskType",
    "JobSubmissionRequest", "JobResponse",
    "TaskConfig"
]
