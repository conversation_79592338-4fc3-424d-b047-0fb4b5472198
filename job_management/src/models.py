"""
Database models for the Music Generation Job Management system.

This module contains all SQLAlchemy database models used for job tracking,
and rate limiting.
"""

from sqlalchemy import Column, String, DateTime, Boolean, Integer, Text
from sqlalchemy.dialects.postgresql import UUID as PGUUID, JSONB
from sqlalchemy.ext.declarative import declarative_base
from uuid import uuid4
from datetime import datetime

Base = declarative_base()


class Job(Base):
    """Job tracking model for music generation tasks and workflows."""
    __tablename__ = "jobs"

    job_id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    job_type = Column(String(20), nullable=False)
    status = Column(String(20), nullable=False, default="queued")
    configuration = Column(JSONB, default=dict)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    client_ip = Column(String(45))
    user_agent = Column(Text)
    # Workflow tracking fields (added in database_schema.sql)
    current_task_type = Column(String(50))
    current_task_index = Column(Integer)
    workflow_progress_percentage = Column(Integer, default=0)



class MusicGPTUsage(Base):
    """Rate limiting tracker for MusicGPT API usage."""

    __tablename__ = "musicgpt_usage_tracker"

    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    endpoint_type = Column(String(50), nullable=False)  # musicai, status_check
    period_type = Column(String(10), nullable=False)    # minute, concurrent
    window_start = Column(DateTime, nullable=False)
    window_end = Column(DateTime, nullable=False)
    request_count = Column(Integer, default=0)
    updated_at = Column(DateTime, default=datetime.utcnow)


class ActiveMusicGeneration(Base):
    """Tracks active music generation tasks for concurrent limit enforcement."""

    __tablename__ = "active_music_generation"

    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    workflow_task_execution_id = Column(PGUUID(as_uuid=True), nullable=False, unique=True)
    musicgpt_task_id = Column(String(255))
    websocket_connection_id = Column(String(255))
    started_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)


class RateLimit(Base):
    """General rate limiting model for various API endpoints and identifiers."""

    __tablename__ = "rate_limits"

    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    identifier = Column(String(255), nullable=False)  # IP address, etc.
    limit_type = Column(String(50), nullable=False)   # ip_minute, ip_hour, etc.
    window_start = Column(DateTime, nullable=False)
    window_end = Column(DateTime, nullable=False)
    request_count = Column(Integer, default=0)
    updated_at = Column(DateTime, default=datetime.utcnow)


class WorkflowTaskExecution(Base):
    """Tracks execution of individual tasks within workflow jobs.

    Note: The actual database schema is defined in database_schema.sql.
    This SQLAlchemy model should match that schema.
    """

    __tablename__ = "workflow_task_executions"

    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    job_id = Column(PGUUID(as_uuid=True), nullable=False)
    task_type = Column(String(50), nullable=False)
    task_index = Column(Integer, nullable=False)
    status = Column(String(20), nullable=False, default="queued")
    parameters = Column(JSONB)
    result = Column(JSONB)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    error_message = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)



class ActiveElevenLabs(Base):
    """Tracks active ElevenLabs tasks for concurrent limit enforcement."""

    __tablename__ = "active_elevenlabs"

    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    workflow_task_execution_id = Column(PGUUID(as_uuid=True), nullable=False, unique=True)
    started_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
