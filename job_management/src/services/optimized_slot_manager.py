"""
Optimized Slot Management System with Redis caching and batch operations.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from sqlalchemy.orm import Session
from sqlalchemy import text
import redis.asyncio as redis
import json

logger = logging.getLogger(__name__)

class OptimizedSlotManager:
    """High-performance slot management with Redis caching and batch operations."""
    
    def __init__(self, db: Session, redis_url: str = "redis://localhost:6379"):
        self.db = db
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        
        # Slot limits
        self.musicgpt_max_slots = 10
        self.elevenlabs_max_slots = 10
        
        # Cache keys
        self.musicgpt_slots_key = "slots:musicgpt:active"
        self.elevenlabs_slots_key = "slots:elevenlabs:active"
        self.slot_metadata_key = "slots:metadata"
        
        # Cleanup intervals
        self.cleanup_interval = timedelta(minutes=5)  # More frequent cleanup
        self.slot_timeout = timedelta(minutes=20)
        
        # Background cleanup task
        self._cleanup_task = None
        self._start_background_cleanup()
    
    def _start_background_cleanup(self):
        """Start background cleanup task."""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._background_cleanup())
    
    async def _background_cleanup(self):
        """Background task to clean up stale slots."""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval.total_seconds())
                await self._cleanup_stale_slots()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in background cleanup: {e}")
    
    async def _cleanup_stale_slots(self):
        """Clean up stale slots from both Redis and database."""
        cutoff_time = datetime.utcnow() - self.slot_timeout
        
        # Get all active slots from Redis
        musicgpt_slots = await self.redis_client.smembers(self.musicgpt_slots_key)
        elevenlabs_slots = await self.redis_client.smembers(self.elevenlabs_slots_key)
        
        stale_musicgpt = []
        stale_elevenlabs = []
        
        # Check metadata for stale slots
        for slot_id in musicgpt_slots:
            metadata = await self.redis_client.hget(self.slot_metadata_key, slot_id)
            if metadata:
                slot_data = json.loads(metadata)
                started_at = datetime.fromisoformat(slot_data['started_at'])
                if started_at < cutoff_time:
                    stale_musicgpt.append(slot_id)
        
        for slot_id in elevenlabs_slots:
            metadata = await self.redis_client.hget(self.slot_metadata_key, slot_id)
            if metadata:
                slot_data = json.loads(metadata)
                started_at = datetime.fromisoformat(slot_data['started_at'])
                if started_at < cutoff_time:
                    stale_elevenlabs.append(slot_id)
        
        # Clean up stale slots
        if stale_musicgpt or stale_elevenlabs:
            await self._batch_release_slots(stale_musicgpt, stale_elevenlabs)
            logger.info(f"Cleaned up {len(stale_musicgpt)} MusicGPT and {len(stale_elevenlabs)} ElevenLabs stale slots")
    
    async def _batch_release_slots(self, musicgpt_slots: List[str], elevenlabs_slots: List[str]):
        """Release multiple slots in batch operations."""
        pipe = self.redis_client.pipeline()
        
        # Remove from Redis sets
        if musicgpt_slots:
            pipe.srem(self.musicgpt_slots_key, *musicgpt_slots)
        if elevenlabs_slots:
            pipe.srem(self.elevenlabs_slots_key, *elevenlabs_slots)
        
        # Remove metadata
        all_slots = musicgpt_slots + elevenlabs_slots
        if all_slots:
            pipe.hdel(self.slot_metadata_key, *all_slots)
        
        await pipe.execute()
        
        # Update database in batch
        if musicgpt_slots:
            await self._batch_update_db_slots("active_music_generation", musicgpt_slots, False)
        if elevenlabs_slots:
            await self._batch_update_db_slots("active_elevenlabs", elevenlabs_slots, False)
    
    async def _batch_update_db_slots(self, table_name: str, slot_ids: List[str], is_active: bool):
        """Update multiple database slots in a single query."""
        if not slot_ids:
            return
        
        try:
            placeholders = ','.join([f"'{slot_id}'" for slot_id in slot_ids])
            query = text(f"""
                UPDATE {table_name}
                SET is_active = :is_active, updated_at = :now
                WHERE workflow_task_execution_id IN ({placeholders})
            """)
            
            self.db.execute(query, {
                "is_active": is_active,
                "now": datetime.utcnow()
            })
            self.db.commit()
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error batch updating {table_name} slots: {e}")
    
    # ============================================================================
    # MUSICGPT SLOT MANAGEMENT
    # ============================================================================
    
    async def acquire_musicgpt_slot(self, task_id: str) -> bool:
        """Acquire MusicGPT slot with Redis-backed fast checking."""
        try:
            # Fast check: Get current count from Redis
            current_count = await self.redis_client.scard(self.musicgpt_slots_key)
            
            if current_count >= self.musicgpt_max_slots:
                logger.warning(f"🚫 MusicGPT slots full: {current_count}/{self.musicgpt_max_slots}")
                return False
            
            # Try to add to Redis set (atomic operation)
            added = await self.redis_client.sadd(self.musicgpt_slots_key, task_id)
            
            if not added:  # Slot already exists
                logger.info(f"🔄 MusicGPT slot already active for task {task_id}")
                return True
            
            # Check if we exceeded limit after adding
            new_count = await self.redis_client.scard(self.musicgpt_slots_key)
            if new_count > self.musicgpt_max_slots:
                # Remove the slot we just added
                await self.redis_client.srem(self.musicgpt_slots_key, task_id)
                logger.warning(f"🚫 MusicGPT slot limit exceeded, removed task {task_id}")
                return False
            
            # Store metadata
            metadata = {
                "task_id": task_id,
                "started_at": datetime.utcnow().isoformat(),
                "service": "musicgpt"
            }
            await self.redis_client.hset(
                self.slot_metadata_key, 
                task_id, 
                json.dumps(metadata)
            )
            
            # Update database (async, non-blocking)
            asyncio.create_task(self._update_db_slot("active_music_generation", task_id, True))
            
            logger.info(f"✅ MusicGPT slot acquired for task {task_id} ({new_count}/{self.musicgpt_max_slots})")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to acquire MusicGPT slot for task {task_id}: {e}")
            # Clean up on error
            await self.redis_client.srem(self.musicgpt_slots_key, task_id)
            return False
    
    async def release_musicgpt_slot(self, task_id: str):
        """Release MusicGPT slot."""
        try:
            # Remove from Redis
            removed = await self.redis_client.srem(self.musicgpt_slots_key, task_id)
            await self.redis_client.hdel(self.slot_metadata_key, task_id)
            
            if removed:
                # Update database (async, non-blocking)
                asyncio.create_task(self._update_db_slot("active_music_generation", task_id, False))
                logger.info(f"✅ MusicGPT slot released for task {task_id}")
            else:
                logger.warning(f"⚠️ MusicGPT slot not found for task {task_id}")
                
        except Exception as e:
            logger.error(f"❌ Failed to release MusicGPT slot for task {task_id}: {e}")
    
    # ============================================================================
    # ELEVENLABS SLOT MANAGEMENT
    # ============================================================================
    
    async def acquire_elevenlabs_slot(self, task_id: str) -> bool:
        """Acquire ElevenLabs slot with Redis-backed fast checking."""
        try:
            current_count = await self.redis_client.scard(self.elevenlabs_slots_key)
            
            if current_count >= self.elevenlabs_max_slots:
                logger.warning(f"🚫 ElevenLabs slots full: {current_count}/{self.elevenlabs_max_slots}")
                return False
            
            added = await self.redis_client.sadd(self.elevenlabs_slots_key, task_id)
            
            if not added:
                logger.info(f"🔄 ElevenLabs slot already active for task {task_id}")
                return True
            
            new_count = await self.redis_client.scard(self.elevenlabs_slots_key)
            if new_count > self.elevenlabs_max_slots:
                await self.redis_client.srem(self.elevenlabs_slots_key, task_id)
                logger.warning(f"🚫 ElevenLabs slot limit exceeded, removed task {task_id}")
                return False
            
            # Store metadata
            metadata = {
                "task_id": task_id,
                "started_at": datetime.utcnow().isoformat(),
                "service": "elevenlabs"
            }
            await self.redis_client.hset(
                self.slot_metadata_key, 
                task_id, 
                json.dumps(metadata)
            )
            
            # Update database (async, non-blocking)
            asyncio.create_task(self._update_db_slot("active_elevenlabs", task_id, True))
            
            logger.info(f"✅ ElevenLabs slot acquired for task {task_id} ({new_count}/{self.elevenlabs_max_slots})")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to acquire ElevenLabs slot for task {task_id}: {e}")
            await self.redis_client.srem(self.elevenlabs_slots_key, task_id)
            return False
    
    async def release_elevenlabs_slot(self, task_id: str):
        """Release ElevenLabs slot."""
        try:
            removed = await self.redis_client.srem(self.elevenlabs_slots_key, task_id)
            await self.redis_client.hdel(self.slot_metadata_key, task_id)
            
            if removed:
                asyncio.create_task(self._update_db_slot("active_elevenlabs", task_id, False))
                logger.info(f"✅ ElevenLabs slot released for task {task_id}")
            else:
                logger.warning(f"⚠️ ElevenLabs slot not found for task {task_id}")
                
        except Exception as e:
            logger.error(f"❌ Failed to release ElevenLabs slot for task {task_id}: {e}")
    
    # ============================================================================
    # DATABASE OPERATIONS
    # ============================================================================
    
    async def _update_db_slot(self, table_name: str, task_id: str, is_active: bool):
        """Update database slot status (async, non-blocking)."""
        try:
            if is_active:
                # Insert or update to active
                query = text(f"""
                    INSERT INTO {table_name} (workflow_task_execution_id, started_at, is_active)
                    VALUES (:task_id, :now, true)
                    ON CONFLICT (workflow_task_execution_id) DO UPDATE
                    SET is_active = true, started_at = EXCLUDED.started_at, updated_at = :now
                """)
            else:
                # Update to inactive
                query = text(f"""
                    UPDATE {table_name}
                    SET is_active = false, updated_at = :now
                    WHERE workflow_task_execution_id = :task_id
                """)
            
            self.db.execute(query, {"task_id": task_id, "now": datetime.utcnow()})
            self.db.commit()
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating {table_name} slot for task {task_id}: {e}")
    
    # ============================================================================
    # MONITORING AND STATS
    # ============================================================================
    
    async def get_slot_stats(self) -> Dict[str, any]:
        """Get current slot usage statistics."""
        try:
            musicgpt_count = await self.redis_client.scard(self.musicgpt_slots_key)
            elevenlabs_count = await self.redis_client.scard(self.elevenlabs_slots_key)
            
            musicgpt_slots = await self.redis_client.smembers(self.musicgpt_slots_key)
            elevenlabs_slots = await self.redis_client.smembers(self.elevenlabs_slots_key)
            
            return {
                "musicgpt": {
                    "active": musicgpt_count,
                    "max": self.musicgpt_max_slots,
                    "utilization": f"{(musicgpt_count/self.musicgpt_max_slots)*100:.1f}%",
                    "active_tasks": list(musicgpt_slots)
                },
                "elevenlabs": {
                    "active": elevenlabs_count,
                    "max": self.elevenlabs_max_slots,
                    "utilization": f"{(elevenlabs_count/self.elevenlabs_max_slots)*100:.1f}%",
                    "active_tasks": list(elevenlabs_slots)
                }
            }
        except Exception as e:
            logger.error(f"Error getting slot stats: {e}")
            return {"error": str(e)}
    
    async def force_cleanup(self):
        """Force immediate cleanup of all stale slots."""
        await self._cleanup_stale_slots()
    
    async def close(self):
        """Clean up resources."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        await self.redis_client.close()
