"""
Task Executor service for processing different types of tasks.

This service handles the actual execution of tasks like music generation,
album cover generation, and prompt enhancement by calling the appropriate
API services and updating job status.
"""

import os
import logging
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
from uuid import UUID
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

from models import Job
from schemas import JobStatus
from services.storage_manager import StorageManager
from services.workflow_tracker import WorkflowTracker
from services.workflow_context import WorkflowContextManager
from services.task_utils import (
    should_requeue_for_resource,
    handle_task_requeue,
    complete_and_store_result,
    get_task_execution_id,
)
# Re-export executor functions for modular organization (backward-compatible)
from services.executors.musicgpt import (
    execute_music_generation,
    execute_music_generation_with_slots,
    execute_sound_generation,
    execute_sound_generation_with_slots,
    execute_music_extension,
    execute_music_extension_with_slots,
    execute_music_remixing,
    execute_music_remixing_with_slots,
    execute_vocal_extraction,
    execute_vocal_extraction_with_slots,
)
from services.executors.content import (
    execute_prompt_enhancement,
    execute_text_translation,
    execute_spoken_word_script,
    execute_artist_generation,
)
from services.executors.media import (
    execute_album_cover_generation,
)
from services.executors.elevenlabs import (
    execute_voice_generation,
    execute_voice_over_generation_with_slots,
    execute_audio_translation_with_slots,
)
from services.executors.transcoder import (
    execute_transcoder_pipeline,
)





class TaskExecutor:
    """Executes different types of tasks and updates job status."""

    def __init__(self, db: Session):
        self.db = db
        # Use environment variable for API URL, fallback to docker network for local dev
        self.api_base_url = os.getenv('API_BASE_URL', 'http://api:8000')
        self.storage_manager = StorageManager()
        self.context_manager = WorkflowContextManager(db)

    async def process_job(self, job_id: str, queue_name: str = None) -> Dict[str, Any]:
        """Process a job by executing its tasks and updating status.

        Args:
            job_id: The job ID to process
            queue_name: The queue this job came from (for queue-specific processing)
        """
        try:
            # Get job from database
            job = self.db.query(Job).filter(Job.job_id == UUID(job_id)).first()
            if not job:
                return {"error": "Job not found", "job_id": job_id}

            # Update job status to running
            job.status = JobStatus.RUNNING.value
            job.updated_at = datetime.now(timezone.utc)
            self.db.commit()

            # Log queue information for debugging
            if queue_name:
                logger.info(f"Processing job {job_id} from queue: {queue_name}")

            # Process workflow job (all jobs are now workflows)
            if job.job_type == "workflow":
                result = await self._process_workflow(job, queue_name)
            else:
                result = {"error": f"Unknown job type: {job.job_type}"}

            # Update final job status with transaction safety
            try:
                if result.get("error"):
                    job.status = JobStatus.FAILED.value
                elif result.get("status") == "requeued":
                    # CRITICAL FIX: Do not mark job as completed when requeued
                    # Job remains in RUNNING state and will be processed again
                    logger.info(f"Job {job_id} requeued, keeping status as RUNNING")
                    job.updated_at = datetime.now(timezone.utc)
                    self.db.commit()
                    return result  # Return early, do not mark as completed

                else:
                    job.status = JobStatus.COMPLETED.value

                job.updated_at = datetime.now(timezone.utc)
                self.db.commit()

                # Log successful status update
                logger.info(
                    f"Job status updated to {job.status}",
                    extra={"job_id": job_id, "status": job.status}
                )

            except Exception as commit_error:
                # Database commit failed - this is critical
                logger.error(
                    "Failed to commit final job status update",
                    extra={
                        "job_id": job_id,
                        "intended_status": JobStatus.FAILED.value if result.get("error") else JobStatus.COMPLETED.value,
                        "error": str(commit_error)
                    },
                    exc_info=True
                )

                # Try to rollback and retry once
                try:
                    self.db.rollback()
                    job = self.db.query(Job).filter(Job.job_id == UUID(job_id)).first()
                    if job:
                        job.status = JobStatus.FAILED.value if result.get("error") else JobStatus.COMPLETED.value
                        job.updated_at = datetime.now(timezone.utc)
                        self.db.commit()
                        logger.info(f"Job status update retry successful for {job_id}")
                except Exception as retry_error:
                    logger.error(f"Job status update retry also failed for {job_id}: {retry_error}")
                    # Don't raise - job processing was successful, just status update failed
                    # Cloud Tasks will see this as success and won't retry

            return result

        except Exception as e:
            # Update job status to failed
            job = self.db.query(Job).filter(Job.job_id == UUID(job_id)).first()
            if job:
                job.status = JobStatus.FAILED.value
                job.updated_at = datetime.now(timezone.utc)
                self.db.commit()

            return {"error": str(e), "job_id": job_id}



    async def _execute_music_generation_with_slots(self, job: Job, parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
        """Delegate to modular musicgpt executor (backward-compatible)."""
        return await execute_music_generation_with_slots(self, job, parameters, task_execution_id)

    async def _execute_sound_generation_with_slots(self, job: Job, parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
        """Delegate to modular musicgpt executor (backward-compatible)."""
        return await execute_sound_generation_with_slots(self, job, parameters, task_execution_id)

    async def _execute_music_extension_with_slots(self, job: Job, parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
        """Delegate to modular musicgpt executor (backward-compatible)."""
        return await execute_music_extension_with_slots(self, job, parameters, task_execution_id)

    async def _execute_music_remixing_with_slots(self, job: Job, parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
        """Delegate to modular musicgpt executor (backward-compatible)."""
        return await execute_music_remixing_with_slots(self, job, parameters, task_execution_id)

    async def _execute_vocal_extraction_with_slots(self, job: Job, parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
        """Delegate to modular musicgpt executor (backward-compatible)."""
        return await execute_vocal_extraction_with_slots(self, job, parameters, task_execution_id)

    async def _dispatch_task(
        self,
        job: Job,
        workflow_tracker: WorkflowTracker,
        queue_name: Optional[str],
        task_idx: int,
        task_type: str,
        parameters: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Central dispatcher that executes a single workflow task using the task registry
        metadata to drive context validation, slot management, execution, and result handling.
        Preserves existing per-task behaviors (requeue vs fail vs complete) and return shapes.
        """
        from services.task_registry import TASK_REGISTRY
        from services.task_utils import (
            should_requeue_for_resource,
            handle_task_requeue,
            complete_and_store_result,
            get_task_execution_id,
        )
        from schemas import TaskType

        t = (task_type or "").lower()
        spec = TASK_REGISTRY.get(t)
        if not spec:
            error_msg = f"Unsupported task type in workflow: {task_type}"
            await workflow_tracker.complete_task(job.job_id, task_idx, success=False, error_message=error_msg)
            return {"error": error_msg}

        # Context validation (optional non-fatal for some tasks like album cover)
        p = parameters or {}
        if spec.needs_context_validation:
            try:
                p = await self.context_manager.validate_task_context(job.job_id, task_type, p)
                logger.info(f"Context validation successful for {t} task {task_idx}")
            except ValueError as e:
                if spec.optional_non_fatal_context:
                    logger.warning(f"Context validation failed for optional task {t}: {str(e)}; proceeding without enrichment")
                    p = dict(parameters or {})
                else:
                    error_msg = f"Context validation failed for {t}: {str(e)}"
                    logger.error(error_msg)
                    await workflow_tracker.complete_task(job.job_id, task_idx, success=False, error_message=error_msg)
                    return {"error": error_msg}

        # Resolve execute function
        exec_fn = getattr(self, spec.execute_method, None)
        if not exec_fn:
            error_msg = f"Executor not implemented for task {t}: {spec.execute_method}"
            logger.error(error_msg)
            await workflow_tracker.complete_task(job.job_id, task_idx, success=False, error_message=error_msg)
            return {"error": error_msg}

        # Slot management: fetch workflow task execution id and call slot-wrapped method
        if spec.slot_managed:
            task_execution_id, err = get_task_execution_id(self.db, job.job_id, task_idx)
            if err:
                error_msg = err
                logger.error(error_msg)
                await workflow_tracker.complete_task(job.job_id, task_idx, success=False, error_message=error_msg)
                return {"error": error_msg}

            queue_info = f" from {queue_name}" if queue_name else ""
            logger.info(f"Processing {t} task{queue_info} with slot management for workflow task {task_execution_id}")
            result = await exec_fn(job, p, task_execution_id)
        else:
            result = await exec_fn(job, p)

        # Per-task result handling to preserve legacy behavior
        if t == TaskType.MUSIC_GENERATION.value:
            if result.get("error"):
                error_message = result.get("error", "")
                if should_requeue_for_resource(result) or should_requeue_for_resource(error_message):
                    logger.info(
                        f"🔄 Music generation task requeued due to resource constraint for job {job.job_id}, task {task_idx}"
                    )
                    return await handle_task_requeue(
                        self.db,
                        workflow_tracker,
                        job,
                        task_idx,
                        result=result,
                        message=f"Task requeued due to resource constraint: {error_message}",
                        default_retry_after=60,
                    )
                else:
                    logger.info(
                        f"❌ Music generation task failed, letting Cloud Tasks handle retry for job {job.job_id}, task {task_idx}"
                    )
                    await workflow_tracker.complete_task(job.job_id, task_idx, success=False, error_message=error_message)
                    raise Exception(f"Music generation failed: {error_message}")
            elif result.get("status") == "requeued":
                logger.info(f"🔄 Music generation task requeued for job {job.job_id}, task {task_idx}")
                return await handle_task_requeue(
                    self.db,
                    workflow_tracker,
                    job,
                    task_idx,
                    result=result,
                    message=result.get("message"),
                    default_retry_after=30,
                )

            await complete_and_store_result(
                self.storage_manager,
                workflow_tracker,
                self.context_manager,
                job,
                task_idx,
                task_type,
                result,
                store_context=spec.store_context,
            )
            return result

        elif t in {TaskType.SOUND_GENERATION.value, TaskType.MUSIC_EXTENSION.value, TaskType.MUSIC_REMIXING.value}:
            if result.get("error"):
                error_message = result.get("error", "")
                if should_requeue_for_resource(result) or should_requeue_for_resource(error_message):
                    logger.info(
                        f"🔄 {t} task requeued due to resource constraint for job {job.job_id}, task {task_idx}"
                    )
                    return await handle_task_requeue(
                        self.db,
                        workflow_tracker,
                        job,
                        task_idx,
                        result=result,
                        message=f"Task requeued due to resource constraint: {error_message}",
                        default_retry_after=60,
                    )
                else:
                    logger.info(
                        f"❌ {t} task failed, letting Cloud Tasks handle retry for job {job.job_id}, task {task_idx}"
                    )
                    await workflow_tracker.complete_task(job.job_id, task_idx, success=False, error_message=error_message)
                    return result

            await complete_and_store_result(
                self.storage_manager,
                workflow_tracker,
                self.context_manager,
                job,
                task_idx,
                task_type,
                result,
                store_context=spec.store_context,
            )
            return result

        elif t == TaskType.ALBUM_COVER_GENERATION.value:
            if result.get("error"):
                error_message = result.get("error", "")
                if should_requeue_for_resource(result) or should_requeue_for_resource(error_message):
                    logger.info(
                        f"🔄 Album cover generation task requeued due to resource constraint for job {job.job_id}, task {task_idx}"
                    )
                    return await handle_task_requeue(
                        self.db,
                        workflow_tracker,
                        job,
                        task_idx,
                        result=result,
                        message=f"Task requeued due to resource constraint: {error_message}",
                        default_retry_after=60,
                    )
                else:
                    logger.info(
                        f"❌ Album cover generation task failed, letting Cloud Tasks handle retry for job {job.job_id}, task {task_idx}"
                    )
                    await workflow_tracker.complete_task(job.job_id, task_idx, success=False, error_message=error_message)
                    raise Exception(f"Album cover generation failed: {error_message}")

            await complete_and_store_result(
                self.storage_manager,
                workflow_tracker,
                self.context_manager,
                job,
                task_idx,
                task_type,
                result,
                store_context=True,
            )
            return result

        else:
            # Simple tasks: fail fast on error, otherwise store and complete with context per spec
            if result.get("error"):
                await workflow_tracker.complete_task(job.job_id, task_idx, success=False, error_message=result.get("error"))
                return result

            await complete_and_store_result(
                self.storage_manager,
                workflow_tracker,
                self.context_manager,
                job,
                task_idx,
                task_type,
                result,
                store_context=spec.store_context,
            )
            return result

    async def _process_workflow(self, job: Job, queue_name: str = None) -> Dict[str, Any]:
        """Process a workflow job with multiple tasks.

        Tasks are executed sequentially with data passing between them:
        1) prompt_enhancement -> 2) music_generation -> 3) album_cover_generation
        The enhanced prompt/genre/lyrics from earlier tasks are fed into later tasks.

        Args:
            job: The job to process
            queue_name: The queue this job came from (affects slot management for music tasks)
        """
        try:
            config = job.configuration or {}

            # Get tasks from configuration - support multiple formats for backward compatibility
            tasks = config.get("tasks")
            if not tasks:
                # Handle legacy stage format
                stages = config.get("stages")
                if not stages:
                    wf_cfg = config.get("workflow_config") or {}
                    stages = wf_cfg.get("stages") or wf_cfg.get("steps")

                if stages and isinstance(stages, list):
                    # Convert stages to tasks
                    tasks = []
                    for stage in stages:
                        if isinstance(stage, dict):
                            if "tasks" in stage:
                                # Stage with tasks list
                                tasks.extend(stage["tasks"])
                            elif "task_type" in stage:
                                # Direct task format
                                tasks.append(stage)

            if not tasks or not isinstance(tasks, list):
                return {"error": "Invalid workflow configuration: missing tasks"}

            # Initialize workflow tracking
            workflow_tracker = WorkflowTracker(self.db)
            await workflow_tracker.initialize_workflow_tracking(job.job_id, tasks)

            # Rebuild context from all previously completed tasks using the context manager
            context = await self.context_manager.get_workflow_context(job.job_id)
            last_result: Optional[Dict[str, Any]] = None

            logger.info(f"Reconstructed workflow context for job {job.job_id}: {len(context)} entries")

            # Process each task
            for task_idx, task in enumerate(tasks):
                task_type_raw = (task or {}).get("task_type")
                parameters = (task or {}).get("parameters") or {}

                # Ensure task_type is always a string value for storage and comparison
                if isinstance(task_type_raw, str):
                    task_type = task_type_raw
                    t = task_type.lower()
                else:
                    # Enum or unknown, convert to string value
                    if hasattr(task_type_raw, 'value'):
                        task_type = task_type_raw.value  # Get the string value from enum
                    else:
                        task_type = str(task_type_raw)
                    t = task_type.lower()

                # Lookup existing task execution to support idempotency and async dispatch
                from models import WorkflowTaskExecution
                task_execution = self.db.query(WorkflowTaskExecution).filter(
                    WorkflowTaskExecution.job_id == job.job_id,
                    WorkflowTaskExecution.task_index == task_idx
                ).first()

                # If task already completed, skip to next
                if task_execution and task_execution.status == JobStatus.COMPLETED.value:
                    continue

                # If task already running (e.g., music subtask dispatched), do not duplicate work
                if task_execution and task_execution.status == JobStatus.RUNNING.value:
                    # Defer and let the in-flight work complete
                    return {"status": "requeued", "message": f"Task {task_idx} is already running"}

                # Start task tracking
                await workflow_tracker.start_task(job.job_id, task_type, task_idx)

                try:
                    # Phase 2 refactor: use registry-driven dispatcher for task execution
                    result = await self._dispatch_task(
                        job=job,
                        workflow_tracker=workflow_tracker,
                        queue_name=queue_name,
                        task_idx=task_idx,
                        task_type=task_type,
                        parameters=parameters,
                    )

                    # If dispatcher requested requeue, bubble up immediately
                    if isinstance(result, dict) and result.get("status") == "requeued":
                        return result

                    last_result = result

                    # Update overall progress and continue to next task
                    await workflow_tracker.update_workflow_progress(job.job_id)
                    continue


                except Exception as e:
                    # Mark task as failed
                    await workflow_tracker.complete_task(job.job_id, task_idx,
                                                       success=False, error_message=str(e))
                    raise

            # Store final consolidated workflow result
            final_result = last_result or {"result": "Workflow completed"}

            # Get all task results for consolidation
            task_results = await self.storage_manager.list_workflow_task_results(str(job.job_id))

            # Create consolidated workflow result
            consolidated_result = {
                "success": True,
                "job_type": "workflow",
                "workflow_completed_at": datetime.now(timezone.utc).isoformat(),
                "final_result": final_result,
                "task_results": task_results,
                "task_count": len(tasks)
            }

            # Store consolidated result as the main job result
            await self.storage_manager.store_job_result(
                job_id=str(job.job_id),
                result_data=consolidated_result
            )

            return final_result

        except Exception as e:
            return {"error": f"Workflow processing failed: {str(e)}"}

    async def _execute_album_cover_generation(self, job: Job, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Delegate to modular media executor (backward-compatible)."""
        return await execute_album_cover_generation(self, job, parameters)

    async def _execute_artist_generation(self, job: Job, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Delegate to modular content executor (backward-compatible)."""
        return await execute_artist_generation(self, job, parameters)

    async def _execute_music_generation(self, job: Job, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Delegate to modular musicgpt executor (backward-compatible)."""
        return await execute_music_generation(self, job, parameters)

    async def _execute_sound_generation(self, job: Job, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Delegate to modular musicgpt executor (backward-compatible)."""
        return await execute_sound_generation(self, job, parameters)

    async def _execute_music_extension(self, job: Job, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Delegate to modular musicgpt executor (backward-compatible)."""
        return await execute_music_extension(self, job, parameters)

    async def _execute_music_remixing(self, job: Job, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Delegate to modular musicgpt executor (backward-compatible)."""
        return await execute_music_remixing(self, job, parameters)

    async def _execute_prompt_enhancement(self, job: Job, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Delegate to modular content executor (backward-compatible)."""
        return await execute_prompt_enhancement(self, job, parameters)

    async def _execute_text_translation(self, job: Job, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Delegate to modular content executor (backward-compatible)."""
        return await execute_text_translation(self, job, parameters)

    async def _execute_voice_generation_with_slots(self, job: Job, parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
        """Delegate to modular elevenlabs executor with slots."""
        from services.executors.elevenlabs import execute_voice_generation_with_slots
        return await execute_voice_generation_with_slots(self, job, parameters, task_execution_id)

    async def _execute_voice_over_generation_with_slots(self, job: Job, parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
        """Delegate to modular elevenlabs executor with slots."""
        return await execute_voice_over_generation_with_slots(self, job, parameters, task_execution_id)

    async def _execute_audio_translation_with_slots(self, job: Job, parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
        """Delegate to modular elevenlabs executor with slots."""
        return await execute_audio_translation_with_slots(self, job, parameters, task_execution_id)

    async def _execute_spoken_word_script(self, job: Job, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Delegate to modular content executor (backward-compatible)."""
        return await execute_spoken_word_script(self, job, parameters)

    async def _execute_transcoder_pipeline(self, job: Job, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Delegate to transcoder executor."""
        return await execute_transcoder_pipeline(self, job, parameters)

    async def _execute_export(self, job: Job, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Export final result assets to an external GCS bucket.

        Parameters expected:
          - target_bucket (str): Destination GCS bucket name (without gs://)
          - target_prefix (str): Path/prefix inside the bucket (e.g., exports/my-job)
          - assets (str|List[str]): Which assets to export: one or more of ["audio", "images", "json", "all"]
          - destination_credentials_secret (Optional[str]): Name of a Secret Manager secret containing a service
            account JSON for writing to the external bucket (optional; if omitted, uses default creds and requires
            IAM access to the destination bucket).
        """
        from services.job_manager import JobManager

        # Validate params
        target_bucket = (parameters or {}).get("target_bucket") or (parameters or {}).get("bucket")
        base_prefix = (parameters or {}).get("target_prefix") or (parameters or {}).get("prefix") or "genai"
        assets_sel = (parameters or {}).get("assets") or "all"
        # Optional destination credentials: either provide JSON directly or name of an env var/secret
        destination_credentials_json = (parameters or {}).get("destination_credentials_json")
        if not destination_credentials_json:
            secret_name = (parameters or {}).get("destination_credentials_secret") or (parameters or {}).get("destination_secret")
            if secret_name:
                destination_credentials_json = os.getenv(secret_name) or os.getenv(secret_name.upper())

        if not target_bucket:
            return {"error": "target_bucket is required"}

        # Normalize target bucket (allow gs://bucket and strip any slashes)
        if isinstance(target_bucket, str) and target_bucket.startswith("gs://"):
            target_bucket = target_bucket[5:]
        target_bucket = target_bucket.strip().strip("/")

        # Normalize assets selection
        if isinstance(assets_sel, str):
            assets_requested = [assets_sel.lower()]
        elif isinstance(assets_sel, list):
            assets_requested = [str(a).lower() for a in assets_sel]
        else:
            assets_requested = ["all"]

        if "all" in assets_requested:
            assets_requested = ["audio", "images", "json"]

        # Source and destination job IDs
        source_job_id = (parameters or {}).get("source_job_id")
        dest_job_id = (parameters or {}).get("destination_job_id")

        job_id_str = str(job.job_id)
        src_job_id_str = str(source_job_id) if source_job_id else job_id_str
        dest_job_id_str = str(dest_job_id) if dest_job_id else job_id_str

        # Optional explicit filename selection
        audio_filenames = parameters.get("audio_filenames") or parameters.get("audio_files")
        image_filenames = parameters.get("image_filenames") or parameters.get("images_filenames") or parameters.get("image_files")
        json_filenames = parameters.get("json_filenames") or parameters.get("result_filenames")

        def _to_list(val):
            if val is None:
                return []
            if isinstance(val, list):
                return [str(x) for x in val if isinstance(x, (str, bytes)) and str(x)]
            if isinstance(val, (str, bytes)):
                s = str(val).strip()
                return [s] if s else []
            return []

        audio_filenames = _to_list(audio_filenames)
        image_filenames = _to_list(image_filenames)
        json_filenames = _to_list(json_filenames)

        explicit_mode = bool(audio_filenames or image_filenames or json_filenames)

        # Discover final assets from job results (only if not using explicit filenames)
        asset_urls_map = {}
        if not explicit_mode:
            jm = JobManager(self.db)
            asset_urls_map = await jm.get_job_asset_urls(src_job_id_str)

        # Classify sources to copy
        sources = []  # List[Tuple[src_bucket, src_path, dest_path]]
        def add_copy(gcs_url: str, kind: str, preferred_filename: str | None = None):
            if not isinstance(gcs_url, str) or not gcs_url.startswith("gs://"):
                return
            try:
                # Parse gs://bucket/path
                _, rest = gcs_url.split("gs://", 1)
                src_bucket, src_path = rest.split("/", 1)
                # Destination folder schema per kind
                if kind == "audio":
                    kind_prefix = f"{base_prefix.strip('/')}/audio/{dest_job_id_str}"
                elif kind == "images":
                    kind_prefix = f"{base_prefix.strip('/')}/images/{dest_job_id_str}"
                elif kind == "json":
                    kind_prefix = f"{base_prefix.strip('/')}/json/{dest_job_id_str}"
                else:
                    kind_prefix = f"{base_prefix.strip('/')}/{dest_job_id_str}"
                # Destination path preserves original filename unless preferred override
                fname = preferred_filename or os.path.basename(src_path)
                dest_path = f"{kind_prefix}/{fname}"
                sources.append((src_bucket, src_path, dest_path))
            except Exception:
                return

        if explicit_mode:
            internal_bucket = self.storage_manager.storage.bucket_name
            # Audio
            if "audio" in assets_requested and audio_filenames:
                for fname in audio_filenames:
                    storage_path = self.storage_manager.storage.get_music_path(src_job_id_str, fname)
                    add_copy(f"gs://{internal_bucket}/{storage_path}", kind="audio", preferred_filename=fname)
            # Images
            if "images" in assets_requested and image_filenames:
                for fname in image_filenames:
                    storage_path = self.storage_manager.storage.get_image_path(src_job_id_str, fname)
                    add_copy(f"gs://{internal_bucket}/{storage_path}", kind="images", preferred_filename=fname)
            # JSON
            if "json" in assets_requested:
                json_names = json_filenames or ["job_result.json"]
                for fname in json_names:
                    storage_path = self.storage_manager.storage.get_results_path(src_job_id_str, fname)
                    add_copy(f"gs://{internal_bucket}/{storage_path}", kind="json", preferred_filename=fname)
        else:
            # Audio from discovered assets
            if "audio" in assets_requested:
                for key, url in (asset_urls_map or {}).items():
                    lk = key.lower()
                    if "audio" in lk or lk.endswith("_mp3") or lk.endswith("_wav"):
                        add_copy(url, kind="audio")
            # Images from discovered assets
            if "images" in assets_requested:
                for key, url in (asset_urls_map or {}).items():
                    lk = key.lower()
                    if "image" in lk or lk.endswith("_png") or lk.endswith("_jpg") or lk.endswith("_jpeg"):
                        add_copy(url, kind="images")
            # JSON results
            json_src = asset_urls_map.get("metadata")
            if "json" in assets_requested and isinstance(json_src, str):
                add_copy(json_src, kind="json", preferred_filename="job_result.json")

        if not sources:
            result = {
                "success": True,
                "task_type": "export",
                "result": {
                    "message": "No matching assets to export",
                    "exported": [],
                    "target_bucket": target_bucket,
                    "target_prefix": base_prefix,
                },
            }
            return result

        # Perform copies
        exported = []
        errors = []
        try:
            # Use the existing StorageManager for current project bucket and client
            sm: StorageManager = self.storage_manager
            for src_bucket, src_path, dest_path in sources:
                try:
                    ok, meta = await sm.copy_gcs_object(
                        source_bucket=src_bucket,
                        source_path=src_path,
                        destination_bucket=target_bucket,
                        destination_path=dest_path,
                        destination_credentials_json=destination_credentials_json,
                    )
                    if ok:
                        exported.append({
                            "source": f"gs://{src_bucket}/{src_path}",
                            "destination": f"gs://{target_bucket}/{dest_path}",
                            "size": meta.get("size"),
                            "content_type": meta.get("content_type"),
                        })
                    else:
                        errors.append({
                            "source": f"gs://{src_bucket}/{src_path}",
                            "destination": f"gs://{target_bucket}/{dest_path}",
                            "error": meta.get("error") if isinstance(meta, dict) else "copy_failed",
                        })
                except Exception as e:
                    errors.append({
                        "source": f"gs://{src_bucket}/{src_path}",
                        "destination": f"gs://{target_bucket}/{dest_path}",
                        "error": str(e),
                    })

            success = len(exported) > 0 and len(errors) == 0
            result = {
                "success": success,
                "task_type": "export",
                "result": {
                    "message": "Export completed" if success else ("Export completed with errors" if exported else "Export failed"),
                    "exported": exported,
                    "errors": errors,
                    "target_bucket": target_bucket,
                    "target_prefix": base_prefix,
                },
            }
            return result
        except Exception as e:
            return {"error": f"Export failed: {str(e)}"}
