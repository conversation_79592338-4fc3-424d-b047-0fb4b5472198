"""
Storage Manager service for handling Google Cloud Storage operations.

This service manages file uploads, downloads, and storage operations
for the music generation system using Google Cloud Storage.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import os
import requests
import json
import aiohttp

from google.cloud import storage
from config import storage_config


class StorageManager:
    """Manages Google Cloud Storage operations for job assets and temporary files."""

    def __init__(self):
        self.storage = storage_config

        # Check if we're using the emulator
        self.emulator_host = os.getenv('STORAGE_EMULATOR_HOST')
        if self.emulator_host:
            self.use_emulator = True
            self.emulator_url = f"http://{self.emulator_host}"
            # Initialize bucket in emulator if it doesn't exist
            self._ensure_bucket_exists()
        else:
            self.use_emulator = False
            # Initialize Google Cloud Storage client for production
            self.client = storage.Client()
            self.bucket = self.client.bucket(self.storage.bucket_name)

    def _ensure_bucket_exists(self):
        """Ensure the bucket exists in the GCS emulator."""
        if not self.use_emulator:
            return

        try:
            # Check if bucket exists
            response = requests.get(f"{self.emulator_url}/storage/v1/b/{self.storage.bucket_name}")
            if response.status_code == 404:
                # Create bucket
                bucket_data = {
                    "name": self.storage.bucket_name,
                    "location": "US"
                }
                create_response = requests.post(
                    f"{self.emulator_url}/storage/v1/b",
                    json=bucket_data,
                    params={"project": "local-dev-project"}
                )
                if create_response.status_code in [200, 201]:
                    print(f"Created bucket: {self.storage.bucket_name}")
                else:
                    print(f"Failed to create bucket: {create_response.text}")
            elif response.status_code == 200:
                print(f"Bucket {self.storage.bucket_name} already exists")
        except Exception as e:
            print(f"Error ensuring bucket exists: {e}")

    # ============================================================================
    # PATH GENERATION METHODS
    # ============================================================================

    def get_job_asset_urls(self, job_id: str) -> Dict[str, str]:
        """
        Get signed URLs for job assets.

        Args:
            job_id: Job ID to get assets for

        Returns:
            Dict[str, str]: Dictionary of asset types to URLs
        """
        # This would integrate with Google Cloud Storage client
        # For now, return the paths that would be used
        return {
            "music_file": f"gs://{self.storage.bucket_name}/{self.storage.get_music_path(job_id, 'final.mp3')}",
            "album_cover": f"gs://{self.storage.bucket_name}/{self.storage.get_image_path(job_id, 'album_cover.jpg')}",
            "metadata": f"gs://{self.storage.bucket_name}/{self.storage.get_results_path(job_id)}"
        }



    def get_processing_file_path(self, job_id: str, filename: str) -> str:
        """
        Get path for intermediate processing files.

        Args:
            job_id: Job ID
            filename: Name of the processing file

        Returns:
            str: Path to the processing file
        """
        return self.storage.get_temp_processing_path(job_id, filename)

    # ============================================================================
    async def copy_gcs_object(
        self,
        source_bucket: str,
        source_path: str,
        destination_bucket: str,
        destination_path: str,
        destination_credentials_json: Optional[str] = None,
    ) -> (bool, Dict[str, Any]):
        """
        Copy an object from one GCS bucket/path to another.

        - In production: attempts server-side copy when using the same credentials and
          destination_credentials_json is not provided; otherwise falls back to
          download+upload using the appropriate clients.
        - In emulator mode: downloads bytes from source and uploads to destination via REST.

        Returns a tuple (success, metadata) where metadata includes content_type, size, etc.
        """
        import mimetypes

        try:
            if self.use_emulator:
                # Download bytes from source (emulator)
                src_url = f"{self.emulator_url}/storage/v1/b/{source_bucket}/o/{source_path.replace('/', '%2F')}?alt=media"
                async with aiohttp.ClientSession() as session:
                    async with session.get(src_url) as resp:
                        if resp.status != 200:
                            return False, {"error": f"emulator download failed HTTP {resp.status}"}
                        data = await resp.read()
                        # Determine content type from extension if not known
                        content_type = mimetypes.guess_type(source_path)[0] or "application/octet-stream"

                        # Attempt to upload to REAL GCS first (allows emulator -> real copy)
                        try:
                            from google.cloud import storage as gcs
                            if destination_credentials_json:
                                from google.oauth2 import service_account
                                import json as _json
                                sa_info = _json.loads(destination_credentials_json)
                                credentials = service_account.Credentials.from_service_account_info(sa_info)
                                dest_client = gcs.Client(credentials=credentials, project=sa_info.get("project_id"))
                            else:
                                dest_client = gcs.Client()
                            dest_bucket_obj = dest_client.bucket(destination_bucket)
                            new_blob = dest_bucket_obj.blob(destination_path)
                            new_blob.upload_from_string(data, content_type=content_type)
                            return True, {"content_type": content_type, "size": len(data)}
                        except Exception:
                            # Fallback to emulator upload (destination must be emulator bucket)
                            upload_url = f"{self.emulator_url}/upload/storage/v1/b/{destination_bucket}/o"
                            params = {"uploadType": "media", "name": destination_path}
                            headers = {"Content-Type": content_type}
                            up_resp = requests.post(upload_url, params=params, data=data, headers=headers)
                            if up_resp.status_code in [200, 201]:
                                return True, {"content_type": content_type, "size": len(data)}
                            return False, {"error": f"emulator upload failed {up_resp.status_code}: {up_resp.text}"}

            # Production
            # Prepare clients/buckets
            dest_client = self.client
            if destination_credentials_json:
                try:
                    from google.oauth2 import service_account
                    import json as _json
                    sa_info = _json.loads(destination_credentials_json)
                    credentials = service_account.Credentials.from_service_account_info(sa_info)
                    from google.cloud import storage as gcs
                    dest_client = gcs.Client(credentials=credentials, project=sa_info.get("project_id"))
                except Exception as e:
                    return False, {"error": f"invalid destination credentials: {e}"}

            src_bucket_obj = self.client.bucket(source_bucket)
            src_blob = src_bucket_obj.blob(source_path)
            if not src_blob.exists():
                return False, {"error": f"source not found: gs://{source_bucket}/{source_path}"}

            dest_bucket_obj = dest_client.bucket(destination_bucket)

            # Always perform download+upload to keep logic simple and explicit
            data = src_blob.download_as_bytes()
            content_type = src_blob.content_type or mimetypes.guess_type(source_path)[0] or "application/octet-stream"
            new_blob = dest_bucket_obj.blob(destination_path)
            new_blob.upload_from_string(data, content_type=content_type)
            return True, {"content_type": content_type, "size": len(data)}

        except Exception as e:
            return False, {"error": str(e)}

    # FILE OPERATIONS
    # ============================================================================

    async def upload_file(self, local_path: str, storage_path: str, content_type: str = None) -> bool:
        """
        Upload a file to Google Cloud Storage.

        Args:
            local_path: Local file path to upload
            storage_path: Destination path in storage
            content_type: MIME type of the file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.use_emulator:
                # For emulator, we'd need to implement this if needed
                # Currently not used in emulator mode
                return False
            else:
                # Implement actual GCS upload for production
                blob = self.bucket.blob(storage_path)
                blob.upload_from_filename(local_path, content_type=content_type)
                print(f"Uploaded {local_path} to gs://{self.storage.bucket_name}/{storage_path}")
                return True
        except Exception as e:
            print(f"Upload failed: {e}")
            return False

    async def upload_from_bytes(self, data: bytes, storage_path: str, content_type: str = None) -> bool:
        """
        Upload data from bytes to Google Cloud Storage.

        Args:
            data: Bytes data to upload
            storage_path: Destination path in storage
            content_type: MIME type of the file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.use_emulator:
                # Upload to GCS emulator using REST API with raw data
                url = f"{self.emulator_url}/upload/storage/v1/b/{self.storage.bucket_name}/o"

                params = {
                    'uploadType': 'media',
                    'name': storage_path
                }

                headers = {
                    'Content-Type': content_type or 'application/octet-stream'
                }

                # Upload raw bytes directly
                response = requests.post(url, data=data, params=params, headers=headers)

                if response.status_code in [200, 201]:
                    print(f"Uploaded {len(data)} bytes to gs://{self.storage.bucket_name}/{storage_path}")
                    return True
                else:
                    print(f"Upload failed: {response.status_code} - {response.text}")
                    return False
            else:
                # Implement actual GCS upload from bytes for production
                blob = self.bucket.blob(storage_path)
                blob.upload_from_string(data, content_type=content_type)
                print(f"Uploaded {len(data)} bytes to gs://{self.storage.bucket_name}/{storage_path}")
                return True
        except Exception as e:
            print(f"Upload from bytes failed: {e}")
            return False

    async def upload_from_base64(self, base64_data: str, storage_path: str, content_type: str = None) -> bool:
        """
        Upload data from base64 string to Google Cloud Storage.

        Args:
            base64_data: Base64 encoded data to upload
            storage_path: Destination path in storage
            content_type: MIME type of the file

        Returns:
            bool: True if successful, False otherwise
        """
        import base64

        try:
            # Decode base64 data
            data = base64.b64decode(base64_data)
            return await self.upload_from_bytes(data, storage_path, content_type)
        except Exception as e:
            print(f"Upload from base64 failed: {e}")
            return False

    async def download_file(self, storage_path: str, local_path: str) -> bool:
        """
        Download a file from Google Cloud Storage.

        Args:
            storage_path: Path in storage to download from
            local_path: Local path to save the file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.use_emulator:
                # For emulator, we'd need to implement this if needed
                # Currently not used in emulator mode
                return False
            else:
                # Implement actual GCS download for production
                blob = self.bucket.blob(storage_path)
                blob.download_to_filename(local_path)
                print(f"Downloaded gs://{self.storage.bucket_name}/{storage_path} to {local_path}")
                return True
        except Exception as e:
            print(f"Download failed: {e}")
            return False

    async def download_as_bytes(self, gcs_path: str) -> bytes:
        """
        Download a file from Google Cloud Storage and return as bytes.

        Args:
            gcs_path: Full GCS path (gs://bucket/path) or just the storage path

        Returns:
            bytes: File content as bytes

        Raises:
            Exception: If download fails
        """
        try:
            # Parse GCS path to extract bucket and storage path
            if gcs_path.startswith('gs://'):
                # Extract bucket and path from full GCS URL
                path_parts = gcs_path[5:].split('/', 1)  # Remove 'gs://' prefix
                bucket_name = path_parts[0]
                storage_path = path_parts[1] if len(path_parts) > 1 else ""

                # Verify we're using the correct bucket
                if bucket_name != self.storage.bucket_name:
                    raise ValueError(f"Bucket mismatch: expected {self.storage.bucket_name}, got {bucket_name}")
            else:
                # Assume it's just the storage path
                storage_path = gcs_path
                bucket_name = self.storage.bucket_name

            if self.use_emulator:
                # Download from GCS emulator using REST API
                url = f"{self.emulator_url}/storage/v1/b/{bucket_name}/o/{storage_path.replace('/', '%2F')}?alt=media"

                async with aiohttp.ClientSession() as session:
                    async with session.get(url) as response:
                        if response.status == 200:
                            content = await response.read()
                            print(f"Downloaded {len(content)} bytes from gs://{bucket_name}/{storage_path}")
                            return content
                        elif response.status == 404:
                            raise FileNotFoundError(f"File not found: gs://{bucket_name}/{storage_path}")
                        else:
                            raise Exception(f"Download failed: HTTP {response.status} - {await response.text()}")
            else:
                # Download from actual GCS for production
                blob = self.bucket.blob(storage_path)
                if not blob.exists():
                    raise FileNotFoundError(f"File not found: gs://{bucket_name}/{storage_path}")

                content = blob.download_as_bytes()
                print(f"Downloaded {len(content)} bytes from gs://{bucket_name}/{storage_path}")
                return content

        except Exception as e:
            print(f"Download as bytes failed for {gcs_path}: {e}")
            raise

    async def delete_file(self, storage_path: str) -> bool:
        """
        Delete a file from Google Cloud Storage.

        Args:
            storage_path: Path in storage to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.use_emulator:
                # For emulator, we'd need to implement this if needed
                # Currently not used in emulator mode
                return False
            else:
                # Implement actual GCS deletion for production
                blob = self.bucket.blob(storage_path)
                blob.delete()
                print(f"Deleted gs://{self.storage.bucket_name}/{storage_path}")
                return True
        except Exception as e:
            print(f"Delete failed: {e}")
            return False

    async def file_exists(self, storage_path: str) -> bool:
        """
        Check if a file exists in Google Cloud Storage.

        Args:
            storage_path: Path in storage to check

        Returns:
            bool: True if file exists, False otherwise
        """
        try:
            if self.use_emulator:
                # Check file existence in GCS emulator
                url = f"{self.emulator_url}/storage/v1/b/{self.storage.bucket_name}/o/{storage_path}"
                response = requests.get(url)
                return response.status_code == 200
            else:
                # Implement actual GCS existence check for production
                blob = self.bucket.blob(storage_path)
                return blob.exists()
        except Exception as e:
            print(f"Existence check failed: {e}")
            return False

    async def get_signed_url(self, storage_path: str, expiration_minutes: int = 60, method: str = "GET") -> Optional[str]:
        """
        Generate a signed URL for accessing a file.

        Args:
            storage_path: Path in storage
            expiration_minutes: URL expiration time in minutes
            method: HTTP method (GET, PUT, POST)

        Returns:
            Optional[str]: Signed URL if successful, None otherwise
        """
        try:
            if self.use_emulator:
                # For emulator, return direct access URL
                return f"{self.emulator_url}/storage/v1/b/{self.storage.bucket_name}/o/{storage_path}?alt=media"
            else:
                # Implement actual signed URL generation for production
                blob = self.bucket.blob(storage_path)
                expiration = datetime.utcnow() + timedelta(minutes=expiration_minutes)
                return blob.generate_signed_url(expiration=expiration, method=method)
        except Exception as e:
            print(f"Signed URL generation failed: {e}")
            return None

    # ============================================================================
    # JOB-SPECIFIC OPERATIONS
    # ============================================================================

    async def store_job_result(self, job_id: str, result_data: Dict[str, Any],
                             task_index: Optional[int] = None, task_type: Optional[str] = None) -> bool:
        """
        Store job result metadata.

        Args:
            job_id: Job ID
            result_data: Result data to store
            task_index: Optional task index for workflow tasks (creates unique filename)
            task_type: Optional task type for workflow tasks (creates unique filename)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Generate filename based on whether this is a task-specific result
            if task_index is not None and task_type is not None:
                # Task-specific result: task_{index}_{type}_result.json
                filename = f"task_{task_index}_{task_type}_result.json"
            else:
                # Default job result filename
                filename = "job_result.json"

            result_path = self.storage.get_results_path(job_id, filename)
            json_data = json.dumps(result_data, indent=2).encode('utf-8')

            return await self.upload_from_bytes(
                json_data,
                result_path,
                content_type='application/json'
            )
        except Exception as e:
            print(f"Result storage failed: {e}")
            return False

    async def store_album_cover(self, job_id: str, image_base64: str, filename: str = "album_cover.jpg") -> Optional[str]:
        """
        Store album cover image from base64 data.

        Args:
            job_id: Job ID
            image_base64: Base64 encoded image data
            filename: Filename for the image (default: album_cover.jpg)

        Returns:
            Optional[str]: GCS path if successful, None otherwise
        """
        try:
            # Generate storage path for the image
            storage_path = self.storage.get_image_path(job_id, filename)

            # Upload the image
            success = await self.upload_from_base64(
                image_base64,
                storage_path,
                content_type="image/jpeg"
            )

            if success:
                return f"gs://{self.storage.bucket_name}/{storage_path}"
            else:
                return None

        except Exception as e:
            print(f"Album cover storage failed: {e}")
            return None

    async def get_album_cover_url(self, job_id: str, filename: str = "album_cover.jpg") -> Optional[str]:
        """
        Get signed URL for album cover image.

        Args:
            job_id: Job ID
            filename: Filename of the image (default: album_cover.jpg)

        Returns:
            Optional[str]: Signed URL if successful, None otherwise
        """
        try:
            storage_path = self.storage.get_image_path(job_id, filename)
            return await self.get_signed_url(storage_path, expiration_minutes=60)
        except Exception as e:
            print(f"Failed to get album cover URL: {e}")
            return None

    async def store_artist_profile(self, job_id: str, artist_data: Dict[str, Any], filename: str = "artist_profile.json") -> Optional[str]:
        """
        Store artist profile data as JSON.

        Args:
            job_id: Job ID
            artist_data: Artist profile data dictionary
            filename: Filename for the profile (default: artist_profile.json)

        Returns:
            Optional[str]: GCS path if successful, None otherwise
        """
        try:
            # Generate storage path for the artist profile
            storage_path = self.storage.get_results_path(job_id, filename)

            # Convert artist data to JSON
            json_data = json.dumps(artist_data, indent=2).encode('utf-8')

            # Upload the JSON data
            success = await self.upload_from_bytes(
                json_data,
                storage_path,
                content_type="application/json"
            )

            if success:
                return f"gs://{self.storage.bucket_name}/{storage_path}"
            else:
                return None

        except Exception as e:
            print(f"Artist profile storage failed: {e}")
            return None

    async def store_spoken_word_script(self, job_id: str, script_data: Dict[str, Any], filename: str = "spoken_word_script.json") -> Optional[str]:
        """
        Store spoken word script data as JSON.

        Args:
            job_id: Job ID
            script_data: Spoken word script data dictionary
            filename: Filename for the script (default: spoken_word_script.json)

        Returns:
            Optional[str]: GCS path if successful, None otherwise
        """
        try:
            # Generate storage path for the spoken word script
            storage_path = self.storage.get_results_path(job_id, filename)

            # Convert script data to JSON
            json_data = json.dumps(script_data, indent=2).encode('utf-8')

            # Upload the JSON data
            success = await self.upload_from_bytes(
                json_data,
                storage_path,
                content_type="application/json"
            )

            if success:
                return f"gs://{self.storage.bucket_name}/{storage_path}"
            else:
                return None

        except Exception as e:
            print(f"Spoken word script storage failed: {e}")
            return None

    async def get_job_result(self, job_id: str, task_index: Optional[int] = None,
                           task_type: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Retrieve job result metadata.

        Args:
            job_id: Job ID
            task_index: Optional task index for workflow task results
            task_type: Optional task type for workflow task results

        Returns:
            Optional[Dict[str, Any]]: Result data if found, None otherwise
        """
        import json
        import aiohttp

        try:
            # Generate filename based on whether this is a task-specific result
            if task_index is not None and task_type is not None:
                filename = f"task_{task_index}_{task_type}_result.json"
            else:
                filename = "job_result.json"

            result_path = self.storage.get_results_path(job_id, filename)

            if self.use_emulator:
                # Use GCS emulator HTTP API
                url = f"{self.emulator_url}/storage/v1/b/{self.storage.bucket_name}/o/{result_path.replace('/', '%2F')}?alt=media"

                async with aiohttp.ClientSession() as session:
                    async with session.get(url) as response:
                        if response.status == 200:
                            content = await response.text()
                            return json.loads(content)
                        elif response.status == 404:
                            return None
                        else:
                            print(f"Failed to retrieve result: HTTP {response.status}")
                            return None
            else:
                # Implement actual GCS retrieval for production
                blob = self.bucket.blob(result_path)
                if blob.exists():
                    return json.loads(blob.download_as_text())
                return None
        except Exception as e:
            print(f"Result retrieval failed: {e}")
            return None

    async def list_workflow_task_results(self, job_id: str) -> List[Dict[str, Any]]:
        """
        List all task results for a workflow job.

        Args:
            job_id: Job ID

        Returns:
            List[Dict[str, Any]]: List of task results with metadata
        """
        import json
        import aiohttp

        task_results = []

        try:
            if self.use_emulator:
                # List objects in the results directory for this job
                results_prefix = self.storage.get_results_path(job_id, "").rstrip("/")
                url = f"{self.emulator_url}/storage/v1/b/{self.storage.bucket_name}/o"
                params = {"prefix": results_prefix}

                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            items = data.get("items", [])

                            for item in items:
                                name = item.get("name", "")
                                if name.startswith(f"{results_prefix}/task_") and name.endswith("_result.json"):
                                    # Extract task info from filename
                                    filename = name.split("/")[-1]  # Get just the filename
                                    # Parse: task_{index}_{type}_result.json
                                    parts = filename.replace("_result.json", "").split("_")
                                    if len(parts) >= 3 and parts[0] == "task":
                                        task_index = int(parts[1])
                                        task_type = "_".join(parts[2:])  # Handle task types with underscores

                                        # Retrieve the actual result data
                                        result_data = await self.get_job_result(job_id, task_index, task_type)
                                        if result_data:
                                            task_results.append({
                                                "task_index": task_index,
                                                "task_type": task_type,
                                                "filename": filename,
                                                "result": result_data
                                            })
            else:
                # For production GCS, list blobs with prefix
                results_prefix = self.storage.get_results_path(job_id, "").rstrip("/")
                blobs = self.bucket.list_blobs(prefix=f"{results_prefix}/")

                for blob in blobs:
                    if blob.name.startswith(f"{results_prefix}/task_") and blob.name.endswith("_result.json"):
                        # Extract task info from filename
                        filename = blob.name.split("/")[-1]  # Get just the filename
                        # Parse: task_{index}_{type}_result.json
                        parts = filename.replace("_result.json", "").split("_")
                        if len(parts) >= 3 and parts[0] == "task":
                            task_index = int(parts[1])
                            task_type = "_".join(parts[2:])  # Handle task types with underscores

                            # Retrieve the actual result data
                            result_data = await self.get_job_result(job_id, task_index, task_type)
                            if result_data:
                                task_results.append({
                                    "task_index": task_index,
                                    "task_type": task_type,
                                    "filename": filename,
                                    "result": result_data
                                })

        except Exception as e:
            print(f"Failed to list workflow task results: {e}")

        # Sort by task index
        task_results.sort(key=lambda x: x["task_index"])
        return task_results

    async def cleanup_temp_files(self, older_than_hours: int = 24) -> int:
        """
        Clean up temporary files older than specified hours.

        Args:
            older_than_hours: Age threshold in hours

        Returns:
            int: Number of files cleaned up
        """
        try:
            # TODO: Implement actual cleanup
            # cutoff_time = datetime.utcnow() - timedelta(hours=older_than_hours)
            # blobs = self.bucket.list_blobs(prefix=self.storage.temp_prefix)
            # deleted_count = 0
            # for blob in blobs:
            #     if blob.time_created < cutoff_time:
            #         blob.delete()
            #         deleted_count += 1
            # return deleted_count
            return 0
        except Exception as e:
            print(f"Temp file cleanup failed: {e}")
            return 0
