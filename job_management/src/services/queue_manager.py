"""
Queue Manager service for handling MusicGPT rate limits and Cloud Tasks queue management.

This service manages rate limiting for MusicGPT API calls and provides
intelligent queue processing with concurrent request tracking.
"""

from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import Dict, Any
import asyncio
import os
import json
import logging
from google.cloud import tasks_v2

from models import RateLimit

# Configure logger
logger = logging.getLogger(__name__)


# Removed custom exception classes - Cloud Tasks handles retry logic based on HTTP status codes


class MusicGPTQueueManager:
    """Manages MusicGPT rate limits and concurrent slot allocation for intelligent job queue processing."""

    def __init__(self, db: Session):
        self.db = db
        # MusicGPT rate limits from official documentation
        self.limits = {
            "musicai_parallel": 10,                    # Maximum 10 parallel audio creation requests
            "conversion_status_check_per_minute": 200, # Maximum 200 GetConversionById calls per minute
            "conversions_per_minute": 60               # Maximum 60 conversions per minute
        }
        # WebSocket connections for real-time MusicAI status updates
        self.websocket_connections = {}
        # Cleanup tracking
        self.last_cleanup = datetime.utcnow()
        self.cleanup_interval = timedelta(minutes=30)

    async def acquire_musicgpt_slot(self, task_id: str) -> bool:
        """
        CRITICAL: Atomically acquire a MusicGPT processing slot.
        Returns True if slot acquired, False if all slots are occupied.
        """
        logger.info(f"🎵 Attempting to acquire MusicGPT slot for task {task_id}")
        from sqlalchemy import text
        from models import ActiveMusicGeneration

        # CRITICAL FIX: Use atomic INSERT to prevent exceeding 10 concurrent limit
        try:
            # First check current active count atomically
            count_query = text("""
                SELECT COUNT(*) FROM active_music_generation
                WHERE is_active = true
            """)
            result = self.db.execute(count_query)
            current_active = result.fetchone()[0]
            logger.info(f"🎵 Current active MusicGPT slots: {current_active}/{self.limits['musicai_parallel']} for task {task_id}")

            if current_active >= self.limits["musicai_parallel"]:
                logger.warning(f"🚫 MusicGPT slots full: {current_active}/{self.limits['musicai_parallel']}, cannot acquire slot for task {task_id}")
                return False

            # ENHANCED DEBUG: Check if table exists and has correct schema
            try:
                schema_check = text("""
                    SELECT column_name FROM information_schema.columns
                    WHERE table_name = 'active_music_generation'
                    AND column_name IN ('task_id', 'workflow_task_execution_id')
                """)
                schema_result = self.db.execute(schema_check)
                columns = [row[0] for row in schema_result.fetchall()]
                logger.info(f"🔍 DEBUG: active_music_generation table columns: {columns}")
            except Exception as schema_e:
                logger.error(f"🔍 DEBUG: Schema check failed: {schema_e}")

            # Atomically upsert active generation record
            # Use workflow_task_execution_id as unique identifier and re-activate on conflict
            insert_query = text("""
                INSERT INTO active_music_generation (workflow_task_execution_id, started_at, is_active)
                VALUES (:task_id, :now, true)
                ON CONFLICT (workflow_task_execution_id) DO UPDATE
                SET is_active = true,
                    started_at = EXCLUDED.started_at,
                    updated_at = :now
                RETURNING id
            """)

            logger.info(f"🔍 DEBUG: Executing UPSERT for task {task_id}")
            result = self.db.execute(insert_query, {
                'task_id': task_id,
                'now': datetime.utcnow()
            })

            upsert_row = result.fetchone()
            if upsert_row:
                self.db.commit()
                logger.info(f"✅ MusicGPT slot acquired/activated for task {task_id}, row ID: {upsert_row[0]}")
                return True
            else:
                # Unexpected: no row returned
                logger.error(f"❌ UPSERT returned no row for task {task_id}")
                self.db.rollback()
                return False

        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ Failed to acquire MusicGPT slot for task {task_id}: {e}")
            logger.error(f"🔍 DEBUG: Exception type: {type(e).__name__}")
            logger.error(f"🔍 DEBUG: Exception details: {str(e)}")
            return False

    async def release_musicgpt_slot(self, task_id: str):
        """
        CRITICAL: Release a MusicGPT processing slot when task completes.
        """
        from sqlalchemy import text

        try:
            update_query = text("""
                UPDATE active_music_generation
                SET is_active = false, updated_at = :now
                WHERE workflow_task_execution_id = :task_id AND is_active = true
            """)

            self.db.execute(update_query, {
                'task_id': task_id,
                'now': datetime.utcnow()
            })
            self.db.commit()
            logger.info(f"MusicGPT slot released for task {task_id}")

        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to release MusicGPT slot for task {task_id}: {e}")

    async def get_available_slots(self) -> int:
        """Get number of available MusicGPT slots."""
        from sqlalchemy import text

        try:
            query = text("SELECT COUNT(*) FROM active_music_generation WHERE is_active = true")
            result = self.db.execute(query)
            active_count = result.fetchone()[0]
            return max(0, self.limits["musicai_parallel"] - active_count)
        except Exception as e:
            logger.error(f"Failed to get available slots: {e}")
            return 0

    async def check_rate_limit(self, identifier: str, limit_type: str) -> bool:
        """Check if a request can be made within rate limits."""
        # Trigger cleanup if needed
        await self._maybe_cleanup()

        now = datetime.utcnow()
        window_start, window_end = self._get_window_bounds(now, limit_type)

        # Get current rate limit record
        rate_limit = self.db.query(RateLimit).filter(
            RateLimit.identifier == identifier,
            RateLimit.limit_type == limit_type,
            RateLimit.window_start == window_start
        ).first()

        if not rate_limit:
            return True

        limit_value = self.limits.get(limit_type, 100)
        return rate_limit.request_count < limit_value

    async def increment_rate_limit(self, identifier: str, limit_type: str):
        """Increment the rate limit counter for an identifier."""
        now = datetime.utcnow()
        window_start, window_end = self._get_window_bounds(now, limit_type)

        # Use UPSERT pattern for better concurrency
        rate_limit = self.db.query(RateLimit).filter(
            RateLimit.identifier == identifier,
            RateLimit.limit_type == limit_type,
            RateLimit.window_start == window_start
        ).first()

        if not rate_limit:
            rate_limit = RateLimit(
                identifier=identifier,
                limit_type=limit_type,
                window_start=window_start,
                window_end=window_end,
                request_count=1
            )
            self.db.add(rate_limit)
        else:
            rate_limit.request_count += 1
            rate_limit.updated_at = now

        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            # Handle race conditions gracefully
            await self._handle_concurrent_update(identifier, limit_type, window_start, window_end)

    def _get_window_bounds(self, now: datetime, limit_type: str) -> tuple:
        """Get window start and end times for rate limiting."""
        if limit_type.endswith("_hour"):
            window_start = now.replace(minute=0, second=0, microsecond=0)
            window_end = window_start + timedelta(hours=1)
        else:  # minute-based
            window_start = now.replace(second=0, microsecond=0)
            window_end = window_start + timedelta(minutes=1)

        return window_start, window_end

    async def _handle_concurrent_update(self, identifier: str, limit_type: str,
                                      window_start: datetime, window_end: datetime):
        """Handle concurrent updates with retry logic."""
        for attempt in range(3):
            try:
                # Try to update existing record
                result = self.db.query(RateLimit).filter(
                    RateLimit.identifier == identifier,
                    RateLimit.limit_type == limit_type,
                    RateLimit.window_start == window_start
                ).update({
                    "request_count": RateLimit.request_count + 1,
                    "updated_at": datetime.utcnow()
                })

                if result == 0:
                    # Record doesn't exist, create it
                    rate_limit = RateLimit(
                        identifier=identifier,
                        limit_type=limit_type,
                        window_start=window_start,
                        window_end=window_end,
                        request_count=1
                    )
                    self.db.add(rate_limit)

                self.db.commit()
                break

            except Exception as e:
                self.db.rollback()
                if attempt == 2:  # Last attempt
                    raise e
                await asyncio.sleep(0.1 * (attempt + 1))  # Exponential backoff

    async def _maybe_cleanup(self):
        """Cleanup old rate limit records if needed."""
        now = datetime.utcnow()
        if now - self.last_cleanup > self.cleanup_interval:
            await self._cleanup_old_records()
            self.last_cleanup = now

    async def _cleanup_old_records(self):
        """Remove expired rate limit records."""
        cutoff_time = datetime.utcnow() - timedelta(hours=2)

        try:
            deleted_count = self.db.query(RateLimit).filter(
                RateLimit.window_end < cutoff_time
            ).delete()

            self.db.commit()

            if deleted_count > 0:
                print(f"Cleaned up {deleted_count} old rate limit records")

        except Exception as e:
            self.db.rollback()
            print(f"Rate limit cleanup failed: {e}")

    async def get_current_usage(self, identifier: str, limit_type: str) -> dict:
        """Get current usage statistics for monitoring."""
        now = datetime.utcnow()
        window_start, window_end = self._get_window_bounds(now, limit_type)

        rate_limit = self.db.query(RateLimit).filter(
            RateLimit.identifier == identifier,
            RateLimit.limit_type == limit_type,
            RateLimit.window_start == window_start
        ).first()

        current_count = rate_limit.request_count if rate_limit else 0
        limit_value = self.limits.get(limit_type, 100)

        return {
            "identifier": identifier,
            "limit_type": limit_type,
            "current_usage": current_count,
            "limit": limit_value,
            "remaining": max(0, limit_value - current_count),
            "window_start": window_start.isoformat(),
            "window_end": window_end.isoformat(),
            "reset_in_seconds": int((window_end - now).total_seconds())
        }


class ElevenLabsQueueManager:
    """Manage concurrent slot allocation for ElevenLabs tasks (e.g., voice over).

    Uses a dedicated DB table active_elevenlabs to cap concurrency to 10 across the service.
    """

    def __init__(self, db: Session):
        self.db = db
        self.max_parallel = 2

    async def acquire_slot(self, task_id: str) -> bool:
        from sqlalchemy import text
        try:
            # Stale slot reaper: clear any stuck slots older than 20 minutes
            stale_cleanup = text("""
                UPDATE active_elevenlabs
                SET is_active = false, updated_at = :now
                WHERE is_active = true
                  AND started_at < (NOW() - INTERVAL '20 minutes')
            """)
            self.db.execute(stale_cleanup, {"now": datetime.utcnow()})
            self.db.commit()

            # Count current active
            count_query = text("""
                SELECT COUNT(*) FROM active_elevenlabs
                WHERE is_active = true
            """)
            result = self.db.execute(count_query)
            current_active = result.fetchone()[0]
            logger.info(f"🗣️ Current active ElevenLabs slots: {current_active}/{self.max_parallel} for task {task_id}")

            if current_active >= self.max_parallel:
                logger.warning(
                    f"🚫 ElevenLabs slots full: {current_active}/{self.max_parallel}, cannot acquire slot for task {task_id}"
                )
                return False

            insert_query = text(
                """
                INSERT INTO active_elevenlabs (workflow_task_execution_id, started_at, is_active)
                VALUES (:task_id, :now, true)
                ON CONFLICT (workflow_task_execution_id) DO UPDATE
                SET is_active = true,
                    started_at = EXCLUDED.started_at,
                    updated_at = :now
                RETURNING id
                """
            )
            result = self.db.execute(insert_query, {"task_id": task_id, "now": datetime.utcnow()})
            row = result.fetchone()
            if row:
                self.db.commit()
                logger.info(f"✅ ElevenLabs slot acquired/activated for task {task_id}, row ID: {row[0]}")
                return True
            else:
                self.db.rollback()
                logger.error(f"❌ UPSERT returned no row for ElevenLabs task {task_id}")
                return False
        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ Failed to acquire ElevenLabs slot for task {task_id}: {e}")
            return False

    async def release_slot(self, task_id: str):
        from sqlalchemy import text
        try:
            update_query = text(
                """
                UPDATE active_elevenlabs
                SET is_active = false, updated_at = :now
                WHERE workflow_task_execution_id = :task_id AND is_active = true
                """
            )
            self.db.execute(update_query, {"task_id": task_id, "now": datetime.utcnow()})
            self.db.commit()
            logger.info(f"ElevenLabs slot released for task {task_id}")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to release ElevenLabs slot for task {task_id}: {e}")



class CloudTasksQueueManager:
    """
    Manages Cloud Tasks queue operations for different task types.

    Handles both emulator (development) and production environments with
    proper OIDC authentication and error handling.
    """

    def __init__(self, queue_config):
        self.queue_config = queue_config
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
        self.location = queue_config.location

        if not self.project_id:
            raise ValueError("GOOGLE_CLOUD_PROJECT environment variable is required")

        # Initialize Cloud Tasks client
        # Check if we're using the emulator
        emulator_host = os.getenv('CLOUD_TASKS_EMULATOR_HOST')
        if emulator_host:
            # For emulator, we'll use HTTP requests instead of the gRPC client
            # to avoid authentication issues
            self.emulator_host = emulator_host
            self.use_emulator = True
            self.client = None
            logger.info("Using Cloud Tasks emulator", extra={"emulator_host": emulator_host})
        else:
            # Use production client
            self.use_emulator = False
            self.client = tasks_v2.CloudTasksClient()
            logger.info("Using production Cloud Tasks client")

    def _get_queue_path(self, queue_name: str) -> str:
        """Get the full queue path for Cloud Tasks."""
        if self.use_emulator:
            return f"projects/{self.project_id}/locations/{self.location}/queues/{queue_name}"
        return self.client.queue_path(self.project_id, self.location, queue_name)

    def _get_service_account_email(self) -> str:
        """Get service account email for OIDC token."""
        # Use configured service account or fall back to default compute SA
        return (os.getenv('GOOGLE_CLOUD_RUN_TASK_SA') or
                f"{os.getenv('GOOGLE_CLOUD_PROJECT_NUMBER', self.project_id)}-<EMAIL>")

    async def enqueue_task(self, queue_name: str, task_data: Dict[str, Any], delay_seconds: int = 0) -> str:
        """Enqueue a task to the specified Cloud Tasks queue."""
        try:
            if self.use_emulator:
                # Use HTTP requests for emulator
                import aiohttp

                url = f"http://{self.emulator_host}/v2/projects/{self.project_id}/locations/{self.location}/queues/{queue_name}/tasks"

                task_payload = {
                    "http_request": {
                        "url": "http://job-management:8001/tasks/process",
                        "body": json.dumps(task_data).encode('utf-8').decode('utf-8'),
                        "headers": {"Content-Type": "application/json"}
                    }
                }

                if delay_seconds > 0:
                    schedule_time = datetime.utcnow() + timedelta(seconds=delay_seconds)
                    task_payload["schedule_time"] = schedule_time.isoformat() + "Z"

                async with aiohttp.ClientSession() as session:
                    async with session.post(url, json=task_payload) as response:
                        if response.status == 200:
                            result = await response.json()
                            return result.get("name", f"task-{datetime.utcnow().timestamp()}")
                        else:
                            raise Exception(f"Emulator returned status {response.status}")
            else:
                # Use production Cloud Tasks client
                parent = self._get_queue_path(queue_name)

                # Use public Cloud Run URL with OIDC auth
                base_url = os.getenv('JOB_MANAGEMENT_BASE_URL')
                if not base_url:
                    raise ValueError('JOB_MANAGEMENT_BASE_URL environment variable is not set')

                task = {
                    'http_request': {
                        'http_method': tasks_v2.HttpMethod.POST,
                        'url': f'{base_url}/tasks/process',
                        'headers': {'Content-Type': 'application/json'},
                        'body': json.dumps(task_data).encode('utf-8'),
                        'oidc_token': {
                            'service_account_email': self._get_service_account_email(),
                            'audience': base_url
                        }
                    }
                }

                # Explicitly set Cloud Tasks dispatch deadline to avoid premature timeouts
                try:
                    from google.protobuf import duration_pb2
                    deadline_seconds = int(os.getenv('CLOUD_TASKS_DISPATCH_DEADLINE_SECONDS', '880'))
                    dispatch_deadline = duration_pb2.Duration()
                    dispatch_deadline.FromSeconds(deadline_seconds)
                    task['dispatch_deadline'] = dispatch_deadline
                except Exception as _e:
                    # If setting deadline fails, proceed without to avoid blocking task creation
                    logger.warning(f"Unable to set dispatch_deadline: {_e}")

                if delay_seconds > 0:
                    from google.protobuf import timestamp_pb2
                    timestamp = timestamp_pb2.Timestamp()
                    timestamp.FromDatetime(datetime.utcnow() + timedelta(seconds=delay_seconds))
                    task['schedule_time'] = timestamp

                response = self.client.create_task(parent=parent, task=task)
                return response.name

        except Exception as e:
            logger.error(
                "Failed to enqueue task",
                extra={
                    "queue_name": queue_name,
                    "error": str(e),
                    "use_emulator": self.use_emulator
                }
            )
            raise

    async def get_queue_stats(self, queue_name: str) -> Dict[str, Any]:
        """Get statistics for a specific queue."""
        try:
            queue_path = self._get_queue_path(queue_name)

            # Get queue information
            queue = self.client.get_queue(name=queue_path)

            # List tasks to get counts (this is a simplified approach)
            # In production, you might want to use queue metrics instead
            tasks = self.client.list_tasks(parent=queue_path)
            task_count = sum(1 for _ in tasks)

            return {
                "queue_name": queue_name,
                "state": queue.state.name if queue.state else "UNKNOWN",
                "pending_tasks": task_count,
                "running_tasks": 0,  # Would need more detailed implementation
                "completed_tasks": 0  # Would need metrics or separate tracking
            }

        except Exception as e:
            logger.warning(
                "Failed to get queue statistics",
                extra={
                    "queue_name": queue_name,
                    "error": str(e)
                }
            )
            return {
                "queue_name": queue_name,
                "pending_tasks": 0,
                "running_tasks": 0,
                "completed_tasks": 0,
                "error": str(e)
            }
