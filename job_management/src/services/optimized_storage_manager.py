"""
Optimized Storage Manager with async operations, batching, and caching.
"""

import asyncio
import aiohttp
import aiofiles
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import json
import hashlib
from google.cloud import storage
from google.cloud.storage import Blob
import redis.asyncio as redis

from .storage_manager import StorageManager

logger = logging.getLogger(__name__)

class OptimizedStorageManager(StorageManager):
    """Enhanced StorageManager with performance optimizations."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        super().__init__()
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.executor = ThreadPoolExecutor(max_workers=20)  # For CPU-bound operations
        
        # Cache settings
        self.cache_ttl = 300  # 5 minutes
        self.metadata_cache_key = "storage:metadata"
        self.result_cache_key = "storage:results"
        
        # Batch operation settings
        self.batch_size = 50
        self.batch_timeout = 2.0  # seconds
        
        # Pending operations for batching
        self._pending_uploads = []
        self._pending_downloads = []
        self._batch_timer = None
    
    # ============================================================================
    # ASYNC FILE OPERATIONS WITH STREAMING
    # ============================================================================
    
    async def upload_file_async(self, local_path: str, gcs_path: str, 
                               content_type: str = None, chunk_size: int = 8192) -> str:
        """Upload file asynchronously with streaming for large files."""
        try:
            # Check cache first
            cache_key = f"upload:{hashlib.md5(gcs_path.encode()).hexdigest()}"
            cached_url = await self.redis_client.get(cache_key)
            if cached_url:
                logger.info(f"📦 Using cached upload URL for {gcs_path}")
                return cached_url
            
            # Get bucket and blob
            bucket = self.client.bucket(self.bucket_name)
            blob = bucket.blob(gcs_path)
            
            # Set content type if provided
            if content_type:
                blob.content_type = content_type
            
            # Stream upload for large files
            async with aiofiles.open(local_path, 'rb') as f:
                # Read file in chunks and upload
                chunks = []
                while True:
                    chunk = await f.read(chunk_size)
                    if not chunk:
                        break
                    chunks.append(chunk)
                
                # Combine chunks and upload
                file_data = b''.join(chunks)
                
                # Use thread pool for blocking GCS operation
                await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    lambda: blob.upload_from_string(file_data, content_type=content_type)
                )
            
            # Generate signed URL
            url = await self._generate_signed_url_async(blob)
            
            # Cache the result
            await self.redis_client.setex(cache_key, self.cache_ttl, url)
            
            logger.info(f"✅ Uploaded {local_path} to {gcs_path}")
            return url
            
        except Exception as e:
            logger.error(f"❌ Failed to upload {local_path} to {gcs_path}: {e}")
            raise
    
    async def download_file_async(self, gcs_path: str, local_path: str, 
                                 chunk_size: int = 8192) -> bool:
        """Download file asynchronously with streaming."""
        try:
            bucket = self.client.bucket(self.bucket_name)
            blob = bucket.blob(gcs_path)
            
            # Check if blob exists
            exists = await asyncio.get_event_loop().run_in_executor(
                self.executor, blob.exists
            )
            if not exists:
                logger.error(f"❌ Blob {gcs_path} does not exist")
                return False
            
            # Stream download
            async with aiofiles.open(local_path, 'wb') as f:
                # Download in chunks using thread pool
                blob_data = await asyncio.get_event_loop().run_in_executor(
                    self.executor, blob.download_as_bytes
                )
                
                # Write in chunks
                for i in range(0, len(blob_data), chunk_size):
                    chunk = blob_data[i:i + chunk_size]
                    await f.write(chunk)
            
            logger.info(f"✅ Downloaded {gcs_path} to {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to download {gcs_path} to {local_path}: {e}")
            return False
    
    async def _generate_signed_url_async(self, blob: Blob, expiration_hours: int = 24) -> str:
        """Generate signed URL asynchronously."""
        expiration = datetime.utcnow() + timedelta(hours=expiration_hours)
        
        return await asyncio.get_event_loop().run_in_executor(
            self.executor,
            lambda: blob.generate_signed_url(expiration=expiration, method='GET')
        )
    
    # ============================================================================
    # BATCH OPERATIONS
    # ============================================================================
    
    async def batch_upload_files(self, file_pairs: List[Tuple[str, str]], 
                                content_type: str = None) -> List[str]:
        """Upload multiple files in parallel with optimal concurrency."""
        if not file_pairs:
            return []
        
        # Limit concurrency to avoid overwhelming GCS
        semaphore = asyncio.Semaphore(10)
        
        async def upload_with_semaphore(local_path: str, gcs_path: str):
            async with semaphore:
                return await self.upload_file_async(local_path, gcs_path, content_type)
        
        # Execute uploads in parallel
        tasks = [
            upload_with_semaphore(local_path, gcs_path)
            for local_path, gcs_path in file_pairs
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and log errors
        urls = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Batch upload failed for {file_pairs[i]}: {result}")
                urls.append(None)
            else:
                urls.append(result)
        
        return urls
    
    async def batch_download_files(self, file_pairs: List[Tuple[str, str]]) -> List[bool]:
        """Download multiple files in parallel."""
        if not file_pairs:
            return []
        
        semaphore = asyncio.Semaphore(10)
        
        async def download_with_semaphore(gcs_path: str, local_path: str):
            async with semaphore:
                return await self.download_file_async(gcs_path, local_path)
        
        tasks = [
            download_with_semaphore(gcs_path, local_path)
            for gcs_path, local_path in file_pairs
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Convert exceptions to False
        return [
            result if not isinstance(result, Exception) else False
            for result in results
        ]
    
    # ============================================================================
    # CACHED METADATA OPERATIONS
    # ============================================================================
    
    async def get_job_result_cached(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job result with Redis caching."""
        cache_key = f"{self.result_cache_key}:{job_id}"
        
        # Check cache first
        cached_result = await self.redis_client.get(cache_key)
        if cached_result:
            try:
                return json.loads(cached_result)
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON in cache for job {job_id}")
        
        # Fetch from storage
        result = await self.get_job_result(job_id)
        
        # Cache the result if found
        if result:
            await self.redis_client.setex(
                cache_key, 
                self.cache_ttl, 
                json.dumps(result, default=str)
            )
        
        return result
    
    async def store_job_result_cached(self, job_id: str, result_data: Dict[str, Any]) -> bool:
        """Store job result and update cache."""
        # Store in GCS
        success = await self.store_job_result(job_id, result_data)
        
        if success:
            # Update cache
            cache_key = f"{self.result_cache_key}:{job_id}"
            await self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                json.dumps(result_data, default=str)
            )
        
        return success
    
    async def get_file_metadata_batch(self, gcs_paths: List[str]) -> List[Optional[Dict[str, Any]]]:
        """Get metadata for multiple files in parallel."""
        if not gcs_paths:
            return []
        
        # Check cache for all paths
        cache_keys = [f"{self.metadata_cache_key}:{path}" for path in gcs_paths]
        cached_results = await self.redis_client.mget(cache_keys)
        
        # Identify missing items
        missing_indices = []
        results = [None] * len(gcs_paths)
        
        for i, cached in enumerate(cached_results):
            if cached:
                try:
                    results[i] = json.loads(cached)
                except json.JSONDecodeError:
                    missing_indices.append(i)
            else:
                missing_indices.append(i)
        
        # Fetch missing metadata in parallel
        if missing_indices:
            missing_paths = [gcs_paths[i] for i in missing_indices]
            
            async def get_metadata(gcs_path: str):
                try:
                    bucket = self.client.bucket(self.bucket_name)
                    blob = bucket.blob(gcs_path)
                    
                    # Get metadata using thread pool
                    blob.reload()
                    return {
                        "name": blob.name,
                        "size": blob.size,
                        "content_type": blob.content_type,
                        "created": blob.time_created.isoformat() if blob.time_created else None,
                        "updated": blob.updated.isoformat() if blob.updated else None,
                        "etag": blob.etag
                    }
                except Exception as e:
                    logger.error(f"Error getting metadata for {gcs_path}: {e}")
                    return None
            
            semaphore = asyncio.Semaphore(20)
            
            async def get_metadata_with_semaphore(gcs_path: str):
                async with semaphore:
                    return await asyncio.get_event_loop().run_in_executor(
                        self.executor, lambda: get_metadata(gcs_path)
                    )
            
            missing_tasks = [
                get_metadata_with_semaphore(path) for path in missing_paths
            ]
            
            missing_results = await asyncio.gather(*missing_tasks, return_exceptions=True)
            
            # Update results and cache
            cache_updates = {}
            for i, result in enumerate(missing_results):
                idx = missing_indices[i]
                if not isinstance(result, Exception) and result:
                    results[idx] = result
                    cache_key = f"{self.metadata_cache_key}:{gcs_paths[idx]}"
                    cache_updates[cache_key] = json.dumps(result, default=str)
            
            # Batch update cache
            if cache_updates:
                pipe = self.redis_client.pipeline()
                for key, value in cache_updates.items():
                    pipe.setex(key, self.cache_ttl, value)
                await pipe.execute()
        
        return results
    
    # ============================================================================
    # CLEANUP AND MONITORING
    # ============================================================================
    
    async def cleanup_temp_files_batch(self, job_ids: List[str]) -> int:
        """Clean up temporary files for multiple jobs in parallel."""
        if not job_ids:
            return 0
        
        # Get all temp paths
        temp_paths = []
        for job_id in job_ids:
            temp_path = self.get_temp_processing_path(job_id, "")
            temp_paths.append(temp_path.rstrip('/'))
        
        # Delete in parallel
        semaphore = asyncio.Semaphore(10)
        
        async def delete_temp_folder(temp_path: str):
            async with semaphore:
                try:
                    bucket = self.client.bucket(self.bucket_name)
                    blobs = bucket.list_blobs(prefix=temp_path)
                    
                    # Delete all blobs in the temp folder
                    deleted_count = 0
                    for blob in blobs:
                        await asyncio.get_event_loop().run_in_executor(
                            self.executor, blob.delete
                        )
                        deleted_count += 1
                    
                    return deleted_count
                except Exception as e:
                    logger.error(f"Error cleaning up temp folder {temp_path}: {e}")
                    return 0
        
        tasks = [delete_temp_folder(path) for path in temp_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_deleted = sum(
            result for result in results 
            if not isinstance(result, Exception)
        )
        
        logger.info(f"🧹 Cleaned up {total_deleted} temp files for {len(job_ids)} jobs")
        return total_deleted
    
    async def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage usage statistics."""
        try:
            bucket = self.client.bucket(self.bucket_name)
            
            # Count objects and total size by prefix
            prefixes = ["assets/", "temp/", "results/"]
            stats = {}
            
            for prefix in prefixes:
                blobs = bucket.list_blobs(prefix=prefix)
                count = 0
                total_size = 0
                
                for blob in blobs:
                    count += 1
                    total_size += blob.size or 0
                
                stats[prefix.rstrip('/')] = {
                    "count": count,
                    "total_size_bytes": total_size,
                    "total_size_mb": round(total_size / (1024 * 1024), 2)
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting storage stats: {e}")
            return {"error": str(e)}
    
    async def close(self):
        """Clean up resources."""
        await self.redis_client.close()
        self.executor.shutdown(wait=True)
