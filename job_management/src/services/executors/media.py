"""
Media task executors extracted from TaskExecutor to improve modularity.

These helpers keep the same behavior and return shapes as the original
TaskExecutor methods and are intended to be used as:

    await execute_album_cover_generation(executor, job, parameters)

where `executor` is an instance of TaskExecutor providing:
- api_base_url
- storage_manager
"""
from __future__ import annotations

import logging
from datetime import datetime, timezone
from typing import Any, Dict

# Type hints for clarity
try:
    from models import Job  # noqa: F401
except Exception:  # pragma: no cover - type hints only
    Job = object  # type: ignore

from utils.http_client import (
    robust_json_request,
    HTTPClientError,
    HTTPClientTimeoutError,
    HTTPRateLimitError,
)

logger = logging.getLogger(__name__)


async def execute_album_cover_generation(executor, job: "Job", parameters: Dict[str, Any]) -> Dict[str, Any]:
    try:
        prompt = parameters.get("prompt", "")
        creativity = parameters.get("creativity", 0.7)
        model = parameters.get("model", "imagen")

        if not prompt:
            return {"error": "Prompt is required for album cover generation"}

        try:
            payload = {
                "prompt": prompt,
                "model_creativity": creativity,
                "model": model,
            }

            result = await robust_json_request(
                "POST",
                f"{executor.api_base_url}/album-cover-art",
                "read",
                json=payload,
            )

            image_base64 = result.get("image_base64")
            if not image_base64:
                return {"error": "API response missing image_base64 data"}

            gcs_path = await executor.storage_manager.store_album_cover(
                job_id=str(job.job_id),
                image_base64=image_base64,
                filename="album_cover.jpg",
            )

            signed_url = None
            if gcs_path:
                signed_url = await executor.storage_manager.get_album_cover_url(
                    job_id=str(job.job_id),
                    filename="album_cover.jpg",
                )

            enhanced_result = {
                "success": True,
                "task_type": "album_cover_generation",
                "result": {
                    **result,
                    "storage": {
                        "gcs_path": gcs_path,
                        "signed_url": signed_url,
                        "stored_at": datetime.now(timezone.utc).isoformat(),
                    },
                },
            }

            await executor.storage_manager.store_job_result(
                job_id=str(job.job_id),
                result_data=enhanced_result,
            )

            return enhanced_result

        except (HTTPClientTimeoutError, HTTPClientError, HTTPRateLimitError) as e:
            logger.error(f"HTTP client error during album cover generation: {e}")
            if isinstance(e, HTTPRateLimitError):
                retry_after = getattr(e, "retry_after", 60)
                return {
                    "error": f"Rate limited: {str(e)}",
                    "retry_after": retry_after,
                    "status": "requeued",
                }
            # Raise to let Cloud Tasks handle retries
            raise Exception(f"Album cover generation HTTP error: {str(e)}") from e

    except Exception as e:  # pragma: no cover - defensive catch
        logger.error(f"Album cover generation failed with unexpected error: {e}")
        return {"error": f"Album cover generation failed: {str(e)}"}

