"""
MusicGPT-related task executors extracted from TaskExecutor to improve modularity.

Helpers here implement the original behavior and return shapes for:
- music generation
- sound generation
- music extension
- music remixing (mock)

They are intended to be called as methods with the TaskExecutor instance, e.g.:
    await execute_music_generation(executor, job, parameters)
    await execute_music_generation_with_slots(executor, job, parameters, task_execution_id)

The slot wrappers preserve the same semantics (requeue payloads, error handling).
"""
from __future__ import annotations

import asyncio
import json
import logging
import os
from datetime import datetime, timezone
from typing import Any, Dict, Optional
from urllib.parse import urlparse

try:
    from models import Job  # noqa: F401
except Exception:  # pragma: no cover - type hints only
    Job = object  # type: ignore

import aiohttp
from utils.http_client import (
    RobustWebSocketClient,
    robust_download,
    HTTPClientError,
    HTTPClientTimeoutError,
    HTTPRateLimitError,
)
from services.task_utils import (
    with_musicgpt_slot,
    log_task_event,
    ws_task_runner,
    ws_url_for,
    download_and_store_audio,
)

logger = logging.getLogger(__name__)


# Slot wrappers
async def execute_music_generation_with_slots(executor, job: "Job", parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
    log_task_event(
        logger,
        "info",
        "🎵 _execute_music_generation_with_slots called",
        job_id=str(job.job_id),
        task_type="music_generation",
        task_execution_id=task_execution_id,
    )

    def requeue_payload():
        return {
            "status": "requeued",
            "message": "No MusicGPT slots available, job will be retried",
            "retry_after": 30,
        }

    async def run():
        result = await execute_music_generation(executor, job, parameters)
        if result is None:
            log_task_event(
                logger,
                "error",
                "Music generation returned None",
                job_id=str(job.job_id),
                task_type="music_generation",
                task_execution_id=task_execution_id,
            )
            return {"error": "Music generation returned no result"}
        return result

    try:
        return await with_musicgpt_slot(executor.db, task_execution_id, run, requeue_payload)
    except Exception as e:
        log_task_event(
            logger,
            "error",
            f"Music generation failed: {e}",
            job_id=str(job.job_id),
            task_type="music_generation",
            task_execution_id=task_execution_id,
        )
        return {"error": f"Music generation failed: {str(e)}"}


async def execute_sound_generation_with_slots(executor, job: "Job", parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
    log_task_event(
        logger,
        "info",
        "🔊 _execute_sound_generation_with_slots called",
        job_id=str(job.job_id),
        task_type="sound_generation",
        task_execution_id=task_execution_id,
    )

    def requeue_payload():
        return {
            "error": "No MusicGPT slots available for sound generation",
            "status": "requeued",
            "retry_after": 30,
        }

    async def run():
        result = await execute_sound_generation(executor, job, parameters)
        if result is None:
            log_task_event(
                logger,
                "error",
                "Sound generation returned None",
                job_id=str(job.job_id),
                task_type="sound_generation",
                task_execution_id=task_execution_id,
            )
            return {"error": "Sound generation returned no result"}
        return result

    try:
        return await with_musicgpt_slot(executor.db, task_execution_id, run, requeue_payload)
    except Exception as e:
        log_task_event(
            logger,
            "error",
            f"Sound generation failed: {e}",
            job_id=str(job.job_id),
            task_type="sound_generation",
            task_execution_id=task_execution_id,
        )
        return {"error": f"Sound generation failed: {str(e)}"}


async def execute_music_extension_with_slots(executor, job: "Job", parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
    log_task_event(
        logger,
        "info",
        "🎵🔄 _execute_music_extension_with_slots called",
        job_id=str(job.job_id),
        task_type="music_extension",
        task_execution_id=task_execution_id,
    )

    def requeue_payload():
        return {
            "status": "requeued",
            "message": "No MusicGPT slots available, job will be retried",
            "retry_after": 30,
        }

    async def run():
        result = await execute_music_extension(executor, job, parameters)
        if result is None:
            log_task_event(
                logger,
                "error",
                "Music extension returned None",
                job_id=str(job.job_id),
                task_type="music_extension",
                task_execution_id=task_execution_id,
            )
            return {"error": "Music extension returned no result"}
        return result

    try:
        return await with_musicgpt_slot(executor.db, task_execution_id, run, requeue_payload)
    except Exception as e:
        log_task_event(
            logger,
            "error",
            f"Music extension failed: {e}",
            job_id=str(job.job_id),
            task_type="music_extension",
            task_execution_id=task_execution_id,
        )
        return {"error": f"Music extension failed: {str(e)}"}


async def execute_music_remixing_with_slots(executor, job: "Job", parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
    log_task_event(
        logger,
        "info",
        "🎵🎛️ _execute_music_remixing_with_slots called",
        job_id=str(job.job_id),
        task_type="music_remixing",
        task_execution_id=task_execution_id,
    )

    def requeue_payload():
        return {
            "status": "requeued",
            "message": "No MusicGPT slots available, job will be retried",
            "retry_after": 30,
        }

    async def run():
        result = await execute_music_remixing(executor, job, parameters)
        if result is None:
            log_task_event(
                logger,
                "error",
                "Music remixing returned None",
                job_id=str(job.job_id),
                task_type="music_remixing",
                task_execution_id=task_execution_id,
            )
            return {"error": "Music remixing returned no result"}
        return result

    try:
        return await with_musicgpt_slot(executor.db, task_execution_id, run, requeue_payload)
    except Exception as e:
        log_task_event(
            logger,
            "error",
            f"Music remixing failed: {e}",
            job_id=str(job.job_id),
            task_type="music_remixing",
            task_execution_id=task_execution_id,
        )
        return {"error": f"Music remixing failed: {str(e)}"}


async def execute_vocal_extraction_with_slots(
    executor,
    job: "Job",
    parameters: Dict[str, Any],
    task_execution_id: str,
) -> Dict[str, Any]:
    """Slot-managed wrapper around execute_vocal_extraction.

    Ensures MusicGPT concurrency cap across tasks.
    """

    log_task_event(
        logger,
        "info",
        "🎤 execute_vocal_extraction_with_slots called",
        job_id=str(job.job_id),
        task_type="vocal_extraction",
        task_execution_id=task_execution_id,
    )

    def requeue_payload():
        return {
            "status": "requeued",
            "message": "No MusicGPT slots available, job will be retried",
            "retry_after": 30,
        }

    async def run():
        result = await execute_vocal_extraction(executor, job, parameters)
        if result is None:
            log_task_event(
                logger,
                "error",
                "Vocal extraction returned None",
                job_id=str(job.job_id),
                task_type="vocal_extraction",
                task_execution_id=task_execution_id,
            )
            return {"error": "Vocal extraction returned no result"}
        return result

    try:
        return await with_musicgpt_slot(executor.db, task_execution_id, run, requeue_payload)
    except Exception as e:
        log_task_event(
            logger,
            "error",
            f"Vocal extraction failed: {e}",
            job_id=str(job.job_id),
            task_type="vocal_extraction",
            task_execution_id=task_execution_id,
        )
        return {"error": f"Vocal extraction failed: {str(e)}"}


# Core executors
async def execute_music_generation(executor, job: "Job", parameters: Dict[str, Any]) -> Dict[str, Any]:
    try:
        # Accept either a direct prompt or derive one from genre/mood/duration
        prompt: Optional[str] = parameters.get("prompt")
        genre: Optional[str] = parameters.get("genre")
        mood: Optional[str] = parameters.get("mood")
        duration: Optional[int] = parameters.get("duration")

        if not prompt:
            if not genre and not mood:
                return {"error": "Provide either 'prompt' or at least one of 'genre'/'mood'"}
            # Build a simple prompt from available fields
            prompt_parts = []
            if mood:
                prompt_parts.append(mood)
            if genre:
                prompt_parts.append(genre)
            if duration:
                prompt_parts.append(f"~{duration}s")
            prompt = " ".join(prompt_parts).strip() or "music generation"

        ws_url = ws_url_for(executor.api_base_url, "/generate-music")

        completed_payload: Optional[Dict[str, Any]] = None

        # Attempt real generation over WebSocket with robust timeout handling
        try:
            def build_payload() -> Dict[str, Any]:
                payload = {"prompt": prompt}
                if genre:
                    payload["music_style"] = genre
                if parameters.get("lyrics"):
                    payload["lyrics"] = parameters.get("lyrics")
                if parameters.get("make_instrumental") is not None:
                    payload["make_instrumental"] = bool(parameters.get("make_instrumental"))
                if parameters.get("vocal_only") is not None:
                    payload["vocal_only"] = bool(parameters.get("vocal_only"))
                if parameters.get("voice_id"):
                    payload["voice_id"] = parameters.get("voice_id")
                return payload

            async def message_handler(ws):
                nonlocal completed_payload
                async for msg in ws:
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        data = json.loads(msg.data)
                        if data.get("error"):
                            raise RuntimeError(f"API WS error: {data.get('data') or data}")
                        status = data.get("status")
                        payload = data.get("data", {})
                        logger.info(f"WebSocket status: {status}")
                        if status == "completed":
                            completed_payload = payload
                            return payload
                    elif msg.type == aiohttp.WSMsgType.ERROR:
                        raise RuntimeError(f"WebSocket error: {ws.exception()}")
                return None

            await ws_task_runner(
                ws_url,
                "MUSIC_GEN_TIMEOUT",
                build_payload,
                message_handler,
            )

            if completed_payload:
                variant_keys = [
                    (["song_1_wav_url", "song_1_url"], "song_1"),
                    (["song_2_wav_url", "song_2_url"], "song_2"),
                ]
                saved_tracks = []
                logger.info(
                    f"Processing {len(variant_keys)} potential audio variants for job {job.job_id}"
                )
                for keys, base_name in variant_keys:
                    audio_url = None
                    for k in keys:
                        v = completed_payload.get(k)
                        if v:
                            audio_url = v
                            break
                    if not audio_url:
                        logger.debug(f"No audio URL found for keys {keys}")
                        continue

                    path = urlparse(audio_url).path or ""
                    ext = os.path.splitext(path)[1].lower()
                    if ext != ".wav":
                        logger.debug(
                            f"Skipping non-WAV file: {audio_url} (extension: {ext})"
                        )
                        continue

                    try:
                        artifacts = await download_and_store_audio(
                            [audio_url],
                            {"filename_base": base_name, "prefer_exts": [".wav"]},
                            executor.storage_manager,
                            str(job.job_id),
                            "music_generation",
                        )
                        if artifacts:
                            artifact = artifacts[0]
                            artifact["source_key"] = ",".join(keys)
                            saved_tracks.append(artifact)
                    except (HTTPClientError, HTTPClientTimeoutError, HTTPRateLimitError) as e:
                        logger.warning(f"Failed to download {audio_url}: {e}")
                        continue
                    except Exception as e:
                        logger.warning(f"Unexpected error downloading {audio_url}: {e}")
                        continue

                if not saved_tracks:
                    logger.error(
                        f"No audio tracks were successfully downloaded for job {job.job_id}"
                    )
                    logger.error(
                        f"Available audio URLs were: {[completed_payload.get(k) for keys, _ in variant_keys for k in keys if completed_payload.get(k)]}"
                    )
                    raise Exception(
                        "No audio tracks were successfully downloaded - all download attempts failed"
                    )

                result_payload: Dict[str, Any] = {
                    "success": True,
                    "task_type": "music_generation",
                    "result": {
                        "message": "Music generated successfully",
                        "prompt": prompt,
                        "parameters": {"genre": genre, "mood": mood, "duration": duration},
                        "job_id": str(job.job_id),
                        "api_result": completed_payload,
                        "storage": {
                            "tracks": saved_tracks,
                            "primary_track": saved_tracks[0] if saved_tracks else None,
                            "stored_at": datetime.now(timezone.utc).isoformat(),
                        },
                    },
                }

                await executor.storage_manager.store_job_result(
                    job_id=str(job.job_id),
                    result_data=result_payload,
                )
                return result_payload
            else:
                raise RuntimeError(
                    "Music generation did not complete - no completion payload received from WebSocket"
                )
        except (HTTPClientTimeoutError, HTTPClientError, HTTPRateLimitError) as e:
            logger.error(f"HTTP client error during music generation: {e}")
            if isinstance(e, HTTPRateLimitError):
                retry_after = getattr(e, "retry_after", 60)
                return {
                    "error": f"Rate limited: {str(e)}",
                    "retry_after": retry_after,
                    "status": "requeued",
                }
            raise Exception(f"Music generation HTTP error: {str(e)}") from e
        except Exception as e:
            logger.error(f"Unexpected error during music generation: {e}")
            raise
    except Exception as e:
        logger.error(f"Music generation failed with unexpected error: {e}")
        return {"error": f"Music generation failed: {str(e)}"}


async def execute_sound_generation(executor, job: "Job", parameters: Dict[str, Any]) -> Dict[str, Any]:
    try:
        prompt: Optional[str] = parameters.get("prompt")
        audio_length_seconds: int = parameters.get("audio_length_seconds", 10)
        if not prompt:
            return {"error": "Prompt is required for sound generation"}

        ws_url = ws_url_for(executor.api_base_url, "/sound-generation")
        completed_payload: Optional[Dict[str, Any]] = None
        try:
            def build_payload() -> Dict[str, Any]:
                return {"prompt": prompt, "audio_length_seconds": audio_length_seconds}

            async def message_handler(ws):
                nonlocal completed_payload
                async for msg in ws:
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        data = json.loads(msg.data)
                        if data.get("error"):
                            raise RuntimeError(f"API WS error: {data.get('data') or data}")
                        status = data.get("status")
                        payload = data.get("data", {})
                        logger.info(f"WebSocket status: {status}")
                        if status == "completed":
                            completed_payload = payload
                            return payload
                    elif msg.type == aiohttp.WSMsgType.ERROR:
                        raise RuntimeError(f"WebSocket error: {ws.exception()}")
                return None

            completed_payload = await ws_task_runner(
                ws_url,
                "SOUND_GEN_TIMEOUT",
                build_payload,
                message_handler,
            )

            if completed_payload:
                sound_url = completed_payload.get("sound_url")
                sound_wav_url = completed_payload.get("sound_wav_url")
                if not (sound_url or sound_wav_url):
                    logger.error(
                        f"No audio URL found in completion payload for job {job.job_id}"
                    )
                    raise Exception("No audio URL found in completion payload")

                try:
                    saved_sounds = await download_and_store_audio(
                        [sound_wav_url, sound_url],
                        {"filename_base": "generated_sound", "prefer_exts": [".wav", ".mp3"]},
                        executor.storage_manager,
                        str(job.job_id),
                        "sound_generation",
                    )
                except (HTTPClientError, HTTPClientTimeoutError, HTTPRateLimitError) as e:
                    logger.error(f"Failed to download generated sound: {e}")
                    raise Exception(f"Failed to download generated sound: {str(e)}")
                except Exception as e:
                    logger.error(
                        f"Unexpected error downloading generated sound: {e}"
                    )
                    raise Exception(
                        f"Unexpected error downloading generated sound: {str(e)}"
                    )

                if not saved_sounds:
                    logger.error(
                        f"No sound files were successfully downloaded for job {job.job_id}"
                    )
                    raise Exception("No sound files were successfully downloaded")

                result_payload: Dict[str, Any] = {
                    "success": True,
                    "task_type": "sound_generation",
                    "result": {
                        "message": "Sound generated successfully",
                        "prompt": prompt,
                        "parameters": {"audio_length_seconds": audio_length_seconds},
                        "job_id": str(job.job_id),
                        "api_result": completed_payload,
                        "storage": {
                            "sounds": saved_sounds,
                            "primary_sound": saved_sounds[0] if saved_sounds else None,
                            "stored_at": datetime.now(timezone.utc).isoformat(),
                        },
                    },
                }

                await executor.storage_manager.store_job_result(
                    job_id=str(job.job_id),
                    result_data=result_payload,
                )
                return result_payload
            else:
                raise RuntimeError(
                    "Sound generation did not complete - no completion payload received from WebSocket"
                )
        except (HTTPClientTimeoutError, HTTPClientError, HTTPRateLimitError) as e:
            logger.error(f"HTTP client error during sound generation: {e}")
            if isinstance(e, HTTPRateLimitError):
                retry_after = getattr(e, "retry_after", 60)
                return {
                    "error": f"Rate limited: {str(e)}",
                    "retry_after": retry_after,
                    "status": "requeued",
                }
            raise Exception(f"Sound generation HTTP error: {str(e)}") from e
        except Exception as e:
            logger.error(f"Unexpected error during sound generation: {e}")
            raise
    except Exception as e:
        logger.error(f"Sound generation failed with unexpected error: {e}")
        return {"error": f"Sound generation failed: {str(e)}"}


async def execute_music_extension(executor, job: "Job", parameters: Dict[str, Any]) -> Dict[str, Any]:
    try:
        audio_gcs_path: Optional[str] = parameters.get("audio_gcs_path")
        extend_after_seconds: int = parameters.get("extend_after_seconds", 30)
        prompt: Optional[str] = parameters.get("prompt")
        lyrics: Optional[str] = parameters.get("lyrics")

        if not audio_gcs_path:
            return {"error": "audio_gcs_path is required for music extension"}
        if not prompt:
            return {"error": "Prompt is required for music extension"}

        try:
            logger.info(f"Downloading source audio from GCS: {audio_gcs_path}")
            audio_bytes = await executor.storage_manager.download_as_bytes(audio_gcs_path)
            logger.info(f"Downloaded {len(audio_bytes)} bytes from {audio_gcs_path}")
        except Exception as e:
            logger.error(
                f"Failed to download source audio from {audio_gcs_path}: {e}"
            )
            return {"error": f"Failed to download source audio: {str(e)}"}

        import base64
        audio_base64 = base64.b64encode(audio_bytes).decode("utf-8")

        ws_url = ws_url_for(executor.api_base_url, "/extend-music")
        completed_payload: Optional[Dict[str, Any]] = None
        try:
            timeout_seconds = int(os.getenv("MUSIC_EXT_TIMEOUT", "1800"))

            ws_payload = {
                "audio_base64": audio_base64,
                "extend_after_seconds": extend_after_seconds,
                "prompt": prompt,
            }
            if lyrics:
                ws_payload["lyrics"] = lyrics

            async def message_handler(ws):
                nonlocal completed_payload
                async for msg in ws:
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        data = json.loads(msg.data)
                        if data.get("error"):
                            raise RuntimeError(f"API WS error: {data.get('data') or data}")
                        status = data.get("status")
                        payload = data.get("data", {})
                        logger.info(f"WebSocket status: {status}")
                        if status == "completed":
                            completed_payload = payload
                            return payload
                    elif msg.type == aiohttp.WSMsgType.ERROR:
                        raise RuntimeError(f"WebSocket error: {ws.exception()}")
                return None

            await ws_task_runner(
                ws_url,
                "MUSIC_EXT_TIMEOUT",
                lambda: ws_payload,
                message_handler,
            )

            if completed_payload:
                variant_keys = [
                    (["song_1_wav_url", "song_1_url"], "extended_song_1"),
                    (["song_2_wav_url", "song_2_url"], "extended_song_2"),
                ]
                saved_tracks = []
                logger.info(
                    f"Processing {len(variant_keys)} potential audio variants for job {job.job_id}"
                )
                for keys, base_name in variant_keys:
                    audio_url = None
                    for k in keys:
                        v = completed_payload.get(k)
                        if v:
                            audio_url = v
                            break
                    if not audio_url:
                        logger.debug(f"No audio URL found for keys {keys}")
                        continue

                    path = urlparse(audio_url).path or ""
                    ext = os.path.splitext(path)[1].lower()
                    if ext not in [".wav", ".mp3"]:
                        logger.debug(
                            f"Skipping unsupported audio format: {audio_url} (extension: {ext})"
                        )
                        continue

                    filename = f"{base_name}{ext}"
                    storage_path = executor.storage_manager.storage.get_music_path(
                        str(job.job_id), filename
                    )
                    logger.info(f"Attempting to download {filename} from {audio_url}")

                    try:
                        audio_bytes = await robust_download(audio_url)
                        content_type = "audio/wav" if ext == ".wav" else "audio/mpeg"
                        await executor.storage_manager.upload_from_bytes(
                            audio_bytes,
                            storage_path,
                            content_type=content_type,
                        )
                        signed_url = await executor.storage_manager.get_signed_url(
                            storage_path, expiration_minutes=60
                        )
                        saved_tracks.append(
                            {
                                "source_key": ",".join(keys),
                                "source_url": audio_url,
                                "filename": filename,
                                "gcs_path": f"gs://{executor.storage_manager.storage.bucket_name}/{storage_path}",
                                "signed_url": signed_url,
                            }
                        )
                        logger.info(f"Successfully downloaded and stored {filename}")
                    except (HTTPClientError, HTTPClientTimeoutError, HTTPRateLimitError) as e:
                        logger.warning(f"Failed to download {audio_url}: {e}")
                        continue
                    except Exception as e:
                        logger.warning(f"Unexpected error downloading {audio_url}: {e}")
                        continue

                result_payload: Dict[str, Any] = {
                    "success": True,
                    "task_type": "music_extension",
                    "result": {
                        "message": "Music extended successfully",
                        "source_audio": audio_gcs_path,
                        "extend_after_seconds": extend_after_seconds,
                        "prompt": prompt,
                        "parameters": {"lyrics": lyrics},
                        "job_id": str(job.job_id),
                        "api_result": completed_payload,
                        "storage": {
                            "tracks": saved_tracks,
                            "primary_track": saved_tracks[0] if saved_tracks else None,
                            "stored_at": datetime.now(timezone.utc).isoformat(),
                        },
                    },
                }

                await executor.storage_manager.store_job_result(
                    job_id=str(job.job_id),
                    result_data=result_payload,
                )
                return result_payload
            else:
                raise RuntimeError(
                    "Music extension did not complete - no completion payload received from WebSocket"
                )
        except (HTTPClientTimeoutError, HTTPClientError, HTTPRateLimitError) as e:
            logger.error(f"HTTP client error during music extension: {e}")
            if isinstance(e, HTTPRateLimitError):
                retry_after = getattr(e, "retry_after", 60)
                return {
                    "error": f"Rate limited: {str(e)}",
                    "retry_after": retry_after,
                    "status": "requeued",
                }
            raise Exception(f"Music extension HTTP error: {str(e)}") from e
        except Exception as e:
            logger.error(f"Unexpected error during music extension: {e}")
            raise
    except Exception as e:
        logger.error(f"Music extension failed with unexpected error: {e}")
        return {"error": f"Music extension failed: {str(e)}"}


async def execute_music_remixing(executor, job: "Job", parameters: Dict[str, Any]) -> Dict[str, Any]:
    try:
        audio_gcs_path: Optional[str] = parameters.get("audio_gcs_path")
        prompt: str = parameters.get("prompt", "")
        lyrics: Optional[str] = parameters.get("lyrics")
        if not audio_gcs_path:
            return {"error": "audio_gcs_path is required for music remixing"}
        if not prompt:
            return {"error": "prompt is required for music remixing"}

        logger.info(
            f"🎵🎛️ Starting music remixing with audio_gcs_path: {audio_gcs_path}, prompt: {prompt}"
        )

        try:
            audio_data = await executor.storage_manager.download_as_bytes(audio_gcs_path)
            if not audio_data:
                return {"error": f"Failed to download audio from GCS path: {audio_gcs_path}"}
            logger.info(f"Downloaded audio data: {len(audio_data)} bytes")
        except Exception as e:
            logger.error(f"Error downloading audio from GCS: {e}")
            return {"error": f"Failed to download audio from GCS: {str(e)}"}

        import base64
        audio_base64 = base64.b64encode(audio_data).decode("utf-8")
        logger.info(f"Converted audio to base64: {len(audio_base64)} chars")

        ws_data = {"audio_base64": audio_base64, "prompt": prompt}
        if lyrics:
            ws_data["lyrics"] = lyrics

        logger.info(
            f"🎵🎛️ Starting mock music remixing (WebSocket endpoint not yet implemented)"
        )

        from utils.http_client import (
            robust_json_request,
            HTTPClientTimeoutError,
            HTTPClientError,
            HTTPRateLimitError,
        )

        try:
            await asyncio.sleep(3)
            logger.info(f"🎵🎛️ Mock remixing processing completed")
            completion_message = {
                "status": "completed",
                "song_1_url": "mock://remixed_song_1.mp3",
                "song_1_wav_url": "mock://remixed_song_1.wav",
                "song_2_url": "mock://remixed_song_2.mp3",
                "song_2_wav_url": "mock://remixed_song_2.wav",
                "music_gpt_task_id": f"mock_remix_{job.job_id}",
            }
            song_1_url = completion_message.get("song_1_url")
            song_1_wav_url = completion_message.get("song_1_wav_url")
            song_2_url = completion_message.get("song_2_url")
            song_2_wav_url = completion_message.get("song_2_wav_url")
            music_gpt_task_id = completion_message.get("music_gpt_task_id")

            logger.info(
                f"🎵🎛️ Music remixing completed with URLs: {song_1_url}, {song_1_wav_url}, {song_2_url}, {song_2_wav_url}"
            )

            storage_results = {}
            for i, (mp3_url, wav_url) in enumerate(
                [(song_1_url, song_1_wav_url), (song_2_url, song_2_wav_url)], 1
            ):
                if mp3_url and mp3_url.startswith("mock://"):
                    try:
                        original_audio_data = await executor.storage_manager.download_as_bytes(
                            audio_gcs_path
                        )
                        filename = f"remixed_song_{i}.mp3"
                        storage_path = executor.storage_manager.storage.get_music_path(
                            str(job.job_id), filename
                        )
                        await executor.storage_manager.upload_from_bytes(
                            original_audio_data,
                            storage_path,
                            content_type="audio/mpeg",
                        )
                        mp3_path = f"gs://{executor.storage_manager.storage.bucket_name}/{storage_path}"
                        storage_results[f"remixed_song_{i}_mp3"] = mp3_path
                        logger.info(f"Stored mock remixed MP3 {i}: {mp3_path}")
                    except Exception as e:
                        logger.error(f"Failed to create mock remixed MP3 {i}: {e}")
                if wav_url and wav_url.startswith("mock://"):
                    try:
                        original_audio_data = await executor.storage_manager.download_as_bytes(
                            audio_gcs_path
                        )
                        filename = f"remixed_song_{i}.wav"
                        storage_path = executor.storage_manager.storage.get_music_path(
                            str(job.job_id), filename
                        )
                        await executor.storage_manager.upload_from_bytes(
                            original_audio_data,
                            storage_path,
                            content_type="audio/wav",
                        )
                        wav_path = f"gs://{executor.storage_manager.storage.bucket_name}/{storage_path}"
                        storage_results[f"remixed_song_{i}_wav"] = wav_path
                        logger.info(f"Stored mock remixed WAV {i}: {wav_path}")
                    except Exception as e:
                        logger.error(f"Failed to create mock remixed WAV {i}: {e}")

            result = {
                "status": "completed",
                "music_gpt_task_id": music_gpt_task_id,
                "storage": storage_results,
                "metadata": {
                    "prompt": prompt,
                    "lyrics": lyrics,
                    "source_audio": audio_gcs_path,
                    "variations_count": len(
                        [url for url in [song_1_url, song_2_url] if url]
                    ),
                    "implementation": "mock",
                    "note": "Mock implementation - actual remixing API not yet available",
                },
            }
            logger.info(f"🎵🎛️ Music remixing task completed successfully: {result}")
            return result
        except (HTTPClientTimeoutError, HTTPClientError, HTTPRateLimitError) as e:
            logger.error(f"HTTP client error during music remixing: {e}")
            if isinstance(e, HTTPRateLimitError):
                retry_after = getattr(e, "retry_after", 60)
                return {
                    "error": f"Rate limited: {str(e)}",
                    "retry_after": retry_after,
                    "status": "requeued",
                }
            raise Exception(f"Music remixing HTTP error: {str(e)}") from e
        except Exception as e:
            logger.error(f"Unexpected error during music remixing: {e}")
            raise
    except Exception as e:
        logger.error(f"Music remixing failed with unexpected error: {e}")
        return {"error": f"Music remixing failed: {str(e)}"}


async def execute_vocal_extraction(executor, job: "Job", parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract vocals from audio using MusicGPT API.

    Parameters expected:
    - audio_gcs_path: str (required) - GCS path to source audio file
    - preprocessing_options: list[str] (optional) - preprocessing options for extraction

    Returns:
    - Dict with success/error status and extracted audio assets
    """
    try:
        # Extract required parameters
        audio_gcs_path: Optional[str] = parameters.get("audio_gcs_path")
        preprocessing_options: Optional[list] = parameters.get("preprocessing_options")

        if not audio_gcs_path:
            return {"error": "Missing required parameter: audio_gcs_path"}

        logger.info(f"🎤 Starting vocal extraction for job {job.job_id}")
        logger.info(f"Source audio: {audio_gcs_path}")
        logger.info(f"Preprocessing options: {preprocessing_options}")

        # Download audio from GCS
        try:
            audio_data = await executor.storage_manager.download_as_bytes(audio_gcs_path)
            if not audio_data:
                return {"error": f"Failed to download audio from GCS path: {audio_gcs_path}"}
            logger.info(f"Downloaded audio data: {len(audio_data)} bytes")
        except Exception as e:
            logger.error(f"Error downloading audio from GCS: {e}")
            return {"error": f"Failed to download audio from GCS: {str(e)}"}

        # Convert to base64 for API
        import base64
        audio_base64 = base64.b64encode(audio_data).decode("utf-8")
        logger.info(f"Converted audio to base64: {len(audio_base64)} chars")

        # Connect to WebSocket endpoint
        ws_url = ws_url_for(executor.api_base_url, "/extract-vocals")
        completed_payload: Optional[Dict[str, Any]] = None

        try:
            ws_payload = {
                "audio_base64": audio_base64,
            }
            if preprocessing_options:
                ws_payload["preprocessing_options"] = preprocessing_options

            async def message_handler(ws):
                nonlocal completed_payload
                async for msg in ws:
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        data = json.loads(msg.data)
                        if data.get("error"):
                            raise RuntimeError(f"API WS error: {data.get('data') or data}")
                        status = data.get("status")
                        payload = data.get("data", {})
                        logger.info(f"WebSocket status: {status}")
                        if status == "completed":
                            completed_payload = payload
                            return payload
                    elif msg.type == aiohttp.WSMsgType.ERROR:
                        raise RuntimeError(f"WebSocket error: {ws.exception()}")
                return None

            await ws_task_runner(
                ws_url,
                "VOCAL_EXTRACTION_TIMEOUT",
                lambda: ws_payload,
                message_handler,
            )

            if completed_payload:
                logger.info(f"🎤 Vocal extraction completed: {completed_payload}")

                # Extract URLs from the completed payload
                # Based on the API implementation, we expect vocals and instrumental tracks
                song_url = completed_payload.get("conversion_path")
                song_wav_url = completed_payload.get("conversion_path_wav")

                # Parse JSON strings if needed (following existing pattern)
                if isinstance(song_url, str):
                    try:
                        song_url = json.loads(song_url)
                    except json.JSONDecodeError:
                        pass  # Keep as string if not JSON

                if isinstance(song_wav_url, str):
                    try:
                        song_wav_url = json.loads(song_wav_url)
                    except json.JSONDecodeError:
                        pass  # Keep as string if not JSON

                # Store extracted audio files
                saved_tracks = []

                # Handle vocals and instrumental tracks
                track_types = ["vocals", "instrumental"]
                for track_type in track_types:
                    # Get URLs for this track type
                    mp3_url = song_url.get(track_type) if isinstance(song_url, dict) else None
                    wav_url = song_wav_url.get(track_type) if isinstance(song_wav_url, dict) else None

                    if wav_url or mp3_url:
                        # Prefer WAV over MP3 based on user preferences
                        candidate_urls = [wav_url, mp3_url]

                        variant_info = {
                            "filename_base": f"extracted_{track_type}",
                            "prefer_exts": [".wav", ".mp3"],
                        }

                        try:
                            stored_tracks = await download_and_store_audio(
                                candidate_urls,
                                variant_info,
                                executor.storage_manager,
                                job.job_id,
                                "vocal_extraction",
                            )
                            saved_tracks.extend(stored_tracks)
                            logger.info(f"Stored {track_type} track: {stored_tracks}")
                        except Exception as e:
                            logger.error(f"Failed to store {track_type} track: {e}")

                # Build result payload
                result_payload: Dict[str, Any] = {
                    "success": True,
                    "task_type": "vocal_extraction",
                    "result": {
                        "message": "Vocal extraction completed successfully",
                        "source_audio": audio_gcs_path,
                        "preprocessing_options": preprocessing_options,
                        "job_id": str(job.job_id),
                        "api_result": completed_payload,
                        "storage": {
                            "tracks": saved_tracks,
                            "vocals_track": next((t for t in saved_tracks if "vocals" in t.get("filename", "")), None),
                            "instrumental_track": next((t for t in saved_tracks if "instrumental" in t.get("filename", "")), None),
                            "stored_at": datetime.now(timezone.utc).isoformat(),
                        },
                    },
                }

                await executor.storage_manager.store_job_result(
                    job_id=str(job.job_id),
                    result_data=result_payload,
                )
                return result_payload
            else:
                raise RuntimeError(
                    "Vocal extraction did not complete - no completion payload received from WebSocket"
                )

        except (HTTPClientTimeoutError, HTTPClientError, HTTPRateLimitError) as e:
            logger.error(f"HTTP client error during vocal extraction: {e}")
            if isinstance(e, HTTPRateLimitError):
                retry_after = getattr(e, "retry_after", 60)
                return {
                    "error": f"Rate limited: {str(e)}",
                    "retry_after": retry_after,
                    "status": "requeued",
                }
            raise Exception(f"Vocal extraction HTTP error: {str(e)}") from e
        except Exception as e:
            logger.error(f"Unexpected error during vocal extraction: {e}")
            raise
    except Exception as e:
        logger.error(f"Vocal extraction failed with unexpected error: {e}")
        return {"error": f"Vocal extraction failed: {str(e)}"}

