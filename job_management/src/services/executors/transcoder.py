"""
Transcoder task executor.

Implements a pipeline call to the external Transcoder service following the
existing executor patterns (media.py, elevenlabs.py):
- Uses robust_json_request for HTTP calls with long read timeout (>60s)
- Uses Google Cloud default credentials to obtain an access token for Authorization
- Returns requeue payloads on rate limiting; returns error on other HTTP/timeouts
- Stores full response under result.transcoder_response; optional storage copying stubbed

Start with slot_managed=False per request; we can add concurrency management later.
"""
from __future__ import annotations

import logging
import os
from datetime import datetime, timezone
from typing import Any, Dict, Optional

# Type hints only; avoid hard dependency at import time
try:
    from models import Job  # noqa: F401
except Exception:  # pragma: no cover - type hints only
    Job = object  # type: ignore

from utils.http_client import (
    robust_json_request,
    HTTPClientError,
    HTTPClientTimeoutError,
    HTTPRateLimitError,
)

# Google auth for Access Token (matching request.py example)
from google.auth import default as ga_default
from google.auth.transport.requests import Request

logger = logging.getLogger(__name__)


async def _get_bearer_headers() -> Dict[str, str]:
    """Fetch an OAuth access token using ADC and return Authorization headers.

    This mirrors the request.py example using cloud-platform scope.
    """
    creds, _ = ga_default(scopes=["https://www.googleapis.com/auth/cloud-platform"])
    # Refresh to ensure valid token
    request = Request()
    creds.refresh(request)
    return {"Authorization": f"Bearer {creds.token}"}


async def execute_transcoder_pipeline(executor: Any, job: Job, parameters: Dict[str, Any]) -> Dict[str, Any]:
    """Execute the Transcoder pipeline.

    parameters should already match the Transcoder API schema expected by
    POST {TRANSCODER_BASE_URL}/pipeline/process

    Returns a structured result consistent with other executors.
    """
    try:
        endpoint = os.getenv("TRANSCODER_BASE_URL")
        if not endpoint:
            return {"error": "TRANSCODER_BASE_URL is not set"}

        # Build payload from parameters (and optionally enrich from context)
        payload: Dict[str, Any] = dict(parameters or {})

        # Authentication header
        headers = await _get_bearer_headers()

        try:
            # Use read timeout profile (configured at 900s default); satisfies >60s requirement
            api_result = await robust_json_request(
                "POST",
                f"{endpoint}/pipeline/process",
                "read",
                json=payload,
                headers=headers,
            )
        except HTTPRateLimitError as e:
            # Propagate as requeue with provided retry_after when available
            return {
                "status": "requeued",
                "retry_after": getattr(e, "retry_after", 60) or 60,
                "error": f"Rate limited: {str(e)}",
            }
        except (HTTPClientTimeoutError, HTTPClientError) as e:
            # Let Cloud Tasks handle retry by returning error
            return {"error": f"Transcoder request failed: {str(e)}"}

        # Optional: inspect api_result for output artifacts and copy to our bucket
        # storage_info = await _copy_outputs_if_any(executor, job, api_result)
        storage_info = None

        result: Dict[str, Any] = {
            "success": True,
            "task_type": "transcoder_pipeline",
            "result": {
                "transcoder_response": api_result,
                **({"storage": storage_info} if storage_info else {}),
                "called_at": datetime.now(timezone.utc).isoformat(),
            },
        }
        return result

    except Exception as e:  # pragma: no cover - defensive catch
        logger.exception("Transcoder pipeline execution failed")
        return {"error": f"Transcoder pipeline failed: {str(e)}"}

