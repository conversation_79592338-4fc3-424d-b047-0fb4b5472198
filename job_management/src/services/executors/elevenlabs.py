"""
ElevenLabs-related task executors extracted to keep modular organization.

Implements:
- voice generation (design/create a voice prototype via API)
- voice over generation (TTS audio) with slot management wrapper

Design goals:
- Follow existing patterns used by content/media/musicgpt executors
- Use robust_json_request for HTTP calls to the API service
- Store results/audio via StorageManager with WAV preference
- Return shapes consistent with other executors
"""
from __future__ import annotations

import base64
import logging
from datetime import datetime, timezone
from typing import Any, Dict, Optional

# Type hints for clarity; no runtime coupling required
try:
    from models import Job  # noqa: F401
except Exception:  # pragma: no cover - type hints only
    Job = object  # type: ignore

from utils.http_client import (
    robust_json_request,
    HTTPClientError,
    HTTPClientTimeoutError,
    HTTPRateLimitError,
)
from services.task_utils import (
    with_elevenlabs_slot,
    log_task_event,
    ws_task_runner,
    ws_url_for,
)

logger = logging.getLogger(__name__)

import io
import wave
import gzip
import json
import aiohttp


def _is_wav(data: bytes) -> bool:
    """Heuristic check for WAV header."""
    try:
        return len(data) >= 12 and data[:4] == b"RIFF" and b"WAVE" in data[8:16]
    except Exception:
        return False


def _is_mp3(data: bytes) -> bool:
    """Heuristic check for MP3 (ID3 tag or MPEG frame sync)."""
    try:
        return (len(data) >= 3 and data[:3] == b"ID3") or (
            len(data) >= 2 and data[0] == 0xFF and (data[1] & 0xE0) == 0xE0
        )
    except Exception:
        return False


def _pcm16_mono_44100_to_wav(pcm_bytes: bytes) -> bytes:
    """Wrap raw PCM 16-bit mono 44.1kHz bytes in a WAV container."""
    buf = io.BytesIO()
    with wave.open(buf, 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)
        wf.setframerate(44100)
        wf.writeframes(pcm_bytes)
    return buf.getvalue()



async def execute_voice_generation(executor, job: "Job", parameters: Dict[str, Any]) -> Dict[str, Any]:
    """Create/design a voice using ElevenLabs via API /generate-voice.

    Parameters expected in `parameters`:
    - voice_description: str (required)
    - voice_name: Optional[str]
    """
    try:
        voice_description: str = parameters.get("voice_description", "").strip()
        voice_name: Optional[str] = parameters.get("voice_name")
        if not voice_description:
            return {"error": "voice_description is required for voice_generation"}

        payload: Dict[str, Any] = {"voice_description": voice_description}
        if voice_name:
            payload["voice_name"] = voice_name

        try:
            api_result = await robust_json_request(
                "POST",
                f"{executor.api_base_url}/generate-voice",
                "read",
                json=payload,
            )
        except (HTTPClientTimeoutError, HTTPClientError, HTTPRateLimitError) as e:
            return {"error": f"API request failed: {str(e)}"}

        result: Dict[str, Any] = {
            "success": True,
            "task_type": "voice_generation",
            "result": {
                **api_result,
                "stored_at": datetime.now(timezone.utc).isoformat(),
            },
        }

        # Store JSON result under job results prefix
        await executor.storage_manager.store_job_result(
            job_id=str(job.job_id),
            result_data=result,
        )
        return result

    except Exception as e:  # pragma: no cover - defensive catch
        return {"error": f"Voice generation failed: {str(e)}"}


async def execute_voice_generation_with_slots(
    executor,
    job: "Job",
    parameters: Dict[str, Any],
    task_execution_id: str,
) -> Dict[str, Any]:
    """Slot-managed wrapper around execute_voice_generation.

    Ensures ElevenLabs concurrency cap across tasks.
    """

    log_task_event(
        logger,
        "info",
        "🗣️ execute_voice_generation_with_slots called",
        job_id=str(job.job_id),
        task_type="voice_generation",
        task_execution_id=task_execution_id,
    )

    def requeue_payload() -> Dict[str, Any]:
        return {
            "status": "requeued",
            "message": "No ElevenLabs slots available, job will be retried",
            "retry_after": 30,
        }

    async def run_core() -> Dict[str, Any]:
        return await execute_voice_generation(executor, job, parameters)

    return await with_elevenlabs_slot(executor.db, task_execution_id, run_core, requeue_payload)


async def execute_voice_over_generation_with_slots(
    executor,
    job: "Job",
    parameters: Dict[str, Any],
    task_execution_id: str,
) -> Dict[str, Any]:
    """Generate spoken-word voice over audio using ElevenLabs with slot control.

    Parameters expected in `parameters`:
    - script: str (required)
    - voice_id: str (required)
    - language_code: str = "en"
    - filename_base: Optional[str] (defaults to "voice_over")
    """

    log_task_event(
        logger,
        "info",
        "🗣️ execute_voice_over_generation_with_slots called",
        job_id=str(job.job_id),
        task_type="voice_over_generation",
        task_execution_id=task_execution_id,
    )

    def requeue_payload() -> Dict[str, Any]:
        return {
            "status": "requeued",
            "message": "No ElevenLabs slots available, job will be retried",
            "retry_after": 30,
        }

    async def run_core() -> Dict[str, Any]:
        try:
            script: str = (parameters.get("script") or "").strip()
            voice_id: str = (parameters.get("voice_id") or "").strip()
            language_code: str = (parameters.get("language_code") or "en").strip() or "en"
            filename_base: str = (parameters.get("filename_base") or "voice_over").strip() or "voice_over"

            if not script:
                return {"error": "script is required for voice_over_generation"}
            if not voice_id:
                return {"error": "voice_id is required for voice_over_generation"}

            compress_audio: bool = bool(parameters.get("compress_audio", True))
            ws_payload = {
                "script": script,
                "voice_id": voice_id,
                "language_code": language_code,
                "compress_audio": compress_audio,
            }

            # Build WS URL from API base
            ws_url = ws_url_for(executor.api_base_url, "/generate-voice-over")

            # Accumulate streamed PCM chunks
            audio_chunks: list[bytes] = []

            async def message_handler(ws):
                nonlocal audio_chunks
                async for msg in ws:
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        data = json.loads(msg.data)
                        if data.get("error"):
                            raise RuntimeError(f"API WS error: {data.get('data') or data}")
                        status = data.get("status")
                        payload = data.get("data", {})
                        logger.info(f"WebSocket status: {status}")

                        if status == "streaming_audio":
                            try:
                                chunk_b64 = payload.get("audio_chunk")
                                if not chunk_b64:
                                    continue
                                chunk = base64.b64decode(chunk_b64)
                                if payload.get("compressed", False):
                                    chunk = gzip.decompress(chunk)
                                audio_chunks.append(chunk)
                            except Exception as e:
                                raise RuntimeError(f"Failed to process audio chunk: {e}")
                        elif status == "completed":
                            return payload
                    elif msg.type == aiohttp.WSMsgType.ERROR:
                        raise RuntimeError(f"WebSocket error: {ws.exception()}")
                return None

            # Run WS task (uses VOICE_OVER_TIMEOUT env var; defaults to 1800s)
            await ws_task_runner(
                ws_url,
                "VOICE_OVER_TIMEOUT",
                lambda: ws_payload,
                message_handler,
            )

            if not audio_chunks:
                return {"error": "No audio chunks received from voice over WS"}

            payload_bytes = b"".join(audio_chunks)

            # Convert raw PCM to WAV if not already a WAV container
            if _is_wav(payload_bytes):
                wav_bytes = payload_bytes
            else:
                wav_bytes = _pcm16_mono_44100_to_wav(payload_bytes)

            storage_path = executor.storage_manager.storage.get_music_path(str(job.job_id), f"{filename_base}.wav")
            uploaded = await executor.storage_manager.upload_from_bytes(
                wav_bytes,
                storage_path,
                content_type="audio/wav",
            )
            if not uploaded:
                return {"error": "Failed to upload generated audio to storage"}

            signed_url = await executor.storage_manager.get_signed_url(storage_path, expiration_minutes=60)
            storage_info = {
                "primary_voice": {
                    "filename": f"{filename_base}.wav",
                    "gcs_path": f"gs://{executor.storage_manager.storage.bucket_name}/{storage_path}",
                    "signed_url": signed_url,
                }
            }

            result = {
                "success": True,
                "task_type": "voice_over_generation",
                "result": {
                    "storage": storage_info,
                    "stored_at": datetime.now(timezone.utc).isoformat(),
                },
            }
            return result

        except Exception as e:  # pragma: no cover - defensive catch
            return {"error": f"Voice over generation failed: {str(e)}"}

    # Acquire a slot, run, and release
    wrapped = await with_elevenlabs_slot(executor.db, task_execution_id, run_core, requeue_payload)
    return wrapped


# Audio Translation
async def execute_audio_translation_with_slots(executor, job: "Job", parameters: Dict[str, Any], task_execution_id: str) -> Dict[str, Any]:
    """Execute audio translation task with ElevenLabs slot management."""
    from services.slot_management import with_elevenlabs_slot

    requeue_payload = {
        "job_id": str(job.job_id),
        "queue": "elevenlabs_queue",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "action": "process_job"
    }

    async def run_core():
        try:
            audio_base64 = parameters.get("audio_base64", "")
            target_language = parameters.get("target_language", "")
            source_language = parameters.get("source_language", "English")
            filename_base = parameters.get("filename_base", "translated_audio")

            if not audio_base64:
                return {"error": "Audio data is required for audio translation"}
            if not target_language:
                return {"error": "Target language is required for audio translation"}

            payload = {
                "audio_base64": audio_base64,
                "target_language": target_language,
            }

            try:
                api_result = await robust_json_request(
                    "POST",
                    f"{executor.api_base_url}/translate-wav-audio",
                    "read",
                    json=payload,
                )
            except HTTPRateLimitError as e:
                # Propagate as requeue
                return {
                    "error": f"Rate limited: {str(e)}",
                    "status": "requeued",
                    "retry_after": getattr(e, "retry_after", 60) or 60,
                }
            except (HTTPClientTimeoutError, HTTPClientError) as e:
                # Non-rate limit HTTP errors -> let Cloud Tasks retry by returning error
                return {"error": f"API request failed: {str(e)}"}

            # Expect {"error": False, "audio_base64": "..."}
            audio_b64: Optional[str] = api_result.get("audio_base64") if isinstance(api_result, dict) else None
            if not audio_b64:
                return {"error": "No translated audio returned from API"}

            # Decode and ensure WAV; convert raw PCM 16-bit mono 44.1kHz if needed
            try:
                decoded = base64.b64decode(audio_b64)
            except Exception as e:
                return {"error": f"Failed to decode translated audio: {str(e)}"}

            lang_suffix = target_language.lower().replace(' ', '_')
            payload_bytes = decoded if _is_wav(decoded) else _pcm16_mono_44100_to_wav(decoded)

            # Store translated audio in GCS with descriptive naming (retain existing key pattern)
            asset_key = f"audio_translation_{lang_suffix}_wav"
            storage_info = await executor.storage_manager.store_job_asset(
                job_id=str(job.job_id),
                asset_key=asset_key,
                asset_data=payload_bytes,
                content_type="audio/wav",
                filename=f"{filename_base}_{lang_suffix}.wav"
            )

            if not storage_info:
                return {"error": "Failed to store translated audio"}

            result = {
                "success": True,
                "task_type": "audio_translation",
                "result": {
                    "storage": storage_info,
                    "source_language": source_language,
                    "target_language": target_language,
                    "stored_at": datetime.now(timezone.utc).isoformat(),
                },
            }
            return result

        except Exception as e:  # pragma: no cover - defensive catch
            return {"error": f"Audio translation failed: {str(e)}"}

    # Acquire a slot, run, and release
    wrapped = await with_elevenlabs_slot(executor.db, task_execution_id, run_core, requeue_payload)
    return wrapped

