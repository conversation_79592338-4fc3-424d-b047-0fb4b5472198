"""Task executors organized by domain (musicgpt, content, media).

This subpackage is internal; import executors via services.task_executor to
preserve public API stability. Direct imports are supported but not required.
"""

from .musicgpt import (
    execute_music_generation,
    execute_music_generation_with_slots,
    execute_sound_generation,
    execute_sound_generation_with_slots,
    execute_music_extension,
    execute_music_extension_with_slots,
    execute_music_remixing,
    execute_music_remixing_with_slots,
    execute_vocal_extraction,
    execute_vocal_extraction_with_slots,
)
from .content import (
    execute_prompt_enhancement,
    execute_text_translation,
    execute_spoken_word_script,
    execute_artist_generation,
)
from .media import (
    execute_album_cover_generation,
)
from .elevenlabs import (
    execute_voice_generation,
    execute_voice_over_generation_with_slots,
    execute_audio_translation_with_slots,
)



__all__ = [
    # musicgpt
    "execute_music_generation",
    "execute_music_generation_with_slots",
    "execute_sound_generation",
    "execute_sound_generation_with_slots",
    "execute_music_extension",
    "execute_music_extension_with_slots",
    "execute_music_remixing",
    "execute_music_remixing_with_slots",
    "execute_vocal_extraction",
    "execute_vocal_extraction_with_slots",
    # content
    "execute_prompt_enhancement",
    "execute_text_translation",
    "execute_spoken_word_script",
    "execute_artist_generation",
    # media
    "execute_album_cover_generation",
    # elevenlabs
    "execute_voice_generation",
    "execute_voice_over_generation_with_slots",
    "execute_audio_translation_with_slots",
]

