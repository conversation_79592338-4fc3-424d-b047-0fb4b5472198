"""
Content task executors extracted from TaskExecutor to improve modularity.

These helpers keep the same behavior and return shapes as the original
TaskExecutor methods. They are designed to be called like:

    await execute_prompt_enhancement(executor, job, parameters)

where `executor` is an instance of TaskExecutor providing:
- api_base_url
- storage_manager
- db (if needed in the future)
"""
from __future__ import annotations

import logging
from datetime import datetime, timezone
from typing import Any, Dict, Optional

# Type hints for clarity; no runtime coupling required
try:
    from models import Job  # noqa: F401
except Exception:  # pragma: no cover - type hints only
    Job = object  # type: ignore

from utils.http_client import (
    robust_json_request,
    HTTPClientError,
    HTTPClientTimeoutError,
    HTTPRateLimitError,
)

logger = logging.getLogger(__name__)


# Prompt Enhancement
async def execute_prompt_enhancement(executor, job: "Job", parameters: Dict[str, Any]) -> Dict[str, Any]:
    try:
        prompt = parameters.get("prompt", "")
        if not prompt:
            return {"error": "Prompt is required for prompt enhancement"}

        payload = {
            "prompt": prompt,
            "model_creativity": parameters.get("creativity", 0.5),
        }
        try:
            result = await robust_json_request(
                "POST",
                f"{executor.api_base_url}/auto-prompt",
                "read",
                json=payload,
            )
        except (HTTPClientTimeoutError, HTTPClientError, HTTPRateLimitError) as e:
            # Preserve previous behavior: return error (do not raise) so auto-retry can handle
            return {"error": f"API request failed: {str(e)}"}

        # Add original prompt for reference
        result["original_prompt"] = prompt

        enhanced_result = {
            "success": True,
            "task_type": "prompt_enhancement",
            "result": {
                **result,
                "stored_at": datetime.now(timezone.utc).isoformat(),
            },
        }

        await executor.storage_manager.store_job_result(
            job_id=str(job.job_id),
            result_data=enhanced_result,
        )

        return enhanced_result

    except Exception as e:  # pragma: no cover - defensive catch
        return {"error": f"Prompt enhancement failed: {str(e)}"}


# Text Translation
async def execute_text_translation(executor, job: "Job", parameters: Dict[str, Any]) -> Dict[str, Any]:
    try:
        text = parameters.get("text", "")
        target_language = parameters.get("target_language", "")

        if not text:
            return {"error": "Text is required for text translation"}
        if not target_language:
            return {"error": "Target language is required for text translation"}

        payload = {
            "text": text,
            "target_language": target_language,
            "source_language": parameters.get("source_language"),
            "context": parameters.get("context"),
            "quality": parameters.get("quality", "standard"),
            "model_creativity": parameters.get("model_creativity", 0.2),
        }
        try:
            result = await robust_json_request(
                "POST",
                f"{executor.api_base_url}/translate-text",
                "read",
                json=payload,
            )
        except (HTTPClientTimeoutError, HTTPClientError, HTTPRateLimitError) as e:
            # Preserve previous behavior: return error (do not raise) so auto-retry can handle
            return {"error": f"API request failed: {str(e)}"}

        result["original_text"] = text

        translation_result = {
            "success": True,
            "task_type": "text_translation",
            "result": {
                **result,
                "stored_at": datetime.now(timezone.utc).isoformat(),
            },
        }

        await executor.storage_manager.store_job_result(
            job_id=str(job.job_id),
            result_data=translation_result,
        )

        return translation_result

    except Exception as e:  # pragma: no cover - defensive catch
        return {"error": f"Text translation failed: {str(e)}"}


# Spoken Word Script
async def execute_spoken_word_script(executor, job: "Job", parameters: Dict[str, Any]) -> Dict[str, Any]:
    try:
        prompt = parameters.get("prompt", "")
        creativity = parameters.get("creativity", 0.2)
        agent_description = parameters.get("agent_description")

        if not prompt:
            return {"error": "Prompt is required for spoken word script generation"}

        logger.info(
            f"Generating spoken word script for job {job.job_id} with prompt: {prompt[:100]}..."
        )

        try:
            payload = {
                "prompt": prompt,
                "model_creativity": creativity,
            }
            if agent_description:
                payload["agent_description"] = agent_description

            result = await robust_json_request(
                "POST",
                f"{executor.api_base_url}/spoken-word-script",
                "read",
                json=payload,
            )

            script_path = await executor.storage_manager.store_spoken_word_script(
                job_id=str(job.job_id),
                script_data=result,
                filename="spoken_word_script.json",
            )

            return {
                "success": True,
                "spoken_word_script": result,
                "script_path": script_path,
                "task_type": "spoken_word_script",
            }

        except (HTTPClientTimeoutError, HTTPClientError, HTTPRateLimitError) as e:
            logger.error(f"HTTP client error during spoken word script generation: {e}")
            if isinstance(e, HTTPRateLimitError):
                retry_after = getattr(e, "retry_after", 60)
                return {
                    "error": f"Rate limited: {str(e)}",
                    "retry_after": retry_after,
                    "status": "requeued",
                }
            # Raise to trigger Cloud Tasks retry as before
            raise Exception(f"Spoken word script generation HTTP error: {str(e)}") from e

        except Exception as e:
            logger.error(f"Unexpected error during spoken word script generation: {e}")
            raise

    except Exception as e:  # pragma: no cover - defensive catch
        logger.error(f"Spoken word script generation failed: {e}")
        return {"error": f"Spoken word script generation failed: {str(e)}"}


# Artist Generation
async def execute_artist_generation(executor, job: "Job", parameters: Dict[str, Any]) -> Dict[str, Any]:
    try:
        prompt = parameters.get("prompt", "")
        creativity = parameters.get("creativity", 0.2)
        agent_description = parameters.get("agent_description")

        if not prompt:
            return {"error": "Prompt is required for artist generation"}

        logger.info(
            f"Executing artist generation for job {job.job_id} with prompt: {prompt}"
        )

        try:
            payload = {
                "prompt": prompt,
                "model_creativity": creativity,
            }
            if agent_description:
                payload["agent_description"] = agent_description

            result = await robust_json_request(
                "POST",
                f"{executor.api_base_url}/artist-generation",
                "read",
                json=payload,
            )

            artist_profile_path = await executor.storage_manager.store_artist_profile(
                job_id=str(job.job_id),
                artist_data=result,
                filename="artist_profile.json",
            )

            return {
                "success": True,
                "artist_profile": result,
                "artist_profile_path": artist_profile_path,
                "task_type": "artist_generation",
            }

        except HTTPRateLimitError as e:
            logger.warning(f"Rate limited during artist generation: {e}")
            return {
                "error": "Rate limited",
                "status": "requeued",
                "retry_after": e.retry_after or 60,
            }
        except (HTTPClientError, HTTPClientTimeoutError) as e:
            logger.error(f"HTTP error during artist generation: {e}")
            return {"error": f"API request failed: {str(e)}"}

    except Exception as e:  # pragma: no cover - defensive catch
        logger.error(f"Artist generation failed: {e}")
        return {"error": f"Artist generation failed: {str(e)}"}

