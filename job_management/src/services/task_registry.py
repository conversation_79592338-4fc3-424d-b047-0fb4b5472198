from dataclasses import dataclass
from typing import Dict, Optional

from schemas import TaskType


@dataclass(frozen=True)
class TaskSpec:
    # Identifier/name
    name: str
    enum_value: TaskType

    # Behavior flags
    needs_context_validation: bool
    slot_managed: bool
    store_context: bool

    # Optional/validation behavior
    optional_non_fatal_context: bool = False  # e.g., album cover

    # Requeue/handling behavior
    uses_handle_requeue: bool = False  # If True, use handle_task_requeue flow
    raise_on_non_resource_error: bool = False  # If True, raise to trigger Cloud Tasks retry

    # Retry timings
    pure_requeue_retry_after: Optional[int] = None  # Used when result has status=requeued without error
    error_requeue_default_retry_after: int = 60

    # TaskExecutor method name to call (bound on the instance via getattr)
    execute_method: str = ""


# Registry keyed by lowercased task type value
TASK_REGISTRY: Dict[str, TaskSpec] = {
    TaskType.PROMPT_ENHANCEMENT.value: TaskSpec(
        name="prompt_enhancement",
        enum_value=TaskType.PROMPT_ENHANCEMENT,
        needs_context_validation=False,
        slot_managed=False,
        store_context=True,
        uses_handle_requeue=False,
        raise_on_non_resource_error=False,
        execute_method="_execute_prompt_enhancement",
    ),
    TaskType.TEXT_TRANSLATION.value: TaskSpec(
        name="text_translation",
        enum_value=TaskType.TEXT_TRANSLATION,
        needs_context_validation=True,
        slot_managed=False,
        store_context=True,
        uses_handle_requeue=False,
        raise_on_non_resource_error=False,
        execute_method="_execute_text_translation",
    ),
    TaskType.MUSIC_GENERATION.value: TaskSpec(
        name="music_generation",
        enum_value=TaskType.MUSIC_GENERATION,
        needs_context_validation=True,
        slot_managed=True,
        store_context=False,
        uses_handle_requeue=True,
        raise_on_non_resource_error=True,
        pure_requeue_retry_after=30,
        error_requeue_default_retry_after=60,
        execute_method="_execute_music_generation_with_slots",
    ),
    TaskType.SOUND_GENERATION.value: TaskSpec(
        name="sound_generation",
        enum_value=TaskType.SOUND_GENERATION,
        needs_context_validation=True,
        slot_managed=True,
        store_context=True,
        uses_handle_requeue=True,
        raise_on_non_resource_error=False,
        # requeue payload from wrapper includes error; pure_requeue not used here
        error_requeue_default_retry_after=60,
        execute_method="_execute_sound_generation_with_slots",
    ),
    TaskType.MUSIC_EXTENSION.value: TaskSpec(
        name="music_extension",
        enum_value=TaskType.MUSIC_EXTENSION,
        needs_context_validation=True,
        slot_managed=True,
        store_context=True,
        uses_handle_requeue=True,
        raise_on_non_resource_error=False,
        pure_requeue_retry_after=30,
        error_requeue_default_retry_after=60,
        execute_method="_execute_music_extension_with_slots",
    ),
    TaskType.MUSIC_REMIXING.value: TaskSpec(
        name="music_remixing",
        enum_value=TaskType.MUSIC_REMIXING,
        needs_context_validation=True,
        slot_managed=True,
        store_context=True,
        uses_handle_requeue=True,
        raise_on_non_resource_error=False,
        # wrapper returns pure requeue; treat same as others with default 30
        pure_requeue_retry_after=30,
        error_requeue_default_retry_after=60,
        execute_method="_execute_music_remixing_with_slots",
    ),
    TaskType.VOCAL_EXTRACTION.value: TaskSpec(
        name="vocal_extraction",
        enum_value=TaskType.VOCAL_EXTRACTION,
        needs_context_validation=True,
        slot_managed=True,
        store_context=True,
        uses_handle_requeue=True,
        raise_on_non_resource_error=False,
        pure_requeue_retry_after=30,
        error_requeue_default_retry_after=60,
        execute_method="_execute_vocal_extraction_with_slots",
    ),
    TaskType.ALBUM_COVER_GENERATION.value: TaskSpec(
        name="album_cover_generation",
        enum_value=TaskType.ALBUM_COVER_GENERATION,
        needs_context_validation=True,
        slot_managed=False,
        store_context=True,
        optional_non_fatal_context=True,
        uses_handle_requeue=True,
        raise_on_non_resource_error=True,
        error_requeue_default_retry_after=60,
        execute_method="_execute_album_cover_generation",
    ),
    TaskType.ARTIST_GENERATION.value: TaskSpec(
        name="artist_generation",
        enum_value=TaskType.ARTIST_GENERATION,
        needs_context_validation=False,
        slot_managed=False,
        store_context=True,
        uses_handle_requeue=False,
        raise_on_non_resource_error=False,
        execute_method="_execute_artist_generation",
    ),
    TaskType.SPOKEN_WORD_SCRIPT.value: TaskSpec(
        name="spoken_word_script",
        enum_value=TaskType.SPOKEN_WORD_SCRIPT,
        needs_context_validation=False,
        slot_managed=False,
        store_context=True,
        uses_handle_requeue=False,
        raise_on_non_resource_error=False,
        execute_method="_execute_spoken_word_script",
    ),

    # Utility tasks
    TaskType.EXPORT.value: TaskSpec(
        name="export",
        enum_value=TaskType.EXPORT,
        needs_context_validation=False,
        slot_managed=False,
        store_context=True,
        uses_handle_requeue=False,
        raise_on_non_resource_error=False,
        error_requeue_default_retry_after=30,
        execute_method="_execute_export",
    ),

    TaskType.VOICE_GENERATION.value: TaskSpec(
        name="voice_generation",
        enum_value=TaskType.VOICE_GENERATION,
        needs_context_validation=False,
        slot_managed=True,
        store_context=True,
        uses_handle_requeue=True,
        raise_on_non_resource_error=False,
        pure_requeue_retry_after=30,
        error_requeue_default_retry_after=60,
        execute_method="_execute_voice_generation_with_slots",
    ),
    TaskType.VOICE_OVER_GENERATION.value: TaskSpec(
        name="voice_over_generation",
        enum_value=TaskType.VOICE_OVER_GENERATION,
        needs_context_validation=True,
        slot_managed=True,
        store_context=True,
        uses_handle_requeue=True,
        raise_on_non_resource_error=False,
        pure_requeue_retry_after=30,
        error_requeue_default_retry_after=60,
        execute_method="_execute_voice_over_generation_with_slots",
    ),
    TaskType.AUDIO_TRANSLATION.value: TaskSpec(
        name="audio_translation",
        enum_value=TaskType.AUDIO_TRANSLATION,
        needs_context_validation=False,
        slot_managed=True,
        store_context=True,
        uses_handle_requeue=True,
        raise_on_non_resource_error=False,
        pure_requeue_retry_after=30,
        error_requeue_default_retry_after=60,
        execute_method="_execute_audio_translation_with_slots",
    ),

    # Transcoder tasks
    TaskType.TRANSCODER_PIPELINE.value: TaskSpec(
        name="transcoder_pipeline",
        enum_value=TaskType.TRANSCODER_PIPELINE,
        needs_context_validation=True,
        slot_managed=False,
        store_context=True,
        uses_handle_requeue=False,
        raise_on_non_resource_error=False,
        error_requeue_default_retry_after=60,
        execute_method="_execute_transcoder_pipeline",
    ),
}

