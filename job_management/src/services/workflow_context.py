"""
Workflow Context Management Service

Provides centralized context management for workflow jobs, ensuring reliable
data passing between sequential tasks with proper validation and versioning.
"""

import json
import logging
from typing import Dict, Any, Optional, List, Union
from uuid import UUID
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from enum import Enum

from sqlalchemy.orm import Session
from models import WorkflowTaskExecution, Job
from schemas import TaskType, JobStatus

logger = logging.getLogger(__name__)


class ContextDataType(Enum):
    """Types of context data that can be stored."""
    ENHANCED_PROMPT = "enhanced_prompt"
    ENHANCED_GENRE = "enhanced_genre"
    LYRICS = "lyrics"
    MUSIC_METADATA = "music_metadata"
    GENERATED_TRACKS = "generated_tracks"
    SOUND_METADATA = "sound_metadata"
    GENERATED_SOUNDS = "generated_sounds"
    AUDIO_FILE = "audio_file"  # GCS path to audio file for extension/remixing
    ALBUM_COVER_URL = "album_cover_url"
    ARTIST_PROFILE = "artist_profile"
    SPOKEN_WORD_SCRIPT = "spoken_word_script"
    TRANSLATED_TEXT = "translated_text"
    SOURCE_LANGUAGE = "source_language"
    TARGET_LANGUAGE = "target_language"
    CUSTOM = "custom"


@dataclass
class ContextEntry:
    """Represents a single context data entry with metadata."""
    key: str
    value: Any
    data_type: ContextDataType
    source_task_type: str
    source_task_index: int
    created_at: datetime
    version: int = 1
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "key": self.key,
            "value": self.value,
            "data_type": self.data_type.value,
            "source_task_type": self.source_task_type,
            "source_task_index": self.source_task_index,
            "created_at": self.created_at.isoformat(),
            "version": self.version
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ContextEntry':
        """Create from dictionary."""
        return cls(
            key=data["key"],
            value=data["value"],
            data_type=ContextDataType(data["data_type"]),
            source_task_type=data["source_task_type"],
            source_task_index=data["source_task_index"],
            created_at=datetime.fromisoformat(data["created_at"]),
            version=data.get("version", 1)
        )


class ContextSchema:
    """Defines the expected context schema for different task types."""
    
    # Define what context each task type produces
    TASK_OUTPUTS = {
        TaskType.PROMPT_ENHANCEMENT.value: [
            (ContextDataType.ENHANCED_PROMPT, "main_prompt", str, True),
            (ContextDataType.ENHANCED_GENRE, "music_genre", str, False),
            (ContextDataType.LYRICS, "lyrics", str, False),
        ],
        TaskType.MUSIC_GENERATION.value: [
            (ContextDataType.MUSIC_METADATA, "metadata", dict, True),
            (ContextDataType.GENERATED_TRACKS, "tracks", list, True),
            (ContextDataType.AUDIO_FILE, "storage", dict, True),  # For music remixing chaining
        ],
        TaskType.SOUND_GENERATION.value: [
            (ContextDataType.SOUND_METADATA, "storage", dict, True),
            (ContextDataType.GENERATED_SOUNDS, "storage", dict, True),
            (ContextDataType.AUDIO_FILE, "storage", dict, True),  # For music extension chaining
        ],
        TaskType.MUSIC_REMIXING.value: [
            (ContextDataType.SOUND_METADATA, "storage", dict, True),
            (ContextDataType.GENERATED_SOUNDS, "storage", dict, True),
            (ContextDataType.AUDIO_FILE, "storage", dict, True),  # For further chaining
        ],
        TaskType.VOCAL_EXTRACTION.value: [
            (ContextDataType.SOUND_METADATA, "storage", dict, True),
            (ContextDataType.GENERATED_SOUNDS, "storage", dict, True),
            (ContextDataType.AUDIO_FILE, "storage", dict, True),  # For further chaining
        ],
        TaskType.ALBUM_COVER_GENERATION.value: [
            (ContextDataType.ALBUM_COVER_URL, "cover_url", str, True),
        ],
        TaskType.TEXT_TRANSLATION.value: [
            (ContextDataType.TRANSLATED_TEXT, "translation", str, True),
            (ContextDataType.SOURCE_LANGUAGE, "source_language", str, False),
            (ContextDataType.TARGET_LANGUAGE, "target_language", str, False),
        ],
        TaskType.ARTIST_GENERATION.value: [
            (ContextDataType.ARTIST_PROFILE, "artist_profile", dict, True),
        ],
        TaskType.SPOKEN_WORD_SCRIPT.value: [
            (ContextDataType.SPOKEN_WORD_SCRIPT, "spoken_word_script", dict, True),
        ]
    }
    
    # Define what context each task type requires
    TASK_INPUTS = {
        TaskType.MUSIC_GENERATION.value: [
            (ContextDataType.ENHANCED_PROMPT, "prompt", str, False),
            (ContextDataType.ENHANCED_GENRE, "genre", str, False),  # TaskExecutor maps genre -> music_style
            (ContextDataType.LYRICS, "lyrics", str, False),
        ],
        TaskType.SOUND_GENERATION.value: [
            (ContextDataType.ENHANCED_PROMPT, "prompt", str, False),
        ],
        TaskType.MUSIC_EXTENSION.value: [
            (ContextDataType.ENHANCED_PROMPT, "prompt", str, False),
            (ContextDataType.AUDIO_FILE, "audio_gcs_path", str, False),  # GCS path to source audio
        ],
        TaskType.MUSIC_REMIXING.value: [
            (ContextDataType.ENHANCED_PROMPT, "prompt", str, False),
            (ContextDataType.AUDIO_FILE, "audio_gcs_path", str, False),  # GCS path to source audio
            (ContextDataType.LYRICS, "lyrics", str, False),  # Optional lyrics for remixing
        ],
        TaskType.VOCAL_EXTRACTION.value: [
            (ContextDataType.AUDIO_FILE, "audio_gcs_path", str, True),  # GCS path to source audio (required)
        ],
        TaskType.ALBUM_COVER_GENERATION.value: [
            (ContextDataType.ENHANCED_PROMPT, "prompt", str, False),
            (ContextDataType.MUSIC_METADATA, "music_metadata", dict, False),
        ]
    }


class WorkflowContextManager:
    """Centralized workflow context management service."""
    
    def __init__(self, db: Session):
        self.db = db
        self.schema = ContextSchema()
    
    async def get_workflow_context(self, job_id: UUID) -> Dict[str, Any]:
        """
        Retrieve complete workflow context for a job.
        
        Returns a dictionary with all available context data from completed tasks.
        """
        try:
            # Get all completed task executions for this job
            completed_tasks = self.db.query(WorkflowTaskExecution).filter(
                WorkflowTaskExecution.job_id == job_id,
                WorkflowTaskExecution.status == JobStatus.COMPLETED.value
            ).order_by(WorkflowTaskExecution.task_index).all()
            
            context = {}
            context_entries = []
            
            for task_execution in completed_tasks:
                if not task_execution.result:
                    continue
                
                # Extract context from task result based on schema
                task_context = self._extract_context_from_task(task_execution)
                context.update(task_context)
                
                # Store context entries with metadata
                for key, value in task_context.items():
                    data_type = self._infer_data_type(key, task_execution.task_type)
                    entry = ContextEntry(
                        key=key,
                        value=value,
                        data_type=data_type,
                        source_task_type=task_execution.task_type,
                        source_task_index=task_execution.task_index,
                        created_at=task_execution.completed_at or datetime.now(timezone.utc)
                    )
                    context_entries.append(entry)
            
            # Add metadata about context reconstruction
            context["_context_metadata"] = {
                "reconstructed_at": datetime.now(timezone.utc).isoformat(),
                "source_tasks": len(completed_tasks),
                "context_entries": len(context_entries),
                "entries": [entry.to_dict() for entry in context_entries]
            }
            
            logger.info(f"Reconstructed context for job {job_id}: {len(context_entries)} entries from {len(completed_tasks)} tasks")
            return context
            
        except Exception as e:
            logger.error(f"Failed to reconstruct context for job {job_id}: {e}")
            # Return empty context with error metadata
            return {
                "_context_metadata": {
                    "error": str(e),
                    "reconstructed_at": datetime.now(timezone.utc).isoformat(),
                    "source_tasks": 0,
                    "context_entries": 0
                }
            }
    
    def _extract_context_from_task(self, task_execution: WorkflowTaskExecution) -> Dict[str, Any]:
        """Extract context data from a completed task execution."""
        context = {}
        result = task_execution.result

        if not result or not isinstance(result, dict):
            return context

        # Get the actual result data (handle nested structure)
        result_data = result.get("result", result)
        if not isinstance(result_data, dict):
            return context

        # Special handling for sound generation to extract audio file path
        if task_execution.task_type == TaskType.SOUND_GENERATION.value:
            # Extract the primary sound GCS path for music extension chaining
            storage = result_data.get("storage", {})
            primary_sound = storage.get("primary_sound", {})
            gcs_path = primary_sound.get("gcs_path")

            if gcs_path:
                context[ContextDataType.AUDIO_FILE.value] = gcs_path
                logger.info(f"Extracted audio file path for music extension: {gcs_path}")

            # Also store the full storage info for other uses
            if storage:
                context[ContextDataType.SOUND_METADATA.value] = storage
                context[ContextDataType.GENERATED_SOUNDS.value] = storage.get("sounds", [])

        # Special handling for music generation to extract audio file path
        if task_execution.task_type == TaskType.MUSIC_GENERATION.value:
            # Extract the primary track GCS path for music remixing chaining
            storage = result_data.get("storage", {})
            primary_track = storage.get("primary_track", {})
            gcs_path = primary_track.get("gcs_path")

            if gcs_path:
                context[ContextDataType.AUDIO_FILE.value] = gcs_path
                logger.info(f"Extracted audio file path for music remixing: {gcs_path}")

            # Also store the full storage info for other uses
            if storage:
                context[ContextDataType.MUSIC_METADATA.value] = storage
                context[ContextDataType.GENERATED_TRACKS.value] = storage.get("tracks", [])

        # Extract context based on task type schema for other task types
        task_outputs = self.schema.TASK_OUTPUTS.get(task_execution.task_type, [])

        for data_type, result_key, expected_type, required in task_outputs:
            # Skip if we already handled this in special cases above
            if (task_execution.task_type == TaskType.SOUND_GENERATION.value and
                data_type in [ContextDataType.AUDIO_FILE, ContextDataType.SOUND_METADATA, ContextDataType.GENERATED_SOUNDS]):
                continue
            if (task_execution.task_type == TaskType.MUSIC_GENERATION.value and
                data_type in [ContextDataType.AUDIO_FILE, ContextDataType.MUSIC_METADATA, ContextDataType.GENERATED_TRACKS]):
                continue

            if result_key in result_data:
                value = result_data[result_key]
                # Validate type if specified
                if expected_type and not isinstance(value, expected_type):
                    logger.warning(f"Context type mismatch for {result_key}: expected {expected_type}, got {type(value)}")
                    continue

                # Map to context key
                context_key = data_type.value
                context[context_key] = value

        return context
    
    def _infer_data_type(self, key: str, task_type: str) -> ContextDataType:
        """Infer the data type from context key and source task type."""
        # Try to match with known data types
        for data_type in ContextDataType:
            if data_type.value == key:
                return data_type
        
        # Default to custom for unknown keys
        return ContextDataType.CUSTOM
    
    async def validate_task_context(self, job_id: UUID, task_type: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate that required context is available for a task and enrich parameters.
        
        Returns enriched parameters with context data injected.
        """
        context = await self.get_workflow_context(job_id)
        enriched_params = dict(parameters)  # Copy parameters
        
        # Get required inputs for this task type
        required_inputs = self.schema.TASK_INPUTS.get(task_type, [])
        missing_required = []

        for data_type, param_key, expected_type, required in required_inputs:
            context_key = data_type.value

            # If parameter not provided, try to get from context
            if not enriched_params.get(param_key) and context_key in context:
                enriched_params[param_key] = context[context_key]
                logger.debug(f"Enriched parameter {param_key} from context for task {task_type}")

            # Check if required parameter is still missing
            if required and not enriched_params.get(param_key):
                missing_required.append(param_key)

        # Special-case: Enrichment for spoken word workflows
        try:
            # Voice-over: default 'script' from spoken_word_script or translated_text
            if task_type == TaskType.VOICE_OVER_GENERATION.value:
                if not enriched_params.get("script"):
                    # Prefer translated_text if present (Pattern B), else fall back to spoken_word_script.script (Pattern A)
                    translated_text = context.get("translated_text")
                    if isinstance(translated_text, str) and translated_text.strip():
                        enriched_params["script"] = translated_text.strip()
                        logger.debug("Enriched 'script' from translated_text context for voice_over_generation")
                    else:
                        sw = context.get(ContextDataType.SPOKEN_WORD_SCRIPT.value)
                        if isinstance(sw, dict):
                            script_text = sw.get("script")
                            if isinstance(script_text, str) and script_text.strip():
                                enriched_params["script"] = script_text.strip()
                                logger.debug("Enriched 'script' from spoken_word_script context for voice_over_generation")
                # require voice id, which does not need lanaguage code
                # Also default language_code from prior translation target_language if not provided
                # if not enriched_params.get("language_code"):
                #     target_lang = context.get("target_language")
                #     if isinstance(target_lang, str) and target_lang.strip():
                #         enriched_params["language_code"] = target_lang.strip()
                #         logger.debug("Enriched 'language_code' from text_translation target_language context for voice_over_generation")

            # Text translation: default 'text' from spoken_word_script.script
            if task_type == TaskType.TEXT_TRANSLATION.value and not enriched_params.get("text"):
                sw = context.get(ContextDataType.SPOKEN_WORD_SCRIPT.value)
                if isinstance(sw, dict):
                    script_text = sw.get("script")
                    if isinstance(script_text, str) and script_text.strip():
                        enriched_params["text"] = script_text.strip()
                        logger.debug("Enriched 'text' from spoken_word_script context for text_translation")
        except Exception as e:
            logger.warning(f"Failed to enrich spoken-word context for task {task_type}: {e}")

        if missing_required:
            raise ValueError(f"Missing required context for task {task_type}: {missing_required}")

        return enriched_params
    
    async def store_task_context(self, job_id: UUID, task_index: int, task_type: str, result: Dict[str, Any]) -> None:
        """
        Store context data from a completed task.
        
        This is called after task completion to ensure context is properly stored.
        """
        try:
            # Context is already stored in WorkflowTaskExecution.result
            # This method can be used for additional context processing if needed
            logger.debug(f"Context stored for job {job_id}, task {task_index} ({task_type})")
            
        except Exception as e:
            logger.error(f"Failed to store context for job {job_id}, task {task_index}: {e}")
            # Don't raise - this shouldn't fail the task

    async def get_context_summary(self, job_id: UUID) -> Dict[str, Any]:
        """Get a summary of available context for debugging and monitoring."""
        try:
            context = await self.get_workflow_context(job_id)
            metadata = context.get("_context_metadata", {})

            return {
                "job_id": str(job_id),
                "total_entries": metadata.get("context_entries", 0),
                "source_tasks": metadata.get("source_tasks", 0),
                "available_data_types": list(set(
                    entry.get("data_type") for entry in metadata.get("entries", [])
                )),
                "last_updated": metadata.get("reconstructed_at"),
                "has_error": "error" in metadata
            }
        except Exception as e:
            return {
                "job_id": str(job_id),
                "error": str(e),
                "total_entries": 0,
                "source_tasks": 0
            }

    def get_required_context_for_task(self, task_type: str) -> List[str]:
        """Get list of required context keys for a task type."""
        required_inputs = self.schema.TASK_INPUTS.get(task_type, [])
        return [
            data_type.value for data_type, param_key, expected_type, required in required_inputs
            if required
        ]

    def get_optional_context_for_task(self, task_type: str) -> List[str]:
        """Get list of optional context keys for a task type."""
        optional_inputs = self.schema.TASK_INPUTS.get(task_type, [])
        return [
            data_type.value for data_type, param_key, expected_type, required in optional_inputs
            if not required
        ]
