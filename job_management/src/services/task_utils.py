import logging
import os
import json
from typing import Any, Callable, Dict, Optional, Tuple, List
from urllib.parse import urlparse
from sqlalchemy.orm import Session

from services.queue_manager import MusicGPTQueueManager, ElevenLabsQueueManager
from services.job_manager import JobManager


async def with_musicgpt_slot(
    db: Session,
    task_execution_id: str,
    coro_factory: Callable[[], Any],
    requeue_payload_builder: Optional[Callable[[], Dict[str, Any]]] = None,
) -> Dict[str, Any]:
    """
    Acquire a MusicGPT slot for the given workflow task_execution_id, execute the provided
    coroutine factory, and ensure slot release. If no slot is available, return a
    standard requeue payload provided by requeue_payload_builder (or a safe default).

    This preserves the existing slot semantics used across music/sound tasks.
    """
    queue_manager = MusicGPTQueueManager(db)

    # Attempt to acquire slot; if unavailable, return requeue response immediately
    acquired = await queue_manager.acquire_musicgpt_slot(task_execution_id)
    if not acquired:
        if requeue_payload_builder:
            return requeue_payload_builder()
        # Safe default if none supplied (callers can provide custom one to preserve shape)
        return {"status": "requeued", "retry_after": 30}

    try:
        result = await coro_factory()
        if result is None:
            # Maintain defensive behavior seen in existing code
            return {"error": "Operation returned no result"}
        return result
    finally:
        await queue_manager.release_musicgpt_slot(task_execution_id)


async def with_elevenlabs_slot(
    db: Session,
    task_execution_id: str,
    coro_factory: Callable[[], Any],
    requeue_payload_builder: Optional[Callable[[], Dict[str, Any]]] = None,
) -> Dict[str, Any]:
    """
    Acquire an ElevenLabs slot for the given workflow task_execution_id, execute the provided
    coroutine factory, and ensure slot release. If no slot is available, return a
    standard requeue payload provided by requeue_payload_builder (or a safe default).

    Mirrors with_musicgpt_slot semantics with a separate slot pool (max 10 concurrent).
    """
    queue_manager = ElevenLabsQueueManager(db)

    acquired = await queue_manager.acquire_slot(task_execution_id)
    if not acquired:
        if requeue_payload_builder:
            return requeue_payload_builder()
        return {"status": "requeued", "retry_after": 30}

    try:
        result = await coro_factory()
        if result is None:
            return {"error": "Operation returned no result"}
        return result
    finally:
        await queue_manager.release_slot(task_execution_id)


def should_requeue_for_resource(result_or_message: Any) -> bool:
    """
    Centralizes resource-constraint detection logic. Accepts either a result dict or a string message.
    Returns True if the condition suggests we should requeue the job.
    """
    text = ""
    if isinstance(result_or_message, dict):
        if result_or_message.get("status") == "requeued":
            return True
        text = (result_or_message.get("error") or result_or_message.get("message") or "")
    else:
        text = str(result_or_message or "")

    low = text.lower()
    if "no slots available" in low or "slot availability" in low:
        return True
    if "rate limited" in low or "quota" in low:
        return True
    return False


async def handle_task_requeue(
    db: Session,
    workflow_tracker,
    job,
    task_idx: int,
    result: Optional[Dict[str, Any]] = None,
    message: Optional[str] = None,
    default_retry_after: int = 60,
) -> Dict[str, Any]:
    """
    Applies repeated requeue flow:
    - workflow_tracker.requeue_task(...)
    - JobManager(db).requeue_job_with_delay(...)

    Returns a requeue-shaped dict. If `result` already contains retry_after, use it.
    If a custom `message` is provided, include it; otherwise preserve the incoming result
    if it already looks like a requeue payload.
    """
    retry_after = default_retry_after
    if isinstance(result, dict):
        retry_after = int(result.get("retry_after", default_retry_after))

    await workflow_tracker.requeue_task(job.job_id, task_idx, retry_after=retry_after)

    job_manager = JobManager(db)
    await job_manager.requeue_job_with_delay(job.job_id, delay_seconds=retry_after)

    if isinstance(result, dict) and result.get("status") == "requeued" and not message:
        # Preserve original if already a well-formed requeue
        return result

    payload: Dict[str, Any] = {"status": "requeued", "retry_after": retry_after}
    if message:
        payload["message"] = message
    return payload


async def complete_and_store_result(
    storage_manager,
    workflow_tracker,
    context_manager,
    job,
    task_idx: int,
    task_type: str,
    result: Dict[str, Any],
    store_context: bool = True,
    include_task_meta: bool = True,
) -> None:
    """
    Stores the result via storage_manager, marks the task as completed via workflow_tracker,
    and optionally stores context via context_manager.
    """
    # Store job result (include task metadata only when provided/desired)
    if include_task_meta and task_idx is not None and task_type is not None:
        await storage_manager.store_job_result(
            job_id=str(job.job_id),
            result_data=result,
            task_index=task_idx,
            task_type=task_type,
        )
    else:
        await storage_manager.store_job_result(job_id=str(job.job_id), result_data=result)

    # Mark task as completed
    await workflow_tracker.complete_task(job.job_id, task_idx, success=True, result=result)

    # Store context if desired
    if store_context:
        await context_manager.store_task_context(job.job_id, task_idx, task_type, result)


def get_task_execution_id(db: Session, job_id, task_idx: int) -> Tuple[Optional[str], Optional[str]]:
    """
    Fetches WorkflowTaskExecution and returns (id, error_msg). error_msg is None when id is found.
    """
    from models import WorkflowTaskExecution  # local import to avoid circulars

    task_execution = (
        db.query(WorkflowTaskExecution)
        .filter(
            WorkflowTaskExecution.job_id == job_id,
            WorkflowTaskExecution.task_index == task_idx,
        )
        .first()
    )

    if not task_execution:
        return None, f"Workflow task execution not found for job {job_id}, task {task_idx}"

    return str(task_execution.id), None


def log_task_event(
    logger: logging.Logger,
    level: str,
    message: str,
    *,
    job_id: Optional[str] = None,
    task_idx: Optional[int] = None,
    task_type: Optional[str] = None,
    task_execution_id: Optional[str] = None,
    extra: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Unified logging helper for consistent structured logging.
    level: one of "debug", "info", "warning", "error".
    """
    extras: Dict[str, Any] = {}
    if job_id is not None:
        extras["job_id"] = str(job_id)
    if task_idx is not None:
        extras["task_idx"] = task_idx
    if task_type is not None:
        extras["task_type"] = task_type
    if task_execution_id is not None:
        extras["task_execution_id"] = task_execution_id
    if extra:
        extras.update(extra)

    log_fn = getattr(logger, level, logger.info)
    log_fn(message, extra=extras)

# === WebSocket URL and timeout helpers ===

def ws_url_for(api_base_url: str, endpoint_path: str) -> str:
    """Convert API base URL (http/https) to ws/wss and append endpoint path."""
    base = api_base_url.replace("http", "ws", 1)
    if not endpoint_path.startswith("/"):
        endpoint_path = "/" + endpoint_path
    return f"{base}{endpoint_path}"


def get_timeout_seconds(timeout_env_var: str, default_seconds: int = 1800) -> int:
    """Read timeout seconds from env var, defaulting to provided value."""
    try:
        return int(os.getenv(timeout_env_var, str(default_seconds)))
    except Exception:
        return default_seconds


# === Unified WebSocket task runner ===
async def ws_task_runner(
    ws_url: str,
    timeout_env_var: str,
    build_payload_fn: Callable[[], Dict[str, Any]],
    handle_messages_fn: Callable[[Any], Any],
) -> Any:
    """
    Unified WebSocket execution flow. Builds payload, connects via RobustWebSocketClient,
    streams messages with handle_messages_fn, and returns its result.

    - Propagates HTTPClientTimeoutError/HTTPClientError and any exceptions raised by handler
    - Timeout is read from the provided environment variable to preserve behavior
    """
    from utils.http_client import RobustWebSocketClient  # local import to avoid circulars

    timeout_seconds = get_timeout_seconds(timeout_env_var, 1800)
    client = RobustWebSocketClient(ws_url, timeout_seconds)

    payload = build_payload_fn() if callable(build_payload_fn) else {}
    return await client.connect_and_process(payload, handle_messages_fn)


# === Shared audio download + store helper ===
async def download_and_store_audio(
    candidate_urls: List[Optional[str]],
    variant_info: Dict[str, Any],
    storage_manager,
    job_id: str,
    task_type: str,
) -> List[Dict[str, Any]]:
    """
    Download audio from candidate_urls (in priority order), validate extension, store to GCS,
    and return artifact dicts with signed URLs. Preserves content types and naming patterns.

    variant_info for single artifact should include:
      - filename_base: base filename without extension (e.g., "generated_sound")
      - prefer_exts: ordered list of preferred extensions (e.g., [".wav", ".mp3"]). Defaults to [".wav", ".mp3"].

    Returns list of saved artifact dicts each with: source_url, filename, gcs_path, signed_url.
    Raises if any network/storage error occurs; caller decides error shape.
    """
    from utils.http_client import robust_download

    filename_base = variant_info.get("filename_base", "audio")
    prefer_exts: List[str] = variant_info.get("prefer_exts", [".wav", ".mp3"])  # WAV over MP3 preference

    # Pick the first non-empty URL from candidates, preferring order.
    chosen_url: Optional[str] = None
    for u in candidate_urls:
        if u:
            chosen_url = u
            break

    if not chosen_url:
        raise Exception("No audio URL found in completion payload")

    # Determine extension from URL; fallback to first preferred ext
    path = urlparse(chosen_url).path or ""
    ext = os.path.splitext(path)[1].lower()
    if not ext or ext not in prefer_exts:
        # keep the original ext if present but not in prefer list; otherwise default to first preference
        ext = ext or (prefer_exts[0] if prefer_exts else ".wav")

    filename = f"{filename_base}{ext}"
    storage_path = storage_manager.storage.get_music_path(str(job_id), filename)

    # Download audio
    audio_bytes = await robust_download(chosen_url)

    # Upload to storage
    content_type = "audio/wav" if ext == ".wav" else "audio/mpeg"
    await storage_manager.upload_from_bytes(
        audio_bytes,
        storage_path,
        content_type=content_type,
    )

    signed_url = await storage_manager.get_signed_url(storage_path, expiration_minutes=60)
    artifact = {
        "source_url": chosen_url,
        "filename": filename,
        "gcs_path": f"gs://{storage_manager.storage.bucket_name}/{storage_path}",
        "signed_url": signed_url,
    }
    return [artifact]
