"""
Optimized Job Manager with performance improvements for high-throughput music generation.
"""

from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy import and_, or_, text
from typing import Dict, Any, Optional, List
from uuid import UUID
from datetime import datetime, timezone
import asyncio
import logging
from concurrent.futures import ThreadPoolExecutor

from models import Job, WorkflowTaskExecution
from schemas import JobSubmissionRequest, JobResponse, JobType, JobStatus, TaskType
from .job_manager import JobManager
from .storage_manager import StorageManager

logger = logging.getLogger(__name__)

class OptimizedJobManager(JobManager):
    """Enhanced JobManager with performance optimizations."""
    
    def __init__(self, db: Session):
        super().__init__(db)
        self._asset_cache = {}  # Simple in-memory cache
        self._queue_cache = {}  # Cache queue routing decisions
        self._executor = ThreadPoolExecutor(max_workers=10)  # For parallel operations
    
    # ============================================================================
    # OPTIMIZED DATABASE QUERIES
    # ============================================================================
    
    async def get_job_status_batch(self, job_ids: List[UUID]) -> List[Optional[JobResponse]]:
        """Get status for multiple jobs in a single query."""
        if not job_ids:
            return []
        
        jobs = self.db.query(Job).filter(Job.job_id.in_(job_ids)).all()
        job_dict = {job.job_id: job for job in jobs}
        
        return [
            JobResponse(
                job_id=job.job_id,
                job_type=JobType(job.job_type),
                status=JobStatus(job.status),
                created_at=job.created_at,
                current_task_type=job.current_task_type,
                current_task_index=job.current_task_index,
                workflow_progress_percentage=job.workflow_progress_percentage
            ) if job else None
            for job_id in job_ids
            for job in [job_dict.get(job_id)]
        ]
    
    async def get_job_with_executions(self, job_id: UUID) -> Optional[tuple]:
        """Get job and all workflow executions in a single optimized query."""
        # Use eager loading to fetch job and executions in one query
        job = (
            self.db.query(Job)
            .options(selectinload(Job.workflow_executions))  # Assuming relationship exists
            .filter(Job.job_id == job_id)
            .first()
        )
        
        if not job:
            return None
            
        # Alternative: Manual join query for better control
        result = self.db.execute(
            text("""
                SELECT 
                    j.job_id, j.job_type, j.status, j.configuration,
                    j.created_at, j.updated_at, j.current_task_type,
                    j.current_task_index, j.workflow_progress_percentage,
                    wte.task_type, wte.task_index, wte.status as task_status,
                    wte.result, wte.started_at, wte.completed_at
                FROM jobs j
                LEFT JOIN workflow_task_execution wte ON j.job_id = wte.job_id
                WHERE j.job_id = :job_id
                ORDER BY wte.task_index
            """),
            {"job_id": str(job_id)}
        )
        
        rows = result.fetchall()
        if not rows:
            return None
            
        # Parse results
        job_data = rows[0]
        executions = []
        
        for row in rows:
            if row.task_type:  # Has execution data
                executions.append({
                    'task_type': row.task_type,
                    'task_index': row.task_index,
                    'status': row.task_status,
                    'result': row.result,
                    'started_at': row.started_at,
                    'completed_at': row.completed_at
                })
        
        return job_data, executions
    
    # ============================================================================
    # OPTIMIZED ASSET RETRIEVAL WITH CACHING
    # ============================================================================
    
    async def get_job_asset_urls_optimized(self, job_id: str) -> Dict[str, str]:
        """Optimized asset URL retrieval with caching and parallel operations."""
        
        # Check cache first
        cache_key = f"assets:{job_id}"
        if cache_key in self._asset_cache:
            cached_result, timestamp = self._asset_cache[cache_key]
            # Cache for 5 minutes
            if (datetime.utcnow() - timestamp).seconds < 300:
                return cached_result
        
        # Parallel execution of database and storage operations
        async def get_job_data():
            return await self.get_job_with_executions(UUID(job_id))
        
        async def get_storage_metadata():
            return await self.storage_manager.get_job_result(job_id)
        
        # Execute in parallel
        job_data_task = asyncio.create_task(get_job_data())
        storage_task = asyncio.create_task(get_storage_metadata())
        
        job_data, storage_metadata = await asyncio.gather(
            job_data_task, storage_task, return_exceptions=True
        )
        
        # Handle exceptions
        if isinstance(job_data, Exception):
            logger.error(f"Error getting job data for {job_id}: {job_data}")
            job_data = None
        if isinstance(storage_metadata, Exception):
            logger.error(f"Error getting storage metadata for {job_id}: {storage_metadata}")
            storage_metadata = None
        
        # Build assets dictionary
        assets = {
            "metadata": f"gs://{self.storage_config.bucket_name}/{self.storage_config.get_results_path(job_id)}"
        }
        
        if job_data:
            job_info, executions = job_data
            assets.update(await self._build_assets_from_executions(executions))
        
        # Cache result
        self._asset_cache[cache_key] = (assets, datetime.utcnow())
        
        return assets
    
    async def _build_assets_from_executions(self, executions: List[Dict]) -> Dict[str, str]:
        """Build asset URLs from execution results in parallel."""
        assets = {}
        
        # Group executions by type for batch processing
        execution_groups = {}
        for execution in executions:
            if execution['status'] == 'completed' and execution['result']:
                task_type = execution['task_type']
                if task_type not in execution_groups:
                    execution_groups[task_type] = []
                execution_groups[task_type].append(execution)
        
        # Process each group in parallel
        tasks = []
        for task_type, group_executions in execution_groups.items():
            task = asyncio.create_task(
                self._process_execution_group(task_type, group_executions)
            )
            tasks.append(task)
        
        # Gather results
        group_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Merge results
        for result in group_results:
            if isinstance(result, dict):
                assets.update(result)
            elif isinstance(result, Exception):
                logger.error(f"Error processing execution group: {result}")
        
        return assets
    
    async def _process_execution_group(self, task_type: str, executions: List[Dict]) -> Dict[str, str]:
        """Process a group of executions of the same type."""
        assets = {}
        
        for execution in executions:
            task_result = execution['result']
            task_index = execution['task_index']
            
            if task_type == "music_generation":
                storage_info = task_result.get("result", {}).get("storage", {})
                audio = self._extract_audio_with_preference(storage_info, "primary_track", "tracks")
                self._add_audio_assets(assets, "music_generation", audio)
                
            elif task_type == "sound_generation":
                storage_info = task_result.get("result", {}).get("storage", {})
                audio = self._extract_audio_with_preference(storage_info, "primary_sound", "sounds")
                self._add_audio_assets(assets, "sound_generation", audio)
                
            elif task_type == "album_cover_generation":
                storage_info = task_result.get("result", {}).get("storage", {})
                self._add_image_assets(assets, "album_cover", storage_info)
        
        return assets
    
    def _add_audio_assets(self, assets: Dict[str, str], prefix: str, audio: Dict[str, str]):
        """Helper to add audio assets with consistent naming."""
        preferred = audio.get("wav") or audio.get("mp3")
        if preferred:
            assets[f"{prefix}_audio"] = preferred
        if "wav" in audio:
            assets[f"{prefix}_audio_wav"] = audio["wav"]
        if "mp3" in audio:
            assets[f"{prefix}_audio_mp3"] = audio["mp3"]
    
    def _add_image_assets(self, assets: Dict[str, str], prefix: str, storage_info: Dict[str, Any]):
        """Helper to add image assets with consistent naming."""
        gcs_path = storage_info.get("gcs_path")
        if isinstance(gcs_path, str):
            lp = gcs_path.lower()
            if lp.endswith((".jpg", ".jpeg")):
                assets[f"{prefix}_image_jpg"] = gcs_path
                assets[f"{prefix}_image"] = gcs_path
            elif lp.endswith(".png"):
                assets[f"{prefix}_image_png"] = gcs_path
                if f"{prefix}_image" not in assets:
                    assets[f"{prefix}_image"] = gcs_path
    
    # ============================================================================
    # OPTIMIZED QUEUE ROUTING WITH CACHING
    # ============================================================================
    
    def get_queue_for_job_cached(self, job: Job) -> str:
        """Get queue with caching based on task composition hash."""
        try:
            config = job.configuration or {}
            tasks = config.get("tasks", [])
            
            # Create cache key from task types
            task_types = sorted([task.get("task_type", "").lower() for task in tasks])
            cache_key = "|".join(task_types)
            
            if cache_key in self._queue_cache:
                return self._queue_cache[cache_key]
            
            # Calculate queue (same logic as original)
            queue_name = self._calculate_queue_for_tasks(task_types)
            
            # Cache result
            self._queue_cache[cache_key] = queue_name
            
            return queue_name
            
        except Exception as e:
            logger.error(f"Error determining queue for job {job.job_id}: {e}")
            return self.queues.job_queue
    
    def _calculate_queue_for_tasks(self, task_types: List[str]) -> str:
        """Calculate appropriate queue based on task types."""
        slow_task_types = {
            TaskType.MUSIC_GENERATION.value,
            TaskType.SOUND_GENERATION.value,
            TaskType.MUSIC_EXTENSION.value,
            TaskType.MUSIC_REMIXING.value,
        }
        
        elevenlabs_task_types = {
            TaskType.VOICE_OVER_GENERATION.value,
            TaskType.VOICE_GENERATION.value,
            TaskType.AUDIO_TRANSLATION.value,
        }
        
        has_musicgpt_tasks = any(task_type in slow_task_types for task_type in task_types)
        has_elevenlabs_tasks = any(task_type in elevenlabs_task_types for task_type in task_types)
        
        if has_elevenlabs_tasks and not has_musicgpt_tasks and self.queues.elevenlabs_queue:
            return self.queues.elevenlabs_queue
        elif has_musicgpt_tasks:
            return self.queues.musicgpt_queue
        else:
            return self.queues.task_queue
    
    # ============================================================================
    # OPTIMIZED JOB SUBMISSION
    # ============================================================================
    
    async def submit_job_optimized(self, request: JobSubmissionRequest, client_ip: str, user_agent: str) -> JobResponse:
        """Optimized job submission with pre-calculated queue routing."""
        
        # Pre-calculate queue to avoid redundant database query
        temp_job = Job(
            job_type=request.job_type.value,
            configuration=request.model_dump()
        )
        queue_name = self.get_queue_for_job_cached(temp_job)
        
        # Create and save job
        job = Job(
            job_type=request.job_type.value,
            configuration=request.model_dump(),
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        self.db.add(job)
        self.db.commit()
        self.db.refresh(job)
        
        # Queue job directly without re-querying
        task_data = {
            "job_id": str(job.job_id),
            "queue": queue_name,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "action": "process_job"
        }
        
        await self.queue_manager.enqueue_task(queue_name, task_data)
        
        return JobResponse(
            job_id=job.job_id,
            job_type=JobType(job.job_type),
            status=JobStatus(job.status),
            created_at=job.created_at
        )
    
    # ============================================================================
    # CACHE MANAGEMENT
    # ============================================================================
    
    def clear_cache(self, job_id: Optional[str] = None):
        """Clear cache entries."""
        if job_id:
            cache_key = f"assets:{job_id}"
            self._asset_cache.pop(cache_key, None)
        else:
            self._asset_cache.clear()
            self._queue_cache.clear()
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics."""
        return {
            "asset_cache_size": len(self._asset_cache),
            "queue_cache_size": len(self._queue_cache)
        }
