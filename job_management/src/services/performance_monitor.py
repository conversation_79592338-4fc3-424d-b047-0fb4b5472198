"""
Performance monitoring and metrics collection for the job management system.
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import redis.asyncio as redis
import json
from sqlalchemy.orm import Session
from sqlalchemy import text

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """Performance metric data structure."""
    timestamp: datetime
    metric_name: str
    value: float
    tags: Dict[str, str]
    unit: str = "count"

@dataclass
class JobPerformanceStats:
    """Job performance statistics."""
    job_id: str
    queue_name: str
    job_type: str
    submission_time: datetime
    start_time: Optional[datetime] = None
    completion_time: Optional[datetime] = None
    queue_wait_time: Optional[float] = None  # seconds
    processing_time: Optional[float] = None  # seconds
    total_time: Optional[float] = None  # seconds
    task_count: int = 0
    failed_tasks: int = 0
    retries: int = 0
    status: str = "queued"

class PerformanceMonitor:
    """Comprehensive performance monitoring system."""
    
    def __init__(self, db: Session, redis_url: str = "redis://localhost:6379"):
        self.db = db
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        
        # Metric storage
        self.metrics_key = "performance:metrics"
        self.job_stats_key = "performance:jobs"
        self.system_stats_key = "performance:system"
        
        # In-memory buffers for high-frequency metrics
        self.metric_buffer = deque(maxlen=10000)
        self.job_stats_buffer = {}
        
        # Performance thresholds
        self.thresholds = {
            "queue_wait_time_warning": 30.0,  # seconds
            "queue_wait_time_critical": 120.0,
            "processing_time_warning": 300.0,  # 5 minutes
            "processing_time_critical": 1800.0,  # 30 minutes
            "database_query_warning": 1.0,
            "database_query_critical": 5.0,
            "slot_utilization_warning": 0.8,  # 80%
            "slot_utilization_critical": 0.95  # 95%
        }
        
        # Background tasks
        self._monitoring_task = None
        self._start_monitoring()
    
    def _start_monitoring(self):
        """Start background monitoring tasks."""
        if self._monitoring_task is None or self._monitoring_task.done():
            self._monitoring_task = asyncio.create_task(self._background_monitoring())
    
    async def _background_monitoring(self):
        """Background task for continuous monitoring."""
        while True:
            try:
                await asyncio.sleep(30)  # Monitor every 30 seconds
                await self._collect_system_metrics()
                await self._flush_metrics_buffer()
                await self._check_performance_alerts()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in background monitoring: {e}")
    
    # ============================================================================
    # JOB PERFORMANCE TRACKING
    # ============================================================================
    
    async def track_job_submission(self, job_id: str, queue_name: str, job_type: str):
        """Track job submission."""
        stats = JobPerformanceStats(
            job_id=job_id,
            queue_name=queue_name,
            job_type=job_type,
            submission_time=datetime.utcnow(),
            status="queued"
        )
        
        self.job_stats_buffer[job_id] = stats
        await self._store_job_stats(stats)
        
        # Record metric
        await self.record_metric("job_submitted", 1, {
            "queue": queue_name,
            "job_type": job_type
        })
    
    async def track_job_start(self, job_id: str):
        """Track job processing start."""
        if job_id in self.job_stats_buffer:
            stats = self.job_stats_buffer[job_id]
            stats.start_time = datetime.utcnow()
            stats.status = "running"
            
            # Calculate queue wait time
            if stats.submission_time:
                stats.queue_wait_time = (stats.start_time - stats.submission_time).total_seconds()
            
            await self._store_job_stats(stats)
            
            # Record metrics
            await self.record_metric("job_started", 1, {
                "queue": stats.queue_name,
                "job_type": stats.job_type
            })
            
            if stats.queue_wait_time:
                await self.record_metric("queue_wait_time", stats.queue_wait_time, {
                    "queue": stats.queue_name,
                    "job_type": stats.job_type
                }, unit="seconds")
    
    async def track_job_completion(self, job_id: str, success: bool = True, task_count: int = 0, 
                                  failed_tasks: int = 0, retries: int = 0):
        """Track job completion."""
        if job_id in self.job_stats_buffer:
            stats = self.job_stats_buffer[job_id]
            stats.completion_time = datetime.utcnow()
            stats.status = "completed" if success else "failed"
            stats.task_count = task_count
            stats.failed_tasks = failed_tasks
            stats.retries = retries
            
            # Calculate processing and total times
            if stats.start_time:
                stats.processing_time = (stats.completion_time - stats.start_time).total_seconds()
            if stats.submission_time:
                stats.total_time = (stats.completion_time - stats.submission_time).total_seconds()
            
            await self._store_job_stats(stats)
            
            # Record metrics
            await self.record_metric("job_completed", 1, {
                "queue": stats.queue_name,
                "job_type": stats.job_type,
                "status": stats.status
            })
            
            if stats.processing_time:
                await self.record_metric("processing_time", stats.processing_time, {
                    "queue": stats.queue_name,
                    "job_type": stats.job_type
                }, unit="seconds")
            
            if stats.total_time:
                await self.record_metric("total_job_time", stats.total_time, {
                    "queue": stats.queue_name,
                    "job_type": stats.job_type
                }, unit="seconds")
            
            # Clean up buffer
            del self.job_stats_buffer[job_id]
    
    # ============================================================================
    # METRIC RECORDING
    # ============================================================================
    
    async def record_metric(self, name: str, value: float, tags: Dict[str, str] = None, 
                           unit: str = "count"):
        """Record a performance metric."""
        metric = PerformanceMetric(
            timestamp=datetime.utcnow(),
            metric_name=name,
            value=value,
            tags=tags or {},
            unit=unit
        )
        
        # Add to buffer for batch processing
        self.metric_buffer.append(metric)
        
        # Also store immediately for critical metrics
        if name in ["job_failed", "database_error", "slot_acquisition_failed"]:
            await self._store_metric_immediately(metric)
    
    async def _store_metric_immediately(self, metric: PerformanceMetric):
        """Store metric immediately in Redis."""
        try:
            metric_data = asdict(metric)
            metric_data['timestamp'] = metric.timestamp.isoformat()
            
            await self.redis_client.lpush(
                f"{self.metrics_key}:{metric.metric_name}",
                json.dumps(metric_data, default=str)
            )
            
            # Keep only last 1000 entries per metric
            await self.redis_client.ltrim(f"{self.metrics_key}:{metric.metric_name}", 0, 999)
            
        except Exception as e:
            logger.error(f"Error storing metric {metric.metric_name}: {e}")
    
    async def _flush_metrics_buffer(self):
        """Flush metrics buffer to Redis."""
        if not self.metric_buffer:
            return
        
        try:
            # Group metrics by name for batch storage
            grouped_metrics = defaultdict(list)
            
            while self.metric_buffer:
                metric = self.metric_buffer.popleft()
                grouped_metrics[metric.metric_name].append(metric)
            
            # Store each group
            pipe = self.redis_client.pipeline()
            
            for metric_name, metrics in grouped_metrics.items():
                for metric in metrics:
                    metric_data = asdict(metric)
                    metric_data['timestamp'] = metric.timestamp.isoformat()
                    
                    pipe.lpush(
                        f"{self.metrics_key}:{metric_name}",
                        json.dumps(metric_data, default=str)
                    )
                
                # Trim to keep only recent entries
                pipe.ltrim(f"{self.metrics_key}:{metric_name}", 0, 999)
            
            await pipe.execute()
            
        except Exception as e:
            logger.error(f"Error flushing metrics buffer: {e}")
    
    # ============================================================================
    # SYSTEM METRICS COLLECTION
    # ============================================================================
    
    async def _collect_system_metrics(self):
        """Collect system-wide performance metrics."""
        try:
            # Database metrics
            await self._collect_database_metrics()
            
            # Queue metrics
            await self._collect_queue_metrics()
            
            # Slot utilization metrics
            await self._collect_slot_metrics()
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    async def _collect_database_metrics(self):
        """Collect database performance metrics."""
        try:
            # Connection pool stats
            engine = self.db.get_bind()
            pool = engine.pool
            
            await self.record_metric("db_pool_size", pool.size())
            await self.record_metric("db_pool_checked_out", pool.checkedout())
            await self.record_metric("db_pool_overflow", pool.overflow())
            
            # Query performance
            start_time = time.time()
            result = self.db.execute(text("SELECT COUNT(*) FROM jobs WHERE status = 'running'"))
            query_time = time.time() - start_time
            
            running_jobs = result.fetchone()[0]
            
            await self.record_metric("db_query_time", query_time, 
                                   {"query_type": "running_jobs_count"}, "seconds")
            await self.record_metric("running_jobs_count", running_jobs)
            
        except Exception as e:
            logger.error(f"Error collecting database metrics: {e}")
    
    async def _collect_queue_metrics(self):
        """Collect queue performance metrics."""
        try:
            # Get queue lengths from database
            queue_stats = self.db.execute(text("""
                SELECT 
                    COALESCE(configuration->>'queue', 'unknown') as queue_name,
                    status,
                    COUNT(*) as count
                FROM jobs 
                WHERE created_at > NOW() - INTERVAL '1 hour'
                GROUP BY queue_name, status
            """)).fetchall()
            
            for row in queue_stats:
                await self.record_metric("queue_jobs", row.count, {
                    "queue": row.queue_name,
                    "status": row.status
                })
                
        except Exception as e:
            logger.error(f"Error collecting queue metrics: {e}")
    
    async def _collect_slot_metrics(self):
        """Collect slot utilization metrics."""
        try:
            # MusicGPT slots
            musicgpt_active = self.db.execute(text("""
                SELECT COUNT(*) FROM active_music_generation WHERE is_active = true
            """)).fetchone()[0]
            
            await self.record_metric("musicgpt_slots_active", musicgpt_active)
            await self.record_metric("musicgpt_slots_utilization", 
                                   musicgpt_active / 10.0, unit="percentage")
            
            # ElevenLabs slots
            elevenlabs_active = self.db.execute(text("""
                SELECT COUNT(*) FROM active_elevenlabs WHERE is_active = true
            """)).fetchone()[0]
            
            await self.record_metric("elevenlabs_slots_active", elevenlabs_active)
            await self.record_metric("elevenlabs_slots_utilization", 
                                   elevenlabs_active / 10.0, unit="percentage")
            
        except Exception as e:
            logger.error(f"Error collecting slot metrics: {e}")
    
    # ============================================================================
    # PERFORMANCE ANALYSIS
    # ============================================================================
    
    async def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get performance summary for the specified time period."""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            # Job completion stats
            job_stats = self.db.execute(text("""
                SELECT 
                    job_type,
                    status,
                    COUNT(*) as count,
                    AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_total_time,
                    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (updated_at - created_at))) as p95_total_time
                FROM jobs 
                WHERE created_at > :cutoff_time
                GROUP BY job_type, status
            """), {"cutoff_time": cutoff_time}).fetchall()
            
            summary = {
                "time_period_hours": hours,
                "job_stats": [],
                "performance_alerts": await self._get_current_alerts(),
                "system_health": await self._get_system_health()
            }
            
            for row in job_stats:
                summary["job_stats"].append({
                    "job_type": row.job_type,
                    "status": row.status,
                    "count": row.count,
                    "avg_total_time_seconds": round(row.avg_total_time or 0, 2),
                    "p95_total_time_seconds": round(row.p95_total_time or 0, 2)
                })
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {"error": str(e)}
    
    async def _check_performance_alerts(self):
        """Check for performance issues and generate alerts."""
        alerts = []
        
        try:
            # Check queue wait times
            recent_jobs = self.db.execute(text("""
                SELECT job_id, created_at, updated_at, status
                FROM jobs 
                WHERE created_at > NOW() - INTERVAL '1 hour'
                AND status IN ('running', 'completed')
            """)).fetchall()
            
            for job in recent_jobs:
                if job.status == 'running':
                    wait_time = (datetime.utcnow() - job.created_at).total_seconds()
                    if wait_time > self.thresholds["queue_wait_time_critical"]:
                        alerts.append({
                            "type": "critical",
                            "message": f"Job {job.job_id} waiting {wait_time:.1f}s in queue",
                            "metric": "queue_wait_time",
                            "value": wait_time
                        })
            
            # Store alerts in Redis
            if alerts:
                await self.redis_client.setex(
                    "performance:alerts",
                    300,  # 5 minutes
                    json.dumps(alerts, default=str)
                )
            
        except Exception as e:
            logger.error(f"Error checking performance alerts: {e}")
    
    async def _get_current_alerts(self) -> List[Dict[str, Any]]:
        """Get current performance alerts."""
        try:
            alerts_data = await self.redis_client.get("performance:alerts")
            if alerts_data:
                return json.loads(alerts_data)
            return []
        except Exception as e:
            logger.error(f"Error getting current alerts: {e}")
            return []
    
    async def _get_system_health(self) -> Dict[str, str]:
        """Get overall system health status."""
        try:
            # Check database connectivity
            self.db.execute(text("SELECT 1"))
            db_health = "healthy"
        except:
            db_health = "unhealthy"
        
        try:
            # Check Redis connectivity
            await self.redis_client.ping()
            redis_health = "healthy"
        except:
            redis_health = "unhealthy"
        
        return {
            "database": db_health,
            "redis": redis_health,
            "overall": "healthy" if db_health == "healthy" and redis_health == "healthy" else "degraded"
        }
    
    async def _store_job_stats(self, stats: JobPerformanceStats):
        """Store job statistics in Redis."""
        try:
            stats_data = asdict(stats)
            # Convert datetime objects to ISO strings
            for key, value in stats_data.items():
                if isinstance(value, datetime):
                    stats_data[key] = value.isoformat()
            
            await self.redis_client.setex(
                f"{self.job_stats_key}:{stats.job_id}",
                3600,  # 1 hour
                json.dumps(stats_data)
            )
        except Exception as e:
            logger.error(f"Error storing job stats for {stats.job_id}: {e}")
    
    async def close(self):
        """Clean up resources."""
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        await self.redis_client.close()
