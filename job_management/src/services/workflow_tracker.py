"""
Workflow Tracking Service for managing task execution status.

This service provides methods to track the execution of workflow tasks,
enabling detailed progress monitoring and status reporting.
"""

from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from uuid import UUID
from datetime import datetime
import logging

from models import Job, WorkflowTaskExecution
from schemas import JobStatus

# Configure logger
logger = logging.getLogger(__name__)


class WorkflowTracker:
    """Service for tracking workflow execution progress at task level."""

    def __init__(self, db: Session):
        self.db = db

    async def initialize_workflow_tracking(self, job_id: UUID, tasks: List[Dict[str, Any]]) -> None:
        """Initialize tracking records for all tasks in a workflow."""
        try:
            # Check if workflow tracking already exists for this job
            existing_count = self.db.query(WorkflowTaskExecution).filter(
                WorkflowTaskExecution.job_id == job_id
            ).count()

            if existing_count > 0:
                # Workflow tracking already initialized, skip creation
                return

            # Create task execution records one by one to handle potential race conditions
            for task_idx, task in enumerate(tasks):
                # Double-check if this specific task already exists
                existing_task = self.db.query(WorkflowTaskExecution).filter(
                    WorkflowTaskExecution.job_id == job_id,
                    WorkflowTaskExecution.task_index == task_idx
                ).first()

                if existing_task:
                    # Task already exists, skip
                    continue

                task_type = task.get("task_type")
                parameters = task.get("parameters", {})

                task_execution = WorkflowTaskExecution(
                    job_id=job_id,
                    task_type=task_type,
                    task_index=task_idx,
                    status=JobStatus.QUEUED.value,
                    parameters=parameters
                )
                self.db.add(task_execution)

            self.db.commit()

        except Exception as e:
            self.db.rollback()
            # If we get a unique constraint violation, it means another process
            # already created the records, which is fine - we can continue
            if "duplicate key value violates unique constraint" in str(e):
                return
            else:
                # Re-raise other exceptions
                raise
    
    async def start_task(self, job_id: UUID, task_type: str, task_index: int) -> None:
        """Mark a task as started and update job current task."""
        # Update task status
        task_execution = self.db.query(WorkflowTaskExecution).filter(
            WorkflowTaskExecution.job_id == job_id,
            WorkflowTaskExecution.task_index == task_index
        ).first()

        if task_execution:
            task_execution.status = JobStatus.RUNNING.value
            task_execution.started_at = datetime.utcnow()
            task_execution.updated_at = datetime.utcnow()

        # Update job current task
        job = self.db.query(Job).filter(Job.job_id == job_id).first()
        if job:
            job.current_task_type = task_type
            job.current_task_index = task_index
            job.updated_at = datetime.utcnow()

        self.db.commit()
    
    async def complete_task(self, job_id: UUID, task_index: int,
                          success: bool = True, result: Dict[str, Any] = None,
                          error_message: str = None) -> None:
        """Mark a task as completed or failed."""
        # Update task status
        task_execution = self.db.query(WorkflowTaskExecution).filter(
            WorkflowTaskExecution.job_id == job_id,
            WorkflowTaskExecution.task_index == task_index
        ).first()

        if task_execution:
            task_execution.status = JobStatus.COMPLETED.value if success else JobStatus.FAILED.value
            task_execution.completed_at = datetime.utcnow()
            task_execution.updated_at = datetime.utcnow()
            if result:
                task_execution.result = result
            if error_message:
                task_execution.error_message = error_message

        self.db.commit()

    async def requeue_task(self, job_id: UUID, task_index: int, retry_after: int = 30) -> None:
        """Mark a task as queued for retry (backpressure handling)."""
        logger.info(f"🔄 Requeuing task {task_index} for job {job_id} with {retry_after}s delay")

        # Update task status back to queued
        task_execution = self.db.query(WorkflowTaskExecution).filter(
            WorkflowTaskExecution.job_id == job_id,
            WorkflowTaskExecution.task_index == task_index
        ).first()

        if task_execution:
            task_execution.status = JobStatus.QUEUED.value  # Reset to queued
            task_execution.completed_at = None  # Clear completion time
            task_execution.updated_at = datetime.utcnow()
            task_execution.error_message = f"Requeued due to resource unavailability, retry in {retry_after}s"
            logger.info(f"✅ Task execution {task_execution.id} status reset to QUEUED")
        else:
            logger.warning(f"⚠️ Task execution not found for job {job_id}, task {task_index}")

        # Update job status back to running (not completed)
        job = self.db.query(Job).filter(Job.job_id == job_id).first()
        if job:
            job.status = JobStatus.RUNNING.value  # Keep as running
            job.updated_at = datetime.utcnow()
            logger.info(f"✅ Job {job_id} status kept as RUNNING for requeue")
        else:
            logger.error(f"❌ Job {job_id} not found during requeue")

        self.db.commit()
    
    async def update_workflow_progress(self, job_id: UUID) -> None:
        """Calculate and update overall workflow progress percentage."""
        # Get all tasks for this job
        total_tasks = self.db.query(WorkflowTaskExecution).filter(
            WorkflowTaskExecution.job_id == job_id
        ).count()
        
        completed_tasks = self.db.query(WorkflowTaskExecution).filter(
            WorkflowTaskExecution.job_id == job_id,
            WorkflowTaskExecution.status == JobStatus.COMPLETED.value
        ).count()
        
        if total_tasks > 0:
            progress = int((completed_tasks / total_tasks) * 100)
        else:
            progress = 0
        
        # Update job progress
        job = self.db.query(Job).filter(Job.job_id == job_id).first()
        if job:
            job.workflow_progress_percentage = progress
            job.updated_at = datetime.utcnow()
        
        self.db.commit()
    
    def get_workflow_status(self, job_id: UUID) -> Optional[Dict[str, Any]]:
        """Get detailed workflow status including all tasks."""
        # Get all task executions
        task_executions = self.db.query(WorkflowTaskExecution).filter(
            WorkflowTaskExecution.job_id == job_id
        ).order_by(WorkflowTaskExecution.task_index).all()

        if not task_executions:
            return None

        tasks = []
        for task_execution in task_executions:
            tasks.append({
                "task_type": task_execution.task_type,
                "task_index": task_execution.task_index,
                "status": task_execution.status,
                "parameters": task_execution.parameters,
                "result": task_execution.result,
                "started_at": task_execution.started_at,
                "completed_at": task_execution.completed_at,
                "error_message": task_execution.error_message
            })

        return {"tasks": tasks}
