"""
Job Manager service for handling job lifecycle management.

This service manages job submission, status tracking, cancellation,
and progress updates for the music generation system.
"""

from sqlalchemy.orm import Session
from fastapi import HTTPException
from typing import Dict, Any, Optional
from uuid import UUID
from datetime import datetime, timezone
import logging

from models import Job, WorkflowTaskExecution
from schemas import (
    JobSubmissionRequest, JobResponse, JobType, JobStatus, TaskType
)
from config import storage_config, queue_config
from .queue_manager import CloudTasksQueueManager
from .storage_manager import StorageManager

# Configure logger
logger = logging.getLogger(__name__)


class JobManager:
    """Manages job lifecycle including submission, tracking, and cancellation."""
    
    def __init__(self, db: Session):
        self.db = db
        self.storage_config = storage_config
        self.storage_manager = StorageManager()
        self.queues = queue_config
        self.queue_manager = CloudTasksQueueManager(queue_config)

    # ============================================================================
    # STORAGE HELPER METHODS
    # ============================================================================

    async def get_job_asset_urls(self, job_id: str) -> Dict[str, str]:
        """
        Get asset URLs for a completed job based on what was actually generated.

        This method analyzes the actual task results to determine which assets were
        really generated, rather than returning placeholder paths.

        Returns:
            Dict[str, str]: Dictionary of asset types to actual GCS URLs
        """
        # Always include metadata for completed jobs
        assets: Dict[str, str] = {
            "metadata": f"gs://{self.storage_config.bucket_name}/{self.storage_config.get_results_path(job_id)}"
        }

        try:
            # Get the job record to determine type and get task results
            job_uuid = UUID(job_id)
            job = self.db.query(Job).filter(Job.job_id == job_uuid).first()
            if not job:
                return assets

            job_type_val = str(job.job_type)

            if job_type_val == "workflow":
                # For workflows, check each task to see what was actually generated
                workflow_assets = await self._get_workflow_assets(job_id)
                assets.update(workflow_assets)

        except Exception as e:
            # If anything fails, just return metadata
            print(f"Error getting job assets for {job_id}: {e}")

        return assets

    async def _get_music_generation_assets(self, job_id: str) -> Dict[str, str]:
        """Get music generation assets with new naming (for non-workflow compatibility)."""
        assets: Dict[str, str] = {}
        try:
            result_json = await self.storage_manager.get_job_result(job_id)
            if not result_json:
                return assets

            storage_info = result_json.get("result", {}).get("storage", {})
            audio = self._extract_audio_with_preference(storage_info, "primary_track", "tracks")
            preferred = audio.get("wav") or audio.get("mp3")
            if preferred:
                assets["music_generation_audio"] = preferred
            if "wav" in audio:
                assets["music_generation_audio_wav"] = audio["wav"]
            if "mp3" in audio:
                assets["music_generation_audio_mp3"] = audio["mp3"]
        except Exception as e:
            print(f"Error getting music generation assets for {job_id}: {e}")
        return assets

    async def _get_album_cover_assets(self, job_id: str) -> Dict[str, str]:
        """Get album cover assets with new naming (for non-workflow compatibility)."""
        assets: Dict[str, str] = {}
        try:
            result_json = await self.storage_manager.get_job_result(job_id)
            if not result_json:
                return assets

            storage_info = result_json.get("result", {}).get("storage", {})
            gcs_path = storage_info.get("gcs_path")
            if isinstance(gcs_path, str):
                lp = gcs_path.lower()
                preferred = gcs_path
                if lp.endswith(".jpg") or lp.endswith(".jpeg"):
                    assets["album_cover_image_jpg"] = gcs_path
                elif lp.endswith(".png"):
                    assets["album_cover_image_png"] = gcs_path
                else:
                    assets["album_cover_image_jpg"] = gcs_path
                if lp.endswith(".png") and "album_cover_image_jpg" in assets:
                    preferred = assets["album_cover_image_jpg"]
                assets["album_cover_image"] = preferred
        except Exception as e:
            print(f"Error getting album cover assets for {job_id}: {e}")
        return assets

    def _extract_audio_with_preference(self, storage_info: Dict[str, Any], primary_key: str, list_key: str) -> Dict[str, str]:
        """Extract audio paths with WAV preference; returns dict keys 'wav'/'mp3' if found."""
        out: Dict[str, str] = {}
        # Check primary
        primary = storage_info.get(primary_key)
        if isinstance(primary, dict):
            gcs_path = primary.get("gcs_path")
            if isinstance(gcs_path, str):
                lp = gcs_path.lower()
                if lp.endswith(".wav"):
                    out["wav"] = gcs_path
                elif lp.endswith(".mp3"):
                    out["mp3"] = gcs_path
                else:
                    # Unknown extension, treat as preferred
                    out.setdefault("wav", gcs_path)
        # Check list
        if not out and list_key in storage_info:
            items = storage_info.get(list_key)
            if isinstance(items, list) and items:
                # Prefer wav, then mp3
                for it in items:
                    if isinstance(it, dict) and isinstance(it.get("gcs_path"), str):
                        lp = it["gcs_path"].lower()
                        if lp.endswith(".wav") and "wav" not in out:
                            out["wav"] = it["gcs_path"]
                        elif lp.endswith(".mp3") and "mp3" not in out:
                            out["mp3"] = it["gcs_path"]
                # Fallback to first entry if nothing matched
                if not out:
                    first = items[0]
                    if isinstance(first, dict) and isinstance(first.get("gcs_path"), str):
                        out.setdefault("wav", first["gcs_path"])  # store under wav key as default
        return out

    async def _get_workflow_assets(self, job_id: str) -> Dict[str, str]:
        """Get workflow assets using descriptive, non-overlapping keys with WAV preference."""
        assets: Dict[str, str] = {}
        try:
            # Get job record to access task results
            job_uuid = UUID(job_id)
            job = self.db.query(Job).filter(Job.job_id == job_uuid).first()
            if not job:
                return assets

            workflow_executions = self.db.query(WorkflowTaskExecution).filter(
                WorkflowTaskExecution.job_id == job_uuid
            ).all()

            task_results: Dict[int, Dict[str, Any]] = {}
            for execution in workflow_executions:
                if execution.result:
                    task_results[execution.task_index] = execution.result

            for execution in workflow_executions:
                task_type = execution.task_type
                task_index = execution.task_index
                if execution.status != "completed":
                    continue

                # Music Generation
                if task_type == "music_generation" and task_index in task_results:
                    task_result = task_results[task_index]
                    storage_info = task_result.get("result", {}).get("storage", {})
                    audio = self._extract_audio_with_preference(storage_info, "primary_track", "tracks")
                    preferred = audio.get("wav") or audio.get("mp3")
                    if preferred:
                        assets["music_generation_audio"] = preferred
                    if "wav" in audio:
                        assets["music_generation_audio_wav"] = audio["wav"]
                    if "mp3" in audio:
                        assets["music_generation_audio_mp3"] = audio["mp3"]

                # Sound Generation
                elif task_type == "sound_generation" and task_index in task_results:
                    task_result = task_results[task_index]
                    storage_info = task_result.get("result", {}).get("storage", {})
                    audio = self._extract_audio_with_preference(storage_info, "primary_sound", "sounds")
                    preferred = audio.get("wav") or audio.get("mp3")
                    if preferred:
                        assets["sound_generation_audio"] = preferred
                    if "wav" in audio:
                        assets["sound_generation_audio_wav"] = audio["wav"]
                    if "mp3" in audio:
                        assets["sound_generation_audio_mp3"] = audio["mp3"]

                # Music Extension
                elif task_type == "music_extension" and task_index in task_results:
                    task_result = task_results[task_index]
                    storage_info = task_result.get("result", {}).get("storage", {})
                    audio = self._extract_audio_with_preference(storage_info, "primary_track", "tracks")
                    preferred = audio.get("wav") or audio.get("mp3")
                    if preferred:
                        assets["music_extension_audio"] = preferred
                    if "wav" in audio:
                        assets["music_extension_audio_wav"] = audio["wav"]
                    if "mp3" in audio:
                        assets["music_extension_audio_mp3"] = audio["mp3"]

                # Music Remixing (different storage shape)
                elif task_type == "music_remixing" and task_index in task_results:
                    task_result = task_results[task_index]
                    storage_info = task_result.get("storage", {})
                    remixed_wav = storage_info.get("remixed_song_1_wav")
                    remixed_mp3 = storage_info.get("remixed_song_1_mp3")
                    preferred = remixed_wav or remixed_mp3
                    if preferred:
                        assets["music_remixing_audio"] = preferred
                    if remixed_wav:
                        assets["music_remixing_audio_wav"] = remixed_wav
                    if remixed_mp3:
                        assets["music_remixing_audio_mp3"] = remixed_mp3

                # Voice Over Generation
                elif task_type == "voice_over_generation" and task_index in task_results:
                    task_result = task_results[task_index]
                    storage_info = task_result.get("result", {}).get("storage", {})
                    audio = self._extract_audio_with_preference(storage_info, "primary_voice", "voices")
                    preferred = audio.get("wav") or audio.get("mp3")
                    if preferred:
                        assets["voice_over_audio"] = preferred
                    if "wav" in audio:
                        assets["voice_over_audio_wav"] = audio["wav"]

                # Album Cover Generation
                elif task_type == "album_cover_generation" and task_index in task_results:
                    task_result = task_results[task_index]
                    storage_info = task_result.get("result", {}).get("storage", {})
                    gcs_path = storage_info.get("gcs_path")
                    if isinstance(gcs_path, str):
                        lp = gcs_path.lower()
                        preferred = gcs_path
                        if lp.endswith(".jpg") or lp.endswith(".jpeg"):
                            assets["album_cover_image_jpg"] = gcs_path
                        elif lp.endswith(".png"):
                            assets["album_cover_image_png"] = gcs_path
                        else:
                            # Unknown, default to jpg key
                            assets["album_cover_image_jpg"] = gcs_path
                        # Preferred image (jpg over png)
                        if lp.endswith(".png") and "album_cover_image_jpg" in assets:
                            preferred = assets["album_cover_image_jpg"]
                        assets["album_cover_image"] = preferred

        except Exception as e:
            print(f"Error getting workflow assets for {job_id}: {e}")

        return assets

    def get_processing_file_path(self, job_id: str, filename: str) -> str:
        """Get path for intermediate processing files"""
        return self.storage_config.get_temp_processing_path(job_id, filename)

    # ============================================================================
    # QUEUE HELPER METHODS
    # ============================================================================

    def get_queue_for_job(self, job: Job) -> str:
        """Get appropriate queue name for entire job based on task composition.

        Jobs containing slow tasks (music generation, remixing) go to musicgpt_queue.
        Jobs with only fast tasks go to task_queue.
        Workflow orchestration stays in job_queue.
        """
        try:
            config = job.configuration or {}
            tasks = config.get("tasks", [])

            # Define slow tasks that require MusicGPT queue
            slow_task_types = {
                TaskType.MUSIC_GENERATION.value,
                TaskType.SOUND_GENERATION.value,
                TaskType.MUSIC_EXTENSION.value,
                TaskType.MUSIC_REMIXING.value,
            }

            # ElevenLabs tasks that should go to ElevenLabs queue
            elevenlabs_task_types = {
                TaskType.VOICE_OVER_GENERATION.value,
                TaskType.VOICE_GENERATION.value,
                TaskType.AUDIO_TRANSLATION.value,
            }

            # Check which queues are needed
            has_musicgpt_tasks = False
            has_elevenlabs_tasks = False
            task_types_found = []

            for task in tasks:
                task_type = task.get("task_type", "").lower()
                task_types_found.append(task_type)
                if task_type in slow_task_types:
                    has_musicgpt_tasks = True
                if task_type in elevenlabs_task_types:
                    has_elevenlabs_tasks = True

            # Prioritize ElevenLabs queue if any ElevenLabs task present and no MusicGPT
            if has_elevenlabs_tasks and not has_musicgpt_tasks and self.queues.elevenlabs_queue:
                logger.info(f"Job {job.job_id} contains ElevenLabs tasks, routing to elevenlabs_queue. Tasks: {task_types_found}")
                return self.queues.elevenlabs_queue

            if has_musicgpt_tasks:
                logger.info(f"Job {job.job_id} contains slow tasks, routing to musicgpt_queue. Tasks: {task_types_found}")
                return self.queues.musicgpt_queue

            # Job contains only fast tasks
            logger.info(f"Job {job.job_id} contains only fast tasks, routing to task_queue. Tasks: {task_types_found}")
            return self.queues.task_queue

        except Exception as e:
            logger.error(f"Error determining queue for job {job.job_id}: {e}")
            # Fallback to job_queue for safety
            return self.queues.job_queue



    async def queue_priority_task(self, task_data: Dict[str, Any]):
        """Queue urgent tasks (cancellations, status updates) to priority queue"""
        queue_name = self.queues.priority_queue
        if queue_name:
            await self.queue_manager.enqueue_task(queue_name, task_data)



    # ============================================================================
    # JOB LIFECYCLE METHODS
    # ============================================================================

    async def submit_job(self, request: JobSubmissionRequest, client_ip: str, user_agent: str) -> JobResponse:
        """Submit a new job for processing."""
        job = Job(
            job_type=request.job_type.value,
            configuration=request.model_dump(),
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        self.db.add(job)
        self.db.commit()
        self.db.refresh(job)
        
        # Queue job for processing (implement with Cloud Tasks)
        await self.queue_job(job.job_id)
        
        return JobResponse(
            job_id=job.job_id,
            job_type=JobType(job.job_type),
            status=JobStatus(job.status),
            created_at=job.created_at
        )
    
    async def queue_job(self, job_id: UUID):
        """Queue job to appropriate queue based on task composition"""
        job = self.db.query(Job).filter(Job.job_id == job_id).first()
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        # Route job to appropriate queue based on task composition
        queue_name = self.get_queue_for_job(job)

        logger.info(f"Queueing job {job_id} to {queue_name}")

        # Enqueue job to Cloud Tasks
        task_data = {
            "job_id": str(job_id),
            "queue": queue_name,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "action": "process_job"
        }

        if queue_name:
            await self.queue_manager.enqueue_task(queue_name, task_data)
    
    async def get_job_status(self, job_id: UUID) -> Optional[JobResponse]:
        """Get current job status and details."""
        job = self.db.query(Job).filter(Job.job_id == job_id).first()

        if job:
            return JobResponse(
                job_id=job.job_id,
                job_type=JobType(job.job_type),
                status=JobStatus(job.status),
                created_at=job.created_at,
                current_task_type=job.current_task_type,
                current_task_index=job.current_task_index,
                workflow_progress_percentage=job.workflow_progress_percentage
            )
        return None

    async def cancel_job(self, job_id: UUID) -> bool:
        """Cancel a running job using priority queue for immediate processing"""
        job = self.db.query(Job).filter(Job.job_id == job_id).first()

        if not job:
            return False

        if job.status in ["completed", "failed", "cancelled"]:
            return False  # Job already finished

        # Update job status immediately
        job.status = "cancelled"
        job.updated_at = datetime.now(timezone.utc)
        self.db.commit()

        # Queue cancellation task to priority queue for immediate processing
        await self.queue_priority_task({
            "action": "cancel_job",
            "job_id": str(job_id),
            "timestamp": datetime.now(timezone.utc).isoformat()
        })

        return True

    async def update_job_progress(self, job_id: UUID, progress_data: Dict[str, Any]):
        """Update job progress using priority queue for real-time updates"""
        await self.queue_priority_task({
            "action": "update_progress",
            "job_id": str(job_id),
            "progress": progress_data,
            "timestamp": datetime.utcnow().isoformat()
        })

    async def requeue_job_with_delay(self, job_id: UUID, delay_seconds: int = 30):
        """Re-enqueue a job with delay for backpressure handling"""
        logger.info(f"🔄 Re-enqueueing job {job_id} with {delay_seconds}s delay for backpressure handling")

        job = self.db.query(Job).filter(Job.job_id == job_id).first()
        if not job:
            logger.error(f"❌ Job {job_id} not found for requeue")
            raise ValueError(f"Job {job_id} not found")

        # Requeue job back to the same queue it was originally assigned to
        queue_name = self.get_queue_for_job(job)
        if not queue_name:
            logger.error(f"❌ Queue name not configured for job {job_id}")
            raise ValueError("Queue name not configured")

        logger.info(f"🔄 Requeuing job {job_id} to {queue_name} with {delay_seconds}s delay")

        # Prepare task data for re-enqueueing
        task_data = {
            "job_id": str(job_id),
            "queue": queue_name,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "action": "process_job",
            "retry_reason": "resource_unavailable"
        }

        try:
            # Enqueue with delay using Cloud Tasks schedule_time
            await self.queue_manager.enqueue_task(
                queue_name=queue_name,
                task_data=task_data,
                delay_seconds=delay_seconds
            )

            logger.info(f"✅ Job {job_id} successfully requeued to {queue_name} with {delay_seconds}s delay due to resource unavailability")

        except Exception as e:
            logger.error(f"❌ Failed to requeue job {job_id}: {e}")
            raise


