"""
Configuration classes for the Music Generation Job Management system.

This module contains configuration classes for storage, queues, and other
system settings that are loaded from environment variables.
"""

import os
from typing import Optional


class StorageConfig:
    """Single bucket storage configuration with organized folder structure."""

    def __init__(self):
        self.bucket_name = os.getenv('GCS_STORAGE_BUCKET')
        self.assets_prefix = os.getenv('GCS_ASSETS_PREFIX', 'assets/')
        self.temp_prefix = os.getenv('GCS_TEMP_PREFIX', 'temp/')
        self.results_prefix = os.getenv('GCS_RESULTS_PREFIX', 'results/')

        if not self.bucket_name:
            raise ValueError("GCS_STORAGE_BUCKET environment variable is required")

    def get_music_path(self, job_id: str, filename: str) -> str:
        """Get path for music files: assets/music/{job_id}/{filename}"""
        return f"{self.assets_prefix}music/{job_id}/{filename}"

    def get_image_path(self, job_id: str, filename: str) -> str:
        """Get path for image files: assets/images/{job_id}/{filename}"""
        return f"{self.assets_prefix}images/{job_id}/{filename}"



    def get_temp_processing_path(self, job_id: str, filename: str) -> str:
        """Get path for processing files: temp/processing/{job_id}/{filename}"""
        return f"{self.temp_prefix}processing/{job_id}/{filename}"

    def get_results_path(self, job_id: str, filename: str = "job_result.json") -> str:
        """Get path for job results: results/{job_id}/{filename}"""
        return f"{self.results_prefix}{job_id}/{filename}"


class QueueConfig:
    """Queue configuration for different task types."""

    def __init__(self):
        self.job_queue = os.getenv('JOB_QUEUE_NAME')
        self.task_queue = os.getenv('TASK_QUEUE_NAME')
        self.musicgpt_queue = os.getenv('MUSICGPT_QUEUE_NAME')
        self.elevenlabs_queue = os.getenv('ELEVENLABS_QUEUE_NAME')
        self.priority_queue = os.getenv('PRIORITY_QUEUE_NAME')
        self.location = os.getenv('CLOUD_TASKS_LOCATION')


class DatabaseConfig:
    """Database configuration settings optimized for high concurrency."""

    def __init__(self):
        self.database_url = os.getenv('DATABASE_URL')
        # CRITICAL FIX: Increase pool sizes for 1000 concurrent jobs
        self.pool_size = int(os.getenv('DB_POOL_SIZE', '50'))  # Increased from 10
        self.max_overflow = int(os.getenv('DB_MAX_OVERFLOW', '100'))  # Increased from 20
        self.pool_timeout = int(os.getenv('DB_POOL_TIMEOUT', '10'))  # Reduced from 30
        self.pool_recycle = int(os.getenv('DB_POOL_RECYCLE', '1800'))  # Reduced from 3600
        # Add connection health checks
        self.pool_pre_ping = True
        self.pool_reset_on_return = 'commit'

        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable is required")


class AppConfig:
    """General application configuration."""

    def __init__(self):
        self.environment = os.getenv('ENVIRONMENT', 'development')
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
        self.debug = os.getenv('DEBUG', 'false').lower() == 'true'
        self.log_level = os.getenv('LOG_LEVEL', 'INFO')

        if not self.project_id:
            raise ValueError("GOOGLE_CLOUD_PROJECT environment variable is required")


# Global configuration instances
storage_config = StorageConfig()
queue_config = QueueConfig()
database_config = DatabaseConfig()
app_config = AppConfig()
