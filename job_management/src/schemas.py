"""
Pydantic schemas for request/response models and enums.

This module contains all Pydantic models used for API request validation,
response serialization, and data transfer objects.

Note: Database schema is defined in database_schema.sql, not in Python code.
The SQLAlchemy models in models.py should match the database schema.
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from enum import Enum
from uuid import UUID
from datetime import datetime


# ============================================================================
# ENUMS
# ============================================================================

class JobType(str, Enum):
    """Types of jobs supported by the system."""
    WORKFLOW = "workflow"


class JobStatus(str, Enum):
    """Possible job statuses."""
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REQUEUED = "requeued"


class TaskType(str, Enum):
    """Types of tasks that can be executed."""
    # Content generation tasks
    PROMPT_ENHANCEMENT = "prompt_enhancement"
    MUSIC_GENERATION = "music_generation"
    SOUND_GENERATION = "sound_generation"
    MUSIC_EXTENSION = "music_extension"
    MUSIC_REMIXING = "music_remixing"
    VOCAL_EXTRACTION = "vocal_extraction"
    ALBUM_COVER_GENERATION = "album_cover_generation"
    ARTIST_GENERATION = "artist_generation"
    SPOKEN_WORD_SCRIPT = "spoken_word_script"
    TEXT_TRANSLATION = "text_translation"
    # ElevenLabs tasks
    VOICE_GENERATION = "voice_generation"
    VOICE_OVER_GENERATION = "voice_over_generation"
    AUDIO_TRANSLATION = "audio_translation"
    # Transcoder tasks
    TRANSCODER_PIPELINE = "transcoder_pipeline"
    # Utility tasks
    EXPORT = "export"


# ============================================================================
# REQUEST/RESPONSE MODELS
# ============================================================================

class TaskConfig(BaseModel):
    """Configuration for individual tasks."""
    task_type: TaskType
    parameters: Dict[str, Any] = Field(default_factory=dict)


class JobSubmissionRequest(BaseModel):
    """Job submission request for workflow jobs."""
    job_type: JobType
    workflow_name: Optional[str] = None
    tasks: List[TaskConfig]
    
    # Optional metadata
    priority: int = Field(default=5, ge=1, le=10)
    timeout_seconds: int = Field(default=3600, gt=0)
    max_retries: int = Field(default=3, ge=0, le=10)
    metadata: Optional[Dict[str, Any]] = None


class JobResponse(BaseModel):
    """Job response model."""
    job_id: UUID
    job_type: JobType
    status: JobStatus
    created_at: datetime
    progress_percentage: float = 0.0
    estimated_completion: Optional[datetime] = None
    # Workflow tracking fields
    current_task_type: Optional[str] = None
    current_task_index: Optional[int] = None
    workflow_progress_percentage: Optional[int] = None


class TaskExecutionStatus(BaseModel):
    """Status of a single task execution within a workflow."""
    task_type: str
    task_index: int
    status: JobStatus
    parameters: Optional[Dict[str, Any]] = None
    result: Optional[Dict[str, Any]] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


class JobResultsData(BaseModel):
    """Job results data including assets and storage information."""
    assets: Dict[str, str] = {}
    storage_info: Optional[Dict[str, Any]] = None


class DetailedJobResponse(BaseModel):
    """Detailed job response with task breakdown for workflows."""
    job_id: UUID
    job_type: JobType
    status: JobStatus
    created_at: datetime
    updated_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress_percentage: float = 0.0
    estimated_completion: Optional[datetime] = None
    error_message: Optional[str] = None
    # Workflow-specific fields
    current_task_type: Optional[str] = None
    current_task_index: Optional[int] = None
    workflow_progress_percentage: Optional[int] = None
    tasks: Optional[List[TaskExecutionStatus]] = None
    # Results data (only populated for completed jobs)
    results: Optional[JobResultsData] = None
