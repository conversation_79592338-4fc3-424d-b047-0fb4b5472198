#!/usr/bin/env python3
"""
Generate job JSON files from a CSV using a JSON template.

- Reads CSV rows from --csv (default: spoken_word/csv/Mindfullness_test_7_json.csv)
- Loads JSON template from --template (default: spoken_word/sp_pipline_template.json)
- For each row:
  * Generates a UUID for Trace Id
  * Creates a workflow name sp_track_{row_index}
  * Replaces placeholders like {{Column Name}} with CSV values
  * Replaces {{Trace Id}} and {{Workflow Name}}
  * Validates JSON and writes to a timestamped output directory under --out-dir (default: spoken_word/jobs)

Basic error handling and validation included.
"""
from __future__ import annotations

import argparse
import csv
import json
import os
import re
import sys
import uuid
from datetime import datetime
from typing import Dict, Iterable, List, Set


def read_csv_rows(csv_path: str) -> Iterable[Dict[str, str]]:
    """Yield dictionaries for each CSV row using the header row as keys."""
    try:
        with open(csv_path, mode="r", encoding="utf-8-sig", newline="") as f:
            reader = csv.DictReader(f)
            if reader.fieldnames is None:
                raise ValueError("CSV appears to have no header row.")
            for i, row in enumerate(reader, start=1):
                # Skip completely empty rows
                if row is None or all((v is None or str(v).strip() == "") for v in row.values()):
                    continue
                yield row
    except FileNotFoundError:
        raise
    except csv.Error as e:
        raise RuntimeError(f"Failed to parse CSV file '{csv_path}': {e}") from e


def load_template_text(template_path: str) -> str:
    try:
        with open(template_path, mode="r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        raise
    except OSError as e:
        raise RuntimeError(f"Failed to read template file '{template_path}': {e}") from e


def find_placeholders(template_text: str) -> Set[str]:
    """Return set of placeholder names found in the template like {{Name}} -> 'Name'."""
    pattern = re.compile(r"\{\{\s*([^}]+?)\s*\}\}")
    return set(m.group(1).strip() for m in pattern.finditer(template_text))


def substitute_placeholders(template_text: str, substitutions: Dict[str, str]) -> str:
    """Replace all {{Key}} with value for each key in substitutions. Keys are matched literally.

    All values are JSON-escaped for safe insertion inside string contexts in the template.
    Example: key 'Trace Id' replaces all occurrences of '{{Trace Id}}'.
    """
    result = template_text
    # Sort keys by length descending to avoid partial overlaps (defensive)
    for key in sorted(substitutions.keys(), key=len, reverse=True):
        placeholder = "{{" + key + "}}"
        val = substitutions[key]
        # JSON-escape the value and strip surrounding quotes to embed into quoted strings in template
        escaped = json.dumps(val)[1:-1]
        result = result.replace(placeholder, escaped)
    return result


def validate_json(json_text: str) -> Dict:
    try:
        return json.loads(json_text)
    except json.JSONDecodeError as e:
        # Provide some context to help diagnose template/substitution errors
        snippet = json_text[max(0, e.pos - 80): e.pos + 80]
        raise ValueError(
            f"Generated JSON is invalid: {e}\n...{snippet}..."
        ) from e


def ensure_dir(path: str) -> None:
    os.makedirs(path, exist_ok=True)


def generate_output_dir(base_out_dir: str) -> str:
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    out_dir = os.path.join(base_out_dir, f"sp_tracks_{ts}")
    ensure_dir(out_dir)
    return out_dir


def main(argv: List[str] | None = None) -> int:
    parser = argparse.ArgumentParser(description="Generate job JSON files from CSV and template")


    default_csv = os.path.join("csv", "Mindfullness_test_7_json.csv")
    default_template = os.path.join("sp_pipeline_template.json")
    default_out_dir = os.path.join("jobs")

    parser.add_argument("--csv", dest="csv_path", default=default_csv, help="Path to input CSV file")
    parser.add_argument("--template", dest="template_path", default=default_template, help="Path to JSON template file")
    parser.add_argument("--out-dir", dest="out_dir", default=default_out_dir, help="Base output directory (JSON files will be written into a timestamped subfolder)")
    parser.add_argument("--start-index", dest="start_index", type=int, default=1, help="Starting index for workflow numbering (default: 1)")

    args = parser.parse_args(argv)

    # Load template
    try:
        template_text = load_template_text(args.template_path)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        return 1

    placeholders = find_placeholders(template_text)
    # We will auto-fill these from code
    auto_keys = {"Trace Id", "Workflow Name"}

    # Prepare output folder
    try:
        batch_out_dir = generate_output_dir(args.out_dir)
    except Exception as e:
        print(f"Error creating output directory: {e}", file=sys.stderr)
        return 1

    # Process CSV rows
    written = 0
    errors: List[str] = []

    try:
        rows = list(read_csv_rows(args.csv_path))
    except Exception as e:
        print(f"Error reading CSV: {e}", file=sys.stderr)
        return 1

    # Validate that any template placeholders (besides auto_keys) exist in CSV headers
    csv_headers = set(rows[0].keys()) if rows else set()
    missing_from_csv = {ph for ph in placeholders if ph not in auto_keys and ph not in csv_headers}
    if missing_from_csv:
        print(
            "Warning: some template placeholders have no matching CSV columns (they will remain unreplaced if not in substitutions):\n  "
            + ", ".join(sorted(missing_from_csv)),
            file=sys.stderr,
        )

    for idx, row in enumerate(rows, start=args.start_index):
        trace_id = str(uuid.uuid4())
        print(trace_id)
        workflow_name = f"sp_track_{idx}"

        # Build substitution map: CSV columns + auto keys
        substitutions: Dict[str, str] = {}
        for k, v in row.items():
            if v is None:
                continue
            substitutions[k] = str(v).strip()
        substitutions["Trace Id"] = trace_id
        substitutions["Workflow Name"] = workflow_name

        try:
            replaced_text = substitute_placeholders(template_text, substitutions)
            data = validate_json(replaced_text)
        except Exception as e:
            err = f"Row {idx}: Failed to generate JSON: {e}"
            print(err, file=sys.stderr)
            errors.append(err)
            continue

        filename = f"{workflow_name}.json"
        out_path = os.path.join(batch_out_dir, filename)
        try:
            with open(out_path, mode="w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            written += 1
            print(f"Wrote {out_path}")
        except OSError as e:
            err = f"Row {idx}: Failed to write file '{out_path}': {e}"
            print(err, file=sys.stderr)
            errors.append(err)

    print(f"Done. Wrote {written} JSON file(s) to {batch_out_dir}.")
    if errors:
        print("Encountered errors:", file=sys.stderr)
        for e in errors:
            print("- " + e, file=sys.stderr)
        # Non-zero exit to signal partial failure
        return 2
    return 0


if __name__ == "__main__":
    sys.exit(main())

