#!/usr/bin/env python3
"""
<PERSON>ript to split a large JSON workflow file into smaller, more manageable files.
Each sub-file will contain exactly 10 jobs (except the last file which may contain fewer).
"""

import json
import os
from pathlib import Path

def split_json_jobs(input_file_path, jobs_per_file=10):
    """
    Split a JSON file containing workflow jobs into smaller files.
    
    Args:
        input_file_path (str): Path to the input JSON file
        jobs_per_file (int): Number of jobs per output file (default: 10)
    """
    
    # Read the original JSON file
    with open(input_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Extract the jobs array
    jobs = data.get('jobs', [])
    total_jobs = len(jobs)
    
    print(f"Total jobs found: {total_jobs}")
    
    # Calculate number of output files needed
    num_files = (total_jobs + jobs_per_file - 1) // jobs_per_file  # Ceiling division
    print(f"Will create {num_files} output files")
    
    # Get the base filename without extension
    input_path = Path(input_file_path)
    base_name = input_path.stem
    output_dir = input_path.parent
    
    # Split jobs into chunks and create output files
    for i in range(num_files):
        start_idx = i * jobs_per_file
        end_idx = min(start_idx + jobs_per_file, total_jobs)
        
        # Extract jobs for this chunk
        chunk_jobs = jobs[start_idx:end_idx]
        
        # Create new JSON structure with the same format as original
        output_data = {
            "description": f"{data.get('description', '')} - Part {i+1} of {num_files} (jobs {start_idx+1}-{end_idx})",
            "jobs": chunk_jobs
        }
        
        # Generate output filename
        output_filename = f"{base_name}_part{i+1}.json"
        output_path = output_dir / output_filename
        
        # Write the output file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        print(f"Created {output_filename} with {len(chunk_jobs)} jobs")
    
    print(f"\nSplit complete! Created {num_files} files in {output_dir}")

def validate_split_files(original_file_path, output_pattern):
    """
    Validate that the split files contain all jobs from the original file.
    
    Args:
        original_file_path (str): Path to the original JSON file
        output_pattern (str): Pattern to match output files (e.g., "*_part*.json")
    """
    
    # Read original file
    with open(original_file_path, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    original_jobs = original_data.get('jobs', [])
    original_count = len(original_jobs)
    
    # Find all split files
    input_path = Path(original_file_path)
    output_dir = input_path.parent
    base_name = input_path.stem
    
    split_files = list(output_dir.glob(f"{base_name}_part*.json"))
    split_files.sort()
    
    total_split_jobs = 0
    
    print(f"Validating {len(split_files)} split files...")
    
    for split_file in split_files:
        with open(split_file, 'r', encoding='utf-8') as f:
            split_data = json.load(f)
        
        split_jobs = split_data.get('jobs', [])
        job_count = len(split_jobs)
        total_split_jobs += job_count
        
        print(f"  {split_file.name}: {job_count} jobs")
        
        # Validate JSON structure
        try:
            json.dumps(split_data)  # Test if it's valid JSON
            print(f"    ✓ Valid JSON structure")
        except Exception as e:
            print(f"    ✗ Invalid JSON structure: {e}")
    
    print(f"\nValidation Summary:")
    print(f"  Original file jobs: {original_count}")
    print(f"  Split files jobs: {total_split_jobs}")
    
    if original_count == total_split_jobs:
        print("  ✓ All jobs successfully split!")
    else:
        print("  ✗ Job count mismatch!")
    
    return original_count == total_split_jobs

if __name__ == "__main__":
    import sys
    
    # Check if input file is provided as command line argument
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
        jobs_per_file = int(sys.argv[2]) if len(sys.argv) > 2 else 10
    else:
        # Default configuration
        input_file = "spoken_word/jobs/Mindfullness_test_7_json_vo_jobs.json"
        jobs_per_file = 10
    
    print("Starting JSON job splitting process...")
    print(f"Input file: {input_file}")
    print(f"Jobs per file: {jobs_per_file}")
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found!")
        print(f"Usage: python3 {sys.argv[0]} <input_file> [jobs_per_file]")
        exit(1)
    
    # Split the file
    split_json_jobs(input_file, jobs_per_file=jobs_per_file)
    
    # Validate the split
    print("\nValidating split files...")
    validation_success = validate_split_files(input_file, "*_part*.json")
    
    if validation_success:
        print("\n✅ Split operation completed successfully!")
    else:
        print("\n❌ Split operation completed with errors!")
