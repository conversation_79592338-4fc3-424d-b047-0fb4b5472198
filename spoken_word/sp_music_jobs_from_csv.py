#!/usr/bin/env python3
"""
Generate music + album cover workflow jobs JSON from a CSV.

Requirements implemented:
1) Extract unique values from "Music Prompt"
2) Create one workflow job per unique music prompt
3) For album cover generation task prompt: use only the "COVER STUFF" value

Usage:
  python spoken_word/sp_music_jobs_from_csv.py --csv path/to/input.csv
"""
import argparse
import copy
import csv
import json
import os
import re
from typing import Dict, List, Tuple, Optional


def norm(s: str) -> str:
    return (s or "").strip()


def find_header(fieldnames: List[str], target: str) -> Optional[str]:
    if not fieldnames:
        return None
    target_n = target.strip().casefold()
    for f in fieldnames:
        if f is None:
            continue
        if f.strip().casefold() == target_n:
            return f
    target_relaxed = re.sub(r"\s+", " ", target_n)
    for f in fieldnames:
        if f is None:
            continue
        f_relaxed = re.sub(r"\s+", " ", f.strip().casefold())
        if f_relaxed == target_relaxed:
            return f
    return None


def slugify(text: str, max_len: int = 40) -> str:
    text = (text or "").strip().lower()
    text = re.sub(r"[^a-z0-9]+", "-", text)
    text = re.sub(r"-+", "-", text).strip("-")
    if len(text) > max_len:
        text = text[:max_len].rstrip("-")
    return text or "job"


def get_template_job() -> Dict:
    """Return the hardcoded template job structure."""
    return {
        "job_type": "workflow",
        "workflow_name": "music_gen_template",
        "tasks": [
            {
                "task_type": "prompt_enhancement",
                "parameters": {
                    "prompt": "",
                    "creativity": 0.6
                }
            },
            {
                "task_type": "music_generation",
                "parameters": {
                    "make_instrumental": True,
                    "vocal_only": False
                }
            },
            {
                "task_type": "album_cover_generation",
                "parameters": {
                    "prompt": "",
                    "creativity": 0.5
                }
            }
        ]
    }


def build_job_from_template(template_job: Dict, music_prompt: str, album_name: str, cover_stuff: str, idx: int) -> Dict:
    job = copy.deepcopy(template_job)

    base_name = slugify(album_name) if album_name else slugify(music_prompt)
    job["workflow_name"] = f"music_gen_{idx:02d}_{base_name}"

    for task in job.get("tasks", []):
        ttype = task.get("task_type")
        params = task.setdefault("parameters", {})
        if ttype == "prompt_enhancement":
            params["prompt"] = norm(music_prompt)
        elif ttype == "album_cover_generation":
            cover = norm(cover_stuff)
            params["prompt"] = cover or "Minimal, tasteful album cover"

    return job


def extract_unique_prompts(csv_path: str) -> List[Tuple[str, str, str]]:
    """Return list of (music_prompt, album_name, cover_stuff) for unique music prompts."""
    with open(csv_path, newline="", encoding="utf-8-sig") as f:
        sample = f.read(4096)
        f.seek(0)
        delim = ','
        try:
            sniff = csv.Sniffer().sniff(sample, delimiters=",;")
            delim = sniff.delimiter
        except Exception:
            if sample.count(';') > sample.count(','):
                delim = ';'

        reader = csv.DictReader(f, delimiter=delim)
        music_h = find_header(reader.fieldnames, "Music Prompt")
        album_h = find_header(reader.fieldnames, "Album Name")
        cover_h = find_header(reader.fieldnames, "COVER STUFF")

        if not music_h:
            raise KeyError("'Music Prompt' column not found in CSV")
        if not album_h:
            raise KeyError("'Album Name' column not found in CSV")
        if not cover_h:
            raise KeyError("'COVER STUFF' column not found in CSV")

        seen = set()
        results: List[Tuple[str, str, str]] = []
        for row in reader:
            m = norm(row.get(music_h, ""))
            if not m or m in seen:
                continue
            a = norm(row.get(album_h, ""))
            c = norm(row.get(cover_h, ""))
            seen.add(m)
            results.append((m, a, c))
    return results


def main():
    parser = argparse.ArgumentParser(description="Generate jobs JSON from CSV")
    parser.add_argument("--csv", required=True, help="Path to input CSV")
    args = parser.parse_args()

    template_job = get_template_job()

    rows = extract_unique_prompts(args.csv)
    if not rows:
        raise SystemExit("No unique 'Music Prompt' entries found in CSV")

    jobs: List[Dict] = []
    for idx, (music_prompt, album_name, cover_stuff) in enumerate(rows, start=1):
        job = build_job_from_template(template_job, music_prompt, album_name, cover_stuff, idx)
        jobs.append(job)

    out_obj = {
        "description": f"Auto-generated music + album cover workflows from CSV: {os.path.basename(args.csv)}",
        "jobs": jobs,
    }

    csv_dir = os.path.dirname(args.csv)
    csv_name = os.path.basename(args.csv)
    output_name = csv_name.replace(".csv", "_music_jobs.json")
    output_path = os.path.join(csv_dir, output_name)

    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(out_obj, f, indent=2, ensure_ascii=False)

    print(f"Wrote {len(jobs)} jobs to {output_path}")


if __name__ == "__main__":
    main()

