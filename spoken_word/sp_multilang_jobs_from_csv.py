#!/usr/bin/env python3
"""
Generate spoken-word multilingual workflow jobs JSON from a CSV.

Simplified functionality:
1) Extract each row's value from "Voice Over Prompt"
2) Create one workflow job per CSV row (no de-duplication)
3) Use hard-coded template structure with dynamic spoken_word_script parameters
4) Auto-generate output filename and location based on input CSV

Usage:
  python spoken_word/sp_multilang_jobs_from_csv.py --csv path/to/input.csv
"""
import argparse
import csv
import json
import os
import re
from typing import Dict, List, Optional


def norm(s: str) -> str:
    return (s or "").strip()


def find_header(fieldnames: List[str], target: str) -> Optional[str]:
    if not fieldnames:
        return None
    target_n = target.strip().casefold()
    for f in fieldnames:
        if f is None:
            continue
        if f.strip().casefold() == target_n:
            return f
    # relaxed match: collapse spaces
    target_relaxed = re.sub(r"\s+", " ", target_n)
    for f in fieldnames:
        if f is None:
            continue
        f_relaxed = re.sub(r"\s+", " ", f.strip().casefold())
        if f_relaxed == target_relaxed:
            return f
    # try partial contains (e.g., "voice over" in header)
    for f in fieldnames:
        if f is None:
            continue
        f_n = f.strip().casefold()
        if target_n in f_n:
            return f
    return None


def slugify(text: str, max_len: int = 40) -> str:
    text = (text or "").strip().lower()
    text = re.sub(r"[^a-z0-9]+", "-", text)
    text = re.sub(r"-+", "-", text).strip("-")
    if len(text) > max_len:
        text = text[:max_len].rstrip("-")
    return text or "job"


HEURISTIC_MAP = [
    # (pattern, creativity, agent_description)
    (re.compile(r"sleep|slumber|rest|bedtime", re.I), 0.2, "A soft, soothing sleep meditation guide with very slow pacing"),
    (re.compile(r"breath|breathing|breathwork|ujjayi|box breathing", re.I), 0.3, "A calm breathwork instructor with clear, gentle pacing"),
    (re.compile(r"body scan", re.I), 0.3, "A warm mindfulness coach guiding a slow, grounding body scan"),
    (re.compile(r"daily|check\s*-?in|micro\s*-?moment|quick", re.I), 0.3, "A friendly mindfulness coach for short daily check-ins"),
    (re.compile(r"gratitude|compassion|heart|loving-?kindness", re.I), 0.4, "A compassionate mindfulness coach with an uplifting tone"),
    (re.compile(r"release|letting go|dissolv(e|ing)|float away", re.I), 0.3, "A gentle guide helping listeners release tension and worries"),
]


def infer_creativity_and_agent(prompt: str) -> tuple[float, str]:
    p = prompt or ""
    for pat, c, agent in HEURISTIC_MAP:
        if pat.search(p):
            return c, agent
    return 0.35, "A warm, calm mindfulness coach with clear pacing"


def get_template_job() -> Dict:
    """Return the hard-coded template job structure."""
    return {
        "job_type": "workflow",
        "workflow_name": "spoken_word_ml_template",
        "tasks": [
            {
                "task_type": "spoken_word_script",
                "parameters": {
                    "prompt": "",
                    "creativity": 0.35,
                    "agent_description": "A warm, calm mindfulness coach with clear pacing"
                }
            },
            {
                "task_type": "voice_over_generation",
                "parameters": {
                    "voice_id": "42ZF7GefiwXbnDaSkPpY",
                    "filename_base": "English"
                }
            },
            {
                "task_type": "voice_over_generation",
                "parameters": {
                    "voice_id": "42ZF7GefiwXbnDaSkPpY",
                    "filename_base": "English"
                }
            },
            {
                "task_type": "text_translation",
                "parameters": {
                    "target_language": "German",
                    "source_language": "English",
                    "quality": "high",
                    "model_creativity": 0.2
                }
            },
            {
                "task_type": "voice_over_generation",
                "parameters": {
                    "voice_id": "umDfZDi2AcMmDUsDsBfA",
                    "filename_base": "German"
                }
            },
            {
                "task_type": "text_translation",
                "parameters": {
                    "target_language": "French",
                    "source_language": "English",
                    "quality": "high",
                    "model_creativity": 0.2
                }
            },
            {
                "task_type": "voice_over_generation",
                "parameters": {
                    "voice_id": "EryqBbKuawX5rMsewc7f",
                    "filename_base": "French"
                }
            },
            {
                "task_type": "text_translation",
                "parameters": {
                    "target_language": "Spanish",
                    "source_language": "English",
                    "quality": "high",
                    "model_creativity": 0.2
                }
            },
            {
                "task_type": "voice_over_generation",
                "parameters": {
                    "voice_id": "toHqs8ENHjZX53stqKOK",
                    "filename_base": "Spanish"
                }
            },
            {
                "task_type": "text_translation",
                "parameters": {
                    "target_language": "Portuguese",
                    "source_language": "English",
                    "quality": "high",
                    "model_creativity": 0.2
                }
            },
            {
                "task_type": "voice_over_generation",
                "parameters": {
                    "voice_id": "h96v1HCJtcisNNeagp0R",
                    "filename_base": "Portuguese"
                }
            }
        ]
    }


def build_job_from_template(voice_over_prompt: str, idx: int) -> Dict:
    job = get_template_job()

    base_name = slugify(voice_over_prompt)
    job["workflow_name"] = f"spoken_word_ml_{idx:02d}_{base_name}"

    for task in job.get("tasks", []):
        if task.get("task_type") == "spoken_word_script":
            params = task["parameters"]
            params["prompt"] = norm(voice_over_prompt)
            creativity, agent_desc = infer_creativity_and_agent(voice_over_prompt)
            params["creativity"] = creativity
            params["agent_description"] = agent_desc
            break

    return job


def extract_rows(csv_path: str) -> List[str]:
    """Return list of voice over prompts (one per row), preserving duplicates and order."""
    prompts: List[str] = []
    with open(csv_path, newline="", encoding="utf-8-sig") as f:
        # Auto-detect delimiter
        sample = f.read(4096)
        f.seek(0)
        delim = ','
        try:
            sniff = csv.Sniffer().sniff(sample, delimiters=",;")
            delim = sniff.delimiter
        except Exception:
            if sample.count(';') > sample.count(','):
                delim = ';'
        reader = csv.DictReader(f, delimiter=delim)
        header = find_header(reader.fieldnames, "Voice Over Prompt")
        if not header:
            # Try alternate casing or field name fallback
            header = find_header(reader.fieldnames, "voice over prompt")
        if not header:
            raise KeyError("'Voice Over Prompt' column not found in CSV")
        for row in reader:
            prompts.append(norm(row.get(header, "")))
    return prompts


def main():
    parser = argparse.ArgumentParser(description="Generate spoken-word multilingual jobs JSON from CSV")
    parser.add_argument("--csv", required=True, help="Path to input CSV")
    args = parser.parse_args()

    # Generate output filename automatically
    csv_path = args.csv
    csv_dir = os.path.dirname(csv_path)
    csv_basename = os.path.basename(csv_path)
    if csv_basename.endswith('.csv'):
        output_basename = csv_basename[:-4] + '_vo_jobs.json'
    else:
        output_basename = csv_basename + '_vo_jobs.json'
    output_path = os.path.join(csv_dir, output_basename)

    rows = extract_rows(csv_path)
    if not rows:
        raise SystemExit("No 'Voice Over Prompt' entries found in CSV")

    jobs: List[Dict] = []
    for idx, voice_prompt in enumerate(rows, start=1):
        if not voice_prompt:
            voice_prompt = "Guide a brief meditation for calm presence and mindful breathing."
        job = build_job_from_template(voice_prompt, idx)
        jobs.append(job)

    out_obj = {
        "description": f"Auto-generated spoken-word multilingual workflows from CSV: {os.path.basename(csv_path)}",
        "jobs": jobs,
    }

    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(out_obj, f, indent=2, ensure_ascii=False)

    print(f"Wrote {len(jobs)} jobs to {output_path}")


if __name__ == "__main__":
    main()

