#!/bin/bash

# Parse command line arguments
FULL_STACK=false

while [[ $# -gt 0 ]]; do
  case $1 in
    --full)
      FULL_STACK=true
      shift
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Create test directory structure
mkdir -p test/{postgres,gcs,cloud-tasks,logs/{api,job-management,gui},temp}

# Determine which services to run
if [ "$FULL_STACK" = true ]; then
  echo "Starting full stack (all services)..."
  SERVICES=""
else
  echo "Starting basic services (api and gui only)..."
  SERVICES="api gui"
fi

# Build and run
docker compose build $SERVICES
docker compose up $SERVICES
