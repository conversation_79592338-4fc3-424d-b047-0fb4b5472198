variable "app_name" {
  description = "Friendly app name"
  type        = string
}

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "environment" {
  description = "The environment to deploy to"
  type        = string
}

variable "region" {
  description = "The region to deploy to"
  type        = string
}

variable "storage_bucket_force_destroy" {
  description = "Force destroy storage bucket (use with caution in production)"
  type        = bool
  default     = false
}

variable "assets_lifecycle_age_days" {
  description = "Number of days after which assets are deleted"
  type        = number
  default     = 90
}

variable "assets_coldline_age_days" {
  description = "Number of days after which assets move to coldline storage"
  type        = number
  default     = 30
}

variable "temp_lifecycle_age_days" {
  description = "Number of days after which temp files are deleted"
  type        = number
  default     = 7
}

variable "enable_versioning" {
  description = "Enable versioning on assets bucket"
  type        = bool
  default     = true
}

variable "cors_origins" {
  description = "List of allowed CORS origins"
  type        = list(string)
  default     = ["*"]
}

variable "cors_methods" {
  description = "List of allowed CORS methods"
  type        = list(string)
  default     = ["GET", "HEAD"]
}

variable "cors_response_headers" {
  description = "List of allowed CORS response headers"
  type        = list(string)
  default     = ["*"]
}

variable "cors_max_age_seconds" {
  description = "CORS max age in seconds"
  type        = number
  default     = 3600
}

variable "uniform_bucket_level_access" {
  description = "Enable uniform bucket-level access"
  type        = bool
  default     = true
}
