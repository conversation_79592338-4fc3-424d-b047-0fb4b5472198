# ============================================================================
# OUTPUTS FOR JOB MANAGEMENT STORAGE MODULE (SINGLE BUCKET)
# ============================================================================

output "storage_bucket_name" {
  description = "Name of the main storage bucket"
  value       = google_storage_bucket.job_management_storage.name
}

output "storage_bucket_url" {
  description = "URL of the main storage bucket"
  value       = google_storage_bucket.job_management_storage.url
}

output "storage_bucket_self_link" {
  description = "Self link of the storage bucket"
  value       = google_storage_bucket.job_management_storage.self_link
}

# Legacy outputs for backward compatibility (all point to same bucket with folder prefixes)
output "assets_bucket_name" {
  description = "Name of the storage bucket (assets folder) - DEPRECATED: Use storage_bucket_name"
  value       = google_storage_bucket.job_management_storage.name
}

output "temp_bucket_name" {
  description = "Name of the storage bucket (temp folder) - DEPRECATED: Use storage_bucket_name"
  value       = google_storage_bucket.job_management_storage.name
}

output "results_bucket_name" {
  description = "Name of the storage bucket (results folder) - DEPRECATED: Use storage_bucket_name"
  value       = google_storage_bucket.job_management_storage.name
}

# Folder structure documentation
output "folder_structure" {
  description = "Folder structure within the storage bucket"
  value = {
    assets_music   = "assets/music/"
    assets_images  = "assets/images/"
    temp_uploads   = "temp/uploads/"
    temp_processing = "temp/processing/"
    results        = "results/"
  }
}
