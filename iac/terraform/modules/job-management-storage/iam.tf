# ============================================================================
# IAM CONFIGURATION FOR JOB MANAGEMENT STORAGE (SINGLE BUCKET)
# ============================================================================

# Get project information for default service account
data "google_project" "project" {
  project_id = var.project_id
}

locals {
  default_sa = "${data.google_project.project.number}-<EMAIL>"
}

# Grant storage admin access to default service account for the main storage bucket
resource "google_storage_bucket_iam_member" "storage_bucket_admin" {
  bucket = google_storage_bucket.job_management_storage.name
  role   = "roles/storage.admin"
  member = "serviceAccount:${local.default_sa}"
}

# If a dedicated service account is provided, grant it access too
variable "service_account_email" {
  description = "Email of the service account to grant storage access to"
  type        = string
  default     = ""
}

resource "google_storage_bucket_iam_member" "storage_bucket_sa_admin" {
  count  = var.service_account_email != "" ? 1 : 0
  bucket = google_storage_bucket.job_management_storage.name
  role   = "roles/storage.admin"
  member = "serviceAccount:${var.service_account_email}"
}
