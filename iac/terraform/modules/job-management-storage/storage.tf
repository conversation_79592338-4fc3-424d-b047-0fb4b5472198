# ============================================================================
# CLOUD STORAGE BUCKET FOR JOB MANAGEMENT (SINGLE BUCKET WITH SUBFOLDERS)
# ============================================================================

# Single bucket with organized folder structure:
# - assets/music/{job_id}/ - Final music files
# - assets/images/{job_id}/ - Album covers and images
# - temp/uploads/{session_id}/ - User uploads
# - temp/processing/{job_id}/ - Intermediate processing files
# - results/{job_id}/ - Job metadata and results
resource "google_storage_bucket" "job_management_storage" {
  name          = "${var.app_name}-storage-${var.environment}-${var.project_id}"
  location      = var.region
  project       = var.project_id
  force_destroy = var.storage_bucket_force_destroy

  uniform_bucket_level_access = var.uniform_bucket_level_access

  versioning {
    enabled = var.enable_versioning
  }

  # Lifecycle rule for temporary files (7 days)
  lifecycle_rule {
    condition {
      age            = var.temp_lifecycle_age_days
      matches_prefix = ["temp/"]
    }
    action {
      type = "Delete"
    }
  }

  # Lifecycle rule for results (30 days)
  lifecycle_rule {
    condition {
      age            = 30
      matches_prefix = ["results/"]
    }
    action {
      type = "Delete"
    }
  }

  # Lifecycle rule to move assets to coldline storage
  lifecycle_rule {
    condition {
      age            = var.assets_coldline_age_days
      matches_prefix = ["assets/"]
    }
    action {
      type          = "SetStorageClass"
      storage_class = "COLDLINE"
    }
  }

  # Lifecycle rule to delete old assets
  lifecycle_rule {
    condition {
      age            = var.assets_lifecycle_age_days
      matches_prefix = ["assets/"]
    }
    action {
      type = "Delete"
    }
  }

  # Clean up incomplete multipart uploads
  lifecycle_rule {
    condition {
      age = 1
    }
    action {
      type = "AbortIncompleteMultipartUpload"
    }
  }

  # CORS configuration for web access
  cors {
    origin          = var.cors_origins
    method          = var.cors_methods
    response_header = var.cors_response_headers
    max_age_seconds = var.cors_max_age_seconds
  }

  labels = {
    environment = var.environment
    component   = "job-management"
    type        = "storage"
  }
}


