# ============================================================================
# OUTPUTS FOR JOB MANAGEMENT QUEUES MODULE
# ============================================================================

output "job_queue_name" {
  description = "Name of the job queue"
  value       = google_cloud_tasks_queue.job_queue.name
}

output "job_queue_id" {
  description = "ID of the job queue"
  value       = google_cloud_tasks_queue.job_queue.id
}

output "task_queue_name" {
  description = "Name of the task queue"
  value       = google_cloud_tasks_queue.task_queue.name
}

output "task_queue_id" {
  description = "ID of the task queue"
  value       = google_cloud_tasks_queue.task_queue.id
}

output "musicgpt_queue_name" {
  description = "Name of the MusicGPT rate-limited queue"
  value       = google_cloud_tasks_queue.musicgpt_queue.name
}

output "musicgpt_queue_id" {
  description = "ID of the MusicGPT rate-limited queue"
  value       = google_cloud_tasks_queue.musicgpt_queue.id
}

output "elevenlabs_queue_name" {
  description = "Name of the ElevenLabs queue"
  value       = google_cloud_tasks_queue.elevenlabs_queue.name
}

output "elevenlabs_queue_id" {
  description = "ID of the ElevenLabs queue"
  value       = google_cloud_tasks_queue.elevenlabs_queue.id
}


output "priority_queue_name" {
  description = "Name of the priority queue"
  value       = google_cloud_tasks_queue.priority_queue.name
}

output "priority_queue_id" {
  description = "ID of the priority queue"
  value       = google_cloud_tasks_queue.priority_queue.id
}

output "queue_location" {
  description = "Location of all queues"
  value       = var.region
}

output "all_queue_names" {
  description = "List of all queue names"
  value = [
    google_cloud_tasks_queue.job_queue.name,
    google_cloud_tasks_queue.task_queue.name,
    google_cloud_tasks_queue.musicgpt_queue.name,
    google_cloud_tasks_queue.elevenlabs_queue.name,
    google_cloud_tasks_queue.priority_queue.name
  ]
}

output "all_queue_ids" {
  description = "List of all queue IDs"
  value = [
    google_cloud_tasks_queue.job_queue.id,
    google_cloud_tasks_queue.task_queue.id,
    google_cloud_tasks_queue.musicgpt_queue.id,
    google_cloud_tasks_queue.elevenlabs_queue.id,
    google_cloud_tasks_queue.priority_queue.id
  ]
}
