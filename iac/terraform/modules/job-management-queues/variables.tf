variable "app_name" {
  description = "Friendly app name"
  type        = string
}

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "environment" {
  description = "The environment to deploy to"
  type        = string
}

variable "region" {
  description = "The region to deploy to"
  type        = string
}

# Job Queue Configuration (for high-level job orchestration)
variable "job_queue_max_dispatches_per_second" {
  description = "Maximum dispatches per second for job queue"
  type        = number
  default     = 10
}



variable "job_queue_max_concurrent_dispatches" {
  description = "Maximum concurrent dispatches for job queue"
  type        = number
  default     = 50
}

variable "job_queue_max_attempts" {
  description = "Maximum retry attempts for job queue"
  type        = number
  default     = 5
}

variable "job_queue_max_retry_duration" {
  description = "Maximum retry duration for job queue (in seconds)"
  type        = string
  default     = "3600s"
}

# Task Queue Configuration (for individual task processing)
variable "task_queue_max_dispatches_per_second" {
  description = "Maximum dispatches per second for task queue"
  type        = number
  default     = 50
}



variable "task_queue_max_concurrent_dispatches" {
  description = "Maximum concurrent dispatches for task queue"
  type        = number
  default     = 100
}

variable "task_queue_max_attempts" {
  description = "Maximum retry attempts for task queue"
  type        = number
  default     = 3
}

variable "task_queue_max_retry_duration" {
  description = "Maximum retry duration for task queue (in seconds)"
  type        = string
  default     = "1800s"
}

# MusicGPT Rate-Limited Queue Configuration (respects API limits)
variable "musicgpt_queue_max_dispatches_per_second" {
  description = "Maximum dispatches per second for MusicGPT queue (respects API limits)"
  type        = number
  default     = 20  # Conservative to respect 10 concurrent limit
}

variable "musicgpt_queue_max_concurrent_dispatches" {
  description = "Maximum concurrent dispatches for MusicGPT queue (API limit is 10)"
  type        = number
  default     = 10  # Matches MusicGPT API limit
}

variable "musicgpt_queue_max_attempts" {
  description = "Maximum retry attempts for MusicGPT queue"
  type        = number
  default     = 3
}

variable "musicgpt_queue_max_retry_duration" {
  description = "Maximum retry duration for MusicGPT queue (in seconds)"
  type        = string
  default     = "1800s"
}

# ElevenLabs Queue Configuration
variable "elevenlabs_queue_max_dispatches_per_second" {
  description = "Maximum dispatches per second for ElevenLabs queue"
  type        = number
  default     = 20
}

variable "elevenlabs_queue_max_concurrent_dispatches" {
  description = "Maximum concurrent dispatches for ElevenLabs queue"
  type        = number
  default     = 10
}

variable "elevenlabs_queue_max_attempts" {
  description = "Maximum retry attempts for ElevenLabs queue"
  type        = number
  default     = 3
}

variable "elevenlabs_queue_max_retry_duration" {
  description = "Maximum retry duration for ElevenLabs queue (in seconds)"
  type        = string
  default     = "1800s"
}


# Priority Queue Configuration (for urgent tasks)
variable "priority_queue_max_dispatches_per_second" {
  description = "Maximum dispatches per second for priority queue"
  type        = number
  default     = 50
}

variable "priority_queue_max_concurrent_dispatches" {
  description = "Maximum concurrent dispatches for priority queue"
  type        = number
  default     = 50
}

variable "priority_queue_max_attempts" {
  description = "Maximum retry attempts for priority queue"
  type        = number
  default     = 2
}

variable "priority_queue_max_retry_duration" {
  description = "Maximum retry duration for priority queue (in seconds)"
  type        = string
  default     = "300s"
}

# General Queue Configuration
variable "enable_stackdriver_logging" {
  description = "Enable Stackdriver logging for queues"
  type        = bool
  default     = true
}

variable "stackdriver_sampling_ratio" {
  description = "Sampling ratio for Stackdriver logging"
  type        = number
  default     = 1.0
}
