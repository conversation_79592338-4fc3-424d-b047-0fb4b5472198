# ============================================================================
# IAM CONFIGURATION FOR JOB MANAGEMENT QUEUES
# ============================================================================

# Note: This module only manages queue-specific IAM permissions.
# Service account IAM permissions are managed by the job-management-cloud-run module
# to avoid duplication and conflicts.

# Get project information for default service account
data "google_project" "project" {
  project_id = var.project_id
}

locals {
  default_sa = "${data.google_project.project.number}-<EMAIL>"
}

# Service account email variable (for reference only, IAM managed elsewhere)
variable "service_account_email" {
  description = "Email of the service account (IAM permissions managed by cloud-run module)"
  type        = string
  default     = ""
}

# Note: Service account token creator permissions are managed by the
# job-management-cloud-run module to avoid duplication.
