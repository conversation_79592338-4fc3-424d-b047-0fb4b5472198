# ============================================================================
# CLOUD TASKS QUEUES FOR JOB MANAGEMENT SYSTEM
# ============================================================================

# High-level job orchestration queue
resource "google_cloud_tasks_queue" "job_queue" {
  name     = "${var.app_name}-job-queue-${var.environment}"
  location = var.region
  project  = var.project_id

  rate_limits {
    max_dispatches_per_second = var.job_queue_max_dispatches_per_second
    max_concurrent_dispatches = var.job_queue_max_concurrent_dispatches
  }

  retry_config {
    max_attempts       = var.job_queue_max_attempts
    max_retry_duration = var.job_queue_max_retry_duration
    max_backoff       = "300s"
    min_backoff       = "5s"
    max_doublings     = 5
  }

  dynamic "stackdriver_logging_config" {
    for_each = var.enable_stackdriver_logging ? [1] : []
    content {
      sampling_ratio = var.stackdriver_sampling_ratio
    }
  }
}

# Individual task processing queue
resource "google_cloud_tasks_queue" "task_queue" {
  name     = "${var.app_name}-task-queue-${var.environment}"
  location = var.region
  project  = var.project_id

  rate_limits {
    max_dispatches_per_second = var.task_queue_max_dispatches_per_second
    max_concurrent_dispatches = var.task_queue_max_concurrent_dispatches
  }

  retry_config {
    max_attempts       = var.task_queue_max_attempts
    max_retry_duration = var.task_queue_max_retry_duration
    max_backoff       = "120s"
    min_backoff       = "2s"
    max_doublings     = 4
  }

  dynamic "stackdriver_logging_config" {
    for_each = var.enable_stackdriver_logging ? [1] : []
    content {
      sampling_ratio = var.stackdriver_sampling_ratio
    }
  }
}

# MusicGPT rate-limited queue (respects API constraints)
resource "google_cloud_tasks_queue" "musicgpt_queue" {
  name     = "${var.app_name}-musicgpt-queue-${var.environment}"
  location = var.region
  project  = var.project_id

  rate_limits {
    max_dispatches_per_second = var.musicgpt_queue_max_dispatches_per_second
    max_concurrent_dispatches = var.musicgpt_queue_max_concurrent_dispatches
  }

  retry_config {
    max_attempts       = var.musicgpt_queue_max_attempts
    max_retry_duration = var.musicgpt_queue_max_retry_duration
    max_backoff       = "180s"
    min_backoff       = "10s"
    max_doublings     = 3
  }

  dynamic "stackdriver_logging_config" {
    for_each = var.enable_stackdriver_logging ? [1] : []
    content {
      sampling_ratio = var.stackdriver_sampling_ratio
    }
  }
}

# ElevenLabs queue for voice generation/over tasks
resource "google_cloud_tasks_queue" "elevenlabs_queue" {
  name     = "${var.app_name}-elevenlabs-queue-${var.environment}"
  location = var.region
  project  = var.project_id

  rate_limits {
    max_dispatches_per_second = var.elevenlabs_queue_max_dispatches_per_second
    max_concurrent_dispatches = var.elevenlabs_queue_max_concurrent_dispatches
  }

  retry_config {
    max_attempts       = var.elevenlabs_queue_max_attempts
    max_retry_duration = var.elevenlabs_queue_max_retry_duration
    max_backoff       = "120s"
    min_backoff       = "2s"
    max_doublings     = 4
  }

  dynamic "stackdriver_logging_config" {
    for_each = var.enable_stackdriver_logging ? [1] : []
    content {
      sampling_ratio = var.stackdriver_sampling_ratio
    }
  }
}


# Priority queue for urgent tasks (e.g., cancellations, status updates)
resource "google_cloud_tasks_queue" "priority_queue" {
  name     = "${var.app_name}-priority-queue-${var.environment}"
  location = var.region
  project  = var.project_id

  rate_limits {
    max_dispatches_per_second = var.priority_queue_max_dispatches_per_second
    max_concurrent_dispatches = var.priority_queue_max_concurrent_dispatches
  }

  retry_config {
    max_attempts       = var.priority_queue_max_attempts
    max_retry_duration = var.priority_queue_max_retry_duration
    max_backoff       = "30s"
    min_backoff       = "1s"
    max_doublings     = 2
  }

  dynamic "stackdriver_logging_config" {
    for_each = var.enable_stackdriver_logging ? [1] : []
    content {
      sampling_ratio = var.stackdriver_sampling_ratio
    }
  }
}
