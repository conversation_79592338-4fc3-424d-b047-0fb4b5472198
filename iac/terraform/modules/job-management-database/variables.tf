variable "app_name" {
  description = "Friendly app name"
  type        = string
}

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "project_number" {
  description = "Google Cloud project number"
  type        = number
}

variable "environment" {
  description = "The environment to deploy to"
  type        = string
}

variable "region" {
  description = "The region to deploy to"
  type        = string
}

variable "db_password" {
  description = "Database password"
  type        = string
  sensitive   = true
}

variable "db_tier" {
  description = "Database tier (machine type)"
  type        = string
  default     = "db-custom-2-4096"
}

variable "db_disk_size" {
  description = "Database disk size in GB"
  type        = number
  default     = 100
}

variable "db_disk_autoresize_limit" {
  description = "Database disk autoresize limit in GB"
  type        = number
  default     = 500
}

variable "backup_retention_days" {
  description = "Number of days to retain backups"
  type        = number
  default     = 30
}

variable "backup_start_time" {
  description = "Backup start time in HH:MM format"
  type        = string
  default     = "03:00"
}

variable "max_connections" {
  description = "Maximum number of database connections"
  type        = number
  default     = 200
}

variable "authorized_networks" {
  description = "List of authorized networks for database access"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "deletion_protection" {
  description = "Enable deletion protection for the database"
  type        = bool
  default     = true
}

variable "shared_buffers" {
  description = "PostgreSQL shared_buffers setting in 8KB pages"
  type        = string
  default     = ""  # Will be calculated based on machine type if not provided
}

variable "effective_cache_size" {
  description = "PostgreSQL effective_cache_size setting in 8KB pages"
  type        = string
  default     = ""  # Will be calculated based on machine type if not provided
}

variable "availability_type" {
  description = "Database availability type (ZONAL or REGIONAL)"
  type        = string
  default     = "REGIONAL"
}
