# ============================================================================
# OUTPUTS FOR JOB MANAGEMENT DATABASE MODULE
# ============================================================================

output "database_instance_name" {
  description = "Name of the Cloud SQL database instance"
  value       = google_sql_database_instance.job_management_db.name
}

output "database_connection_name" {
  description = "Connection name for the Cloud SQL database instance"
  value       = google_sql_database_instance.job_management_db.connection_name
}

output "database_name" {
  description = "Name of the job management database"
  value       = google_sql_database.job_management_main.name
}

output "database_user" {
  description = "Database user name"
  value       = google_sql_user.job_management_user.name
}

output "database_private_ip" {
  description = "Private IP address of the database instance"
  value       = google_sql_database_instance.job_management_db.private_ip_address
}

output "database_public_ip" {
  description = "Public IP address of the database instance"
  value       = google_sql_database_instance.job_management_db.public_ip_address
}

output "database_url_secret_id" {
  description = "Secret Manager secret ID for database URL"
  value       = google_secret_manager_secret.job_db_url.secret_id
}

output "database_connection_secret_id" {
  description = "Secret Manager secret ID for database connection name"
  value       = google_secret_manager_secret.job_db_connection_name.secret_id
}

output "service_account_email" {
  description = "Email of the job management service account"
  value       = google_service_account.job_management_sa.email
}

output "service_account_id" {
  description = "ID of the job management service account"
  value       = google_service_account.job_management_sa.account_id
}

output "database_instance_self_link" {
  description = "Self link of the database instance"
  value       = google_sql_database_instance.job_management_db.self_link
}

output "database_memory_settings" {
  description = "Calculated memory settings for the database"
  value = {
    ram_mb                      = local.ram_mb
    shared_buffers              = local.calculated_shared_buffers
    effective_cache_size        = local.calculated_effective_cache_size
    machine_type               = var.db_tier
  }
}

# Additional outputs for backward compatibility
output "db_instance_name" {
  description = "The name of the Cloud SQL instance (alias for database_instance_name)"
  value       = google_sql_database_instance.job_management_db.name
}

output "db_connection_name" {
  description = "The connection name of the Cloud SQL instance (alias for database_connection_name)"
  value       = google_sql_database_instance.job_management_db.connection_name
}

output "db_public_ip_address" {
  description = "The public IP address of the Cloud SQL instance (alias for database_public_ip)"
  value       = google_sql_database_instance.job_management_db.public_ip_address
}
