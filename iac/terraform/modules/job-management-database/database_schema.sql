-- Music Generation Job Management System - Database Schema

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Core job management tables

-- Jobs table - main job records
CREATE TABLE IF NOT EXISTS jobs (
    job_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    job_type VARCHAR(20) NOT NULL CHECK (job_type IN ('workflow')),
    status VARCHAR(20) NOT NULL DEFAULT 'queued' CHECK (status IN ('queued', 'running', 'completed', 'failed', 'cancelled')),
    configuration JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    metadata JSONB DEFAULT '{}',
    client_ip INET,
    user_agent TEXT,
    priority INTEGER DEFAULT 5 CHECK (priority BETWEEN 1 AND 10),
    timeout_seconds INTEGER DEFAULT 3600,
    -- Workflow tracking fields
    current_task_type VARCHAR(50),
    current_task_index INTEGER,
    workflow_progress_percentage INTEGER DEFAULT 0
);

-- Workflows table - workflow definitions
CREATE TABLE IF NOT EXISTS workflows (
    workflow_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(job_id) ON DELETE CASCADE,
    name VARCHAR(255),
    stages JSONB NOT NULL DEFAULT '[]',
    current_stage_index INTEGER DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'queued' CHECK (status IN ('queued', 'running', 'completed', 'failed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workflow task executions - tracks execution of individual tasks within workflows
CREATE TABLE IF NOT EXISTS workflow_task_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(job_id) ON DELETE CASCADE,
    task_type VARCHAR(50) NOT NULL,
    task_index INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'queued' CHECK (status IN ('queued', 'running', 'completed', 'failed', 'cancelled')),
    parameters JSONB,
    result JSONB,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(job_id, task_index)
);

-- Tasks table - individual task records
CREATE TABLE IF NOT EXISTS tasks (
    task_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(job_id) ON DELETE CASCADE,
    workflow_id UUID REFERENCES workflows(workflow_id) ON DELETE CASCADE,
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'queued' CHECK (status IN ('queued', 'running', 'completed', 'failed', 'cancelled')),
    input_data JSONB DEFAULT '{}',
    output_data JSONB DEFAULT '{}',
    gcs_input_path TEXT,
    gcs_output_path TEXT,
    stage_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    progress_percentage REAL DEFAULT 0.0 CHECK (progress_percentage BETWEEN 0.0 AND 100.0),
    external_task_id VARCHAR(255) -- For MusicGPT task IDs, etc.
);

-- Task dependencies
CREATE TABLE IF NOT EXISTS task_dependencies (
    dependency_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID NOT NULL REFERENCES tasks(task_id) ON DELETE CASCADE,
    depends_on_task_id UUID NOT NULL REFERENCES tasks(task_id) ON DELETE CASCADE,
    dependency_type VARCHAR(50) NOT NULL DEFAULT 'completion_required',
    dependency_config JSONB DEFAULT '{}'
);

-- Assets table - generated files and metadata
CREATE TABLE IF NOT EXISTS assets (
    asset_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID NOT NULL REFERENCES tasks(task_id) ON DELETE CASCADE,
    job_id UUID NOT NULL REFERENCES jobs(job_id) ON DELETE CASCADE,
    asset_type VARCHAR(20) NOT NULL CHECK (asset_type IN ('audio', 'image', 'text', 'metadata')),
    file_name VARCHAR(255) NOT NULL,
    gcs_path TEXT NOT NULL,
    mime_type VARCHAR(100),
    file_size_bytes BIGINT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    checksum VARCHAR(64),
    is_public BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Job history for audit trail
CREATE TABLE IF NOT EXISTS job_history (
    history_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(job_id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL,
    old_status VARCHAR(20),
    new_status VARCHAR(20),
    event_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by VARCHAR(50) DEFAULT 'system'
);

-- General rate limiting table for various identifiers (IP, etc.)
CREATE TABLE IF NOT EXISTS rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    identifier VARCHAR(255) NOT NULL,
    limit_type VARCHAR(50) NOT NULL,
    window_start TIMESTAMP WITH TIME ZONE NOT NULL,
    window_end TIMESTAMP WITH TIME ZONE NOT NULL,
    request_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(identifier, limit_type, window_start)
);

-- MusicGPT usage tracking for rate limiting and scaling control
CREATE TABLE IF NOT EXISTS musicgpt_usage_tracker (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    endpoint_type VARCHAR(50) NOT NULL, -- 'conversions', 'conversion_status_check'
    period_type VARCHAR(10) NOT NULL,   -- 'minute'
    window_start TIMESTAMP WITH TIME ZONE NOT NULL,
    window_end TIMESTAMP WITH TIME ZONE NOT NULL,
    request_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Track active music generation requests for concurrent limiting (max 10)
CREATE TABLE IF NOT EXISTS active_music_generation (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_task_execution_id UUID NOT NULL UNIQUE REFERENCES workflow_task_executions(id) ON DELETE CASCADE,
    musicgpt_task_id VARCHAR(255), -- MusicGPT's task ID
    websocket_connection_id VARCHAR(255), -- WebSocket connection for real-time updates
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);


-- Track active ElevenLabs requests for concurrent limiting (max 10)
CREATE TABLE IF NOT EXISTS active_elevenlabs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_task_execution_id UUID NOT NULL UNIQUE REFERENCES workflow_task_executions(id) ON DELETE CASCADE,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Job progress tracking (Replacing Redis cache)
CREATE TABLE IF NOT EXISTS job_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(job_id) ON DELETE CASCADE,
    task_id UUID REFERENCES tasks(task_id) ON DELETE CASCADE,
    progress_data JSONB DEFAULT '{}',
    status_message TEXT,
    progress_percentage REAL DEFAULT 0.0 CHECK (progress_percentage BETWEEN 0.0 AND 100.0),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Simplified external API configuration (MusicGPT limits are hardcoded in application)
-- This table can store dynamic configuration if needed in the future
CREATE TABLE IF NOT EXISTS api_configuration (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service_name VARCHAR(50) NOT NULL, -- 'musicgpt', 'gemini', 'imagen'
    config_key VARCHAR(100) NOT NULL,  -- 'max_parallel', 'requests_per_minute'
    config_value INTEGER NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(service_name, config_key)
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Job indexes
CREATE INDEX IF NOT EXISTS idx_jobs_status ON jobs(status);
CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON jobs(created_at);
CREATE INDEX IF NOT EXISTS idx_jobs_client_ip ON jobs(client_ip);
CREATE INDEX IF NOT EXISTS idx_jobs_status_created ON jobs(status, created_at);

-- CRITICAL HIGH-LOAD INDEXES: Added for 1000 concurrent job handling
CREATE INDEX IF NOT EXISTS idx_jobs_client_ip_created ON jobs(client_ip, created_at);
CREATE INDEX IF NOT EXISTS idx_jobs_type_status_created ON jobs(job_type, status, created_at);

-- Task indexes
CREATE INDEX IF NOT EXISTS idx_tasks_job_id ON tasks(job_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_workflow_id ON tasks(workflow_id);
CREATE INDEX IF NOT EXISTS idx_tasks_task_type ON tasks(task_type);
CREATE INDEX IF NOT EXISTS idx_tasks_external_id ON tasks(external_task_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status_created ON tasks(status, created_at);

-- Workflow tracking indexes
CREATE INDEX IF NOT EXISTS idx_workflow_task_executions_job_id ON workflow_task_executions(job_id);
CREATE INDEX IF NOT EXISTS idx_workflow_task_executions_status ON workflow_task_executions(status);
CREATE INDEX IF NOT EXISTS idx_workflow_task_executions_task_index ON workflow_task_executions(job_id, task_index);

-- Asset indexes
CREATE INDEX IF NOT EXISTS idx_assets_job_id ON assets(job_id);
CREATE INDEX IF NOT EXISTS idx_assets_task_id ON assets(task_id);
CREATE INDEX IF NOT EXISTS idx_assets_type ON assets(asset_type);
CREATE INDEX IF NOT EXISTS idx_assets_expires ON assets(expires_at);



-- MusicGPT usage tracking indexes
CREATE INDEX IF NOT EXISTS idx_musicgpt_usage_lookup ON musicgpt_usage_tracker(endpoint_type, period_type, window_start);
CREATE INDEX IF NOT EXISTS idx_musicgpt_usage_cleanup ON musicgpt_usage_tracker(window_end);

-- Active music generation indexes

-- Active ElevenLabs indexes
CREATE INDEX IF NOT EXISTS idx_active_elevenlabs_workflow_task ON active_elevenlabs(workflow_task_execution_id);
CREATE INDEX IF NOT EXISTS idx_active_elevenlabs_status ON active_elevenlabs(is_active, started_at);

CREATE INDEX IF NOT EXISTS idx_active_music_workflow_task ON active_music_generation(workflow_task_execution_id);
CREATE INDEX IF NOT EXISTS idx_active_music_status ON active_music_generation(is_active, started_at);
CREATE INDEX IF NOT EXISTS idx_active_music_musicgpt_id ON active_music_generation(musicgpt_task_id);

-- Progress tracking indexes
CREATE INDEX IF NOT EXISTS idx_job_progress_job ON job_progress(job_id, updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_job_progress_task ON job_progress(task_id, updated_at DESC);

-- API configuration indexes
CREATE INDEX IF NOT EXISTS idx_api_config_service ON api_configuration(service_name, config_key);

-- CRITICAL HIGH-LOAD INDEXES: Rate limiting performance
CREATE INDEX IF NOT EXISTS idx_rate_limits_atomic_lookup ON rate_limits(identifier, limit_type, window_start);
CREATE INDEX IF NOT EXISTS idx_rate_limits_cleanup ON rate_limits(window_end);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- Update timestamps automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to relevant tables
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_jobs_updated_at') THEN
        CREATE TRIGGER update_jobs_updated_at BEFORE UPDATE ON jobs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_workflows_updated_at') THEN
        CREATE TRIGGER update_workflows_updated_at BEFORE UPDATE ON workflows FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_tasks_updated_at') THEN
        CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_musicgpt_usage_updated_at') THEN
        CREATE TRIGGER update_musicgpt_usage_updated_at BEFORE UPDATE ON musicgpt_usage_tracker FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_workflow_task_executions_updated_at') THEN
        CREATE TRIGGER update_workflow_task_executions_updated_at BEFORE UPDATE ON workflow_task_executions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_active_music_generation_updated_at') THEN
        CREATE TRIGGER update_active_music_generation_updated_at BEFORE UPDATE ON active_music_generation FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END;
$$;

-- Job history trigger
CREATE OR REPLACE FUNCTION log_job_status_change()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO job_history (job_id, event_type, old_status, new_status, event_data)
        VALUES (NEW.job_id, 'status_change', OLD.status, NEW.status,
                jsonb_build_object('changed_at', NOW(), 'changed_by', 'system'));
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER log_job_status_changes AFTER UPDATE ON jobs FOR EACH ROW EXECUTE FUNCTION log_job_status_change();

-- ============================================================================
-- INITIAL DATA AND CONFIGURATION
-- ============================================================================

-- Insert MusicGPT configuration based on official documentation
INSERT INTO api_configuration (service_name, config_key, config_value) VALUES
('musicgpt', 'max_parallel_musicai', 10),           -- Maximum 10 parallel audio creation requests
('musicgpt', 'conversion_status_checks_per_minute', 200), -- Maximum 200 GetConversionById calls per minute
('musicgpt', 'conversions_per_minute', 60),         -- Maximum 60 conversions per minute
('gemini', 'requests_per_minute', 60),              -- Generous limit for Gemini
('imagen', 'requests_per_minute', 30)               -- Generous limit for Imagen
ON CONFLICT (service_name, config_key) DO NOTHING;

-- ============================================================================
-- CLEANUP FUNCTIONS
-- ============================================================================



-- Function to clean up old MusicGPT usage records
CREATE OR REPLACE FUNCTION cleanup_old_musicgpt_usage()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM musicgpt_usage_tracker WHERE window_end < NOW() - INTERVAL '2 hours';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up stale active music generation records
CREATE OR REPLACE FUNCTION cleanup_stale_music_generation()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Mark as inactive if started more than 1 hour ago (likely stale)
    UPDATE active_music_generation
    SET is_active = FALSE
    WHERE started_at < NOW() - INTERVAL '1 hour' AND is_active = TRUE;

    -- Delete very old inactive records
    DELETE FROM active_music_generation
    WHERE started_at < NOW() - INTERVAL '24 hours' AND is_active = FALSE;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old progress data
CREATE OR REPLACE FUNCTION cleanup_old_progress()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM job_progress WHERE updated_at < NOW() - INTERVAL '7 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- PERFORMANCE OPTIMIZATION SETTINGS
-- ============================================================================

-- Recommended PostgreSQL configuration for this workload:
-- shared_buffers = 1GB
-- effective_cache_size = 3GB
-- work_mem = 16MB
-- maintenance_work_mem = 256MB
-- max_connections = 200
-- random_page_cost = 1.1
-- effective_io_concurrency = 200
