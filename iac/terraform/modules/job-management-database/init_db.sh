#!/bin/bash
set -euo pipefail

# Database initialization script for Cloud SQL
# This script is designed to run in GitHub Actions with proper error handling

echo "Initializing database schema..."

# Function to cleanup on exit
cleanup() {
    if [[ -n "${PROXY_PID:-}" ]]; then
        echo "Cleaning up Cloud SQL Proxy (PID: $PROXY_PID)..."
        kill $PROXY_PID 2>/dev/null || true
        wait $PROXY_PID 2>/dev/null || true
    fi
    if [[ -f "cloud-sql-proxy" ]]; then
        rm -f cloud-sql-proxy
    fi
}
trap cleanup EXIT

# Download Cloud SQL Proxy
echo "Downloading Cloud SQL Proxy..."
curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.14.0/cloud-sql-proxy.linux.amd64
chmod +x cloud-sql-proxy

# Start Cloud SQL Proxy
echo "Starting Cloud SQL Proxy..."
CONNECTION_NAME="${PROJECT_ID}:${REGION}:${DB_INSTANCE_NAME}"
./cloud-sql-proxy $CONNECTION_NAME --port=5432 &
PROXY_PID=$!

# Wait for proxy to be ready
echo "Waiting for Cloud SQL Proxy to be ready..."
for i in {1..30}; do
    if nc -z 127.0.0.1 5432 2>/dev/null; then
        echo "Cloud SQL Proxy is ready!"
        break
    fi
    if [[ $i -eq 30 ]]; then
        echo "ERROR: Cloud SQL Proxy failed to start within 30 seconds"
        exit 1
    fi
    echo "Waiting... ($i/30)"
    sleep 1
done

# Test database connection
echo "Testing database connection..."
PGPASSWORD="${DB_PASSWORD}" psql \
    -h 127.0.0.1 \
    -p 5432 \
    -U "${DB_USER}" \
    -d "${DB_NAME}" \
    -c "SELECT version();" \
    --quiet

if [[ $? -ne 0 ]]; then
    echo "ERROR: Failed to connect to database"
    exit 1
fi

echo "Database connection successful!"

# Check if schema is already initialized
echo "Checking if database schema is already initialized..."
TABLES_COUNT=$(PGPASSWORD="${DB_PASSWORD}" psql \
    -h 127.0.0.1 \
    -p 5432 \
    -U "${DB_USER}" \
    -d "${DB_NAME}" \
    -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';" \
    --quiet | tr -d ' ')

echo "Found $TABLES_COUNT existing tables"

if [[ "$TABLES_COUNT" -gt 0 ]]; then
    echo "Database schema appears to be already initialized. Skipping initialization."
    echo "If you need to reinitialize, please drop the existing tables first."
    exit 0
fi

# Initialize database schema
echo "Initializing database schema..."
SCHEMA_FILE="${SCHEMA_FILE_PATH:-/tmp/database_schema.sql}"

if [[ ! -f "$SCHEMA_FILE" ]]; then
    echo "ERROR: Schema file not found at $SCHEMA_FILE"
    exit 1
fi

echo "Executing schema file: $SCHEMA_FILE"
PGPASSWORD="${DB_PASSWORD}" psql \
    -h 127.0.0.1 \
    -p 5432 \
    -U "${DB_USER}" \
    -d "${DB_NAME}" \
    -f "$SCHEMA_FILE" \
    --set ON_ERROR_STOP=1 \
    -v ON_ERROR_STOP=1 \
    --echo-errors

if [[ $? -eq 0 ]]; then
    echo "✅ Database schema initialization completed successfully!"
    
    # Verify initialization
    FINAL_TABLES_COUNT=$(PGPASSWORD="${DB_PASSWORD}" psql \
        -h 127.0.0.1 \
        -p 5432 \
        -U "${DB_USER}" \
        -d "${DB_NAME}" \
        -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';" \
        --quiet | tr -d ' ')
    
    echo "Database now contains $FINAL_TABLES_COUNT tables"
else
    echo "❌ Database schema initialization failed!"
    exit 1
fi

echo "Database initialization complete"
