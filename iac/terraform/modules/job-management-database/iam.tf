# ============================================================================
# IAM CONFIGURATION FOR JOB MANAGEMENT DATABASE
# ============================================================================

# Create a dedicated service account for job management
resource "google_service_account" "job_management_sa" {
  account_id   = "${var.app_name}-job-mgmt-${var.environment}"
  display_name = "Job Management Service Account (${var.environment})"
  description  = "Service account for job management system in ${var.environment} environment"
  project      = var.project_id
}

# Grant Cloud SQL client access to the service account
resource "google_project_iam_member" "job_management_sql_client" {
  project = var.project_id
  role    = "roles/cloudsql.client"
  member  = "serviceAccount:${google_service_account.job_management_sa.email}"
}

# Grant secret accessor role for database secrets
resource "google_secret_manager_secret_iam_member" "job_db_url_accessor" {
  secret_id = google_secret_manager_secret.job_db_url.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.job_management_sa.email}"
  project   = var.project_id
}

resource "google_secret_manager_secret_iam_member" "job_db_connection_accessor" {
  secret_id = google_secret_manager_secret.job_db_connection_name.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.job_management_sa.email}"
  project   = var.project_id
}


