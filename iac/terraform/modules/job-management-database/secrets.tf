# ============================================================================
# SECRETS MANAGEMENT FOR JOB MANAGEMENT DATABASE
# ============================================================================

resource "google_secret_manager_secret" "job_db_url" {
  secret_id = "${var.app_name}-job-db-url-${var.environment}"
  project   = var.project_id

  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }

  labels = {
    environment = var.environment
    component   = "job-management"
    type        = "database"
  }
}

resource "google_secret_manager_secret_version" "job_db_url_version" {
  secret      = google_secret_manager_secret.job_db_url.id
  # Use public IP + standard Postgres port for direct TCP connections from Cloud Run
  secret_data = "postgresql://${google_sql_user.job_management_user.name}:${var.db_password}@${google_sql_database_instance.job_management_db.public_ip_address}:5432/${google_sql_database.job_management_main.name}?sslmode=require"
}

# Additional secret for connection name (useful for Cloud SQL Proxy)
resource "google_secret_manager_secret" "job_db_connection_name" {
  secret_id = "${var.app_name}-job-db-connection-${var.environment}"
  project   = var.project_id

  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }

  labels = {
    environment = var.environment
    component   = "job-management"
    type        = "database"
  }
}

resource "google_secret_manager_secret_version" "job_db_connection_name_version" {
  secret      = google_secret_manager_secret.job_db_connection_name.id
  secret_data = google_sql_database_instance.job_management_db.connection_name
}
