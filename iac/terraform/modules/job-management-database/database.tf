# Cloud SQL PostgreSQL instance for job management database

# Calculate memory settings based on machine type
locals {
  # Extract RAM size from db_tier (e.g., "db-custom-2-4096" -> 4096)
  ram_mb = tonumber(split("-", var.db_tier)[3])

  # Calculate shared_buffers (25% of RAM, in 8KB pages)
  # For 4GB RAM: 1024MB = 131072 pages
  calculated_shared_buffers = var.shared_buffers != "" ? var.shared_buffers : tostring(floor(local.ram_mb * 0.25 * 128))

  # Calculate effective_cache_size (75% of RAM, in 8KB pages, but within GCP limits)
  # For 4GB RAM: max ~2.8GB = ~367000 pages (GCP limit is 367001)
  calculated_effective_cache_size = var.effective_cache_size != "" ? var.effective_cache_size : tostring(min(floor(local.ram_mb * 0.75 * 128), 367000))
}

resource "google_sql_database_instance" "job_management_db" {
  name             = "${var.app_name}-job-db-${var.environment}"
  database_version = "POSTGRES_15"
  region           = var.region
  project          = var.project_id

  settings {
    tier                        = var.db_tier
    availability_type          = var.availability_type
    disk_type                  = "PD_SSD"
    disk_size                  = var.db_disk_size
    disk_autoresize           = true
    disk_autoresize_limit     = var.db_disk_autoresize_limit

    backup_configuration {
      enabled                        = true
      start_time                    = var.backup_start_time
      location                      = var.region
      point_in_time_recovery_enabled = true
      transaction_log_retention_days = 7
      backup_retention_settings {
        retained_backups = var.backup_retention_days
        retention_unit   = "COUNT"
      }
    }

    ip_configuration {
      ipv4_enabled = true
      ssl_mode     = "ENCRYPTED_ONLY"

      # Only add authorized networks if provided
      dynamic "authorized_networks" {
        for_each = var.authorized_networks
        content {
          name  = authorized_networks.value.name
          value = authorized_networks.value.value
        }
      }
    }

    database_flags {
      name  = "max_connections"
      value = var.max_connections
    }

    database_flags {
      name  = "shared_buffers"
      value = local.calculated_shared_buffers
    }

    database_flags {
      name  = "effective_cache_size"
      value = local.calculated_effective_cache_size
    }

    database_flags {
      name  = "work_mem"
      value = "4096"
    }

    database_flags {
      name  = "maintenance_work_mem"
      value = "65536"
    }

    database_flags {
      name  = "checkpoint_completion_target"
      value = "0.9"
    }

    database_flags {
      name  = "wal_buffers"
      value = "16384"
    }

    database_flags {
      name  = "default_statistics_target"
      value = "100"
    }
  }

  deletion_protection = var.deletion_protection
}

# Main database for job management
resource "google_sql_database" "job_management_main" {
  name     = "job_management"
  instance = google_sql_database_instance.job_management_db.name
  project  = var.project_id
}

# Application user for job management
resource "google_sql_user" "job_management_user" {
  name     = "job_mgmt_app"
  instance = google_sql_database_instance.job_management_db.name
  password = var.db_password
  project  = var.project_id
}

# Initialize database schema using robust initialization script
resource "null_resource" "initialize_schema" {
  depends_on = [
    google_sql_database.job_management_main,
    google_sql_user.job_management_user
  ]

  provisioner "local-exec" {
    command = "bash ${path.module}/init_db.sh"

    environment = {
      PROJECT_ID        = var.project_id
      REGION           = var.region
      DB_INSTANCE_NAME = google_sql_database_instance.job_management_db.name
      DB_NAME          = google_sql_database.job_management_main.name
      DB_USER          = google_sql_user.job_management_user.name
      DB_PASSWORD      = var.db_password
      SCHEMA_FILE_PATH = "${path.module}/database_schema.sql"
    }
  }

  # Re-run if schema file or init script changes
  triggers = {
    schema_hash     = filemd5("${path.module}/database_schema.sql")
    init_script_hash = filemd5("${path.module}/init_db.sh")
    db_instance     = google_sql_database_instance.job_management_db.name
    db_name         = google_sql_database.job_management_main.name
  }
}