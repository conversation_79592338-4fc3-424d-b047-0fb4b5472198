variable "project_id" {
  description = "Google Cloud project ID where the Artifact Registry will be created"
  type        = string
}

variable "environment" {
  description = "The environment to deploy to"
  type        = string
}

variable "registries" {
  description = "Map of Artifact Registry configurations. Key is the registry name, value contains region."
  type = map(object({
    region = string
  }))
}
