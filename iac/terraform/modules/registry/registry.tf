module "docker_artifact_registry" {
  source     = "github.com/GoogleCloudPlatform/cloud-foundation-fabric//modules/artifact-registry?ref=v39.1.0"
  for_each   = var.registries
  project_id = var.project_id
  location   = each.value.region
  name       = "${each.key}-${var.environment}"

  # Default cleanup policy for all registries
  cleanup_policies = {
    keep-99-versions = {
      action = "KEEP"
      most_recent_versions = {
        keep_count = 50
      }
    }
  }

  format = {
    docker = {
      standard = {
        immutable_tags = false
      }
    }
  }
}
