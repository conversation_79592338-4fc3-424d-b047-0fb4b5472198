# ============================================================================
# OUTPUTS FOR JOB MANAGEMENT CLOUD RUN MODULE
# ============================================================================

output "service_name" {
  description = "Name of the Cloud Run service"
  value       = google_cloud_run_v2_service.job_management_service.name
}

output "service_url" {
  description = "URL of the Cloud Run service"
  value       = google_cloud_run_v2_service.job_management_service.uri
}

output "service_id" {
  description = "ID of the Cloud Run service"
  value       = google_cloud_run_v2_service.job_management_service.id
}

output "service_location" {
  description = "Location of the Cloud Run service"
  value       = google_cloud_run_v2_service.job_management_service.location
}

output "service_account_email" {
  description = "Email of the service account used by the Cloud Run service"
  value       = local.service_account
}
