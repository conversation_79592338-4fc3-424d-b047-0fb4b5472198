# ============================================================================
# IAM CONFIGURATION FOR JOB MANAGEMENT CLOUD RUN
# ============================================================================



# Allow unauthenticated access if specified
resource "google_cloud_run_v2_service_iam_member" "noauth" {
  count    = var.allow_unauthenticated ? 1 : 0
  location = google_cloud_run_v2_service.job_management_service.location
  name     = google_cloud_run_v2_service.job_management_service.name
  project  = var.project_id
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# Grant secret accessor role to the service account
resource "google_project_iam_member" "cloud_run_secret_accessor" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${local.service_account}"
}

# Grant Cloud SQL client access
resource "google_project_iam_member" "cloud_run_sql_client" {
  project = var.project_id
  role    = "roles/cloudsql.client"
  member  = "serviceAccount:${local.service_account}"
}

# Grant Cloud Storage admin access
resource "google_project_iam_member" "cloud_run_storage_admin" {
  project = var.project_id
  role    = "roles/storage.admin"
  member  = "serviceAccount:${local.service_account}"
}

# Grant Cloud Tasks enqueuer access
resource "google_project_iam_member" "cloud_run_tasks_enqueuer" {
  project = var.project_id
  role    = "roles/cloudtasks.enqueuer"
  member  = "serviceAccount:${local.service_account}"
}

# Grant Cloud Tasks task runner access
resource "google_project_iam_member" "cloud_run_tasks_runner" {
  project = var.project_id
  role    = "roles/cloudtasks.taskRunner"
  member  = "serviceAccount:${local.service_account}"
}

# Grant Cloud Tasks admin access for queue management
resource "google_project_iam_member" "cloud_run_tasks_admin" {
  project = var.project_id
  role    = "roles/cloudtasks.admin"
  member  = "serviceAccount:${local.service_account}"
}

# Grant logging write access
resource "google_project_iam_member" "cloud_run_logging_writer" {
  project = var.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${local.service_account}"
}

# Grant monitoring metric writer access
resource "google_project_iam_member" "cloud_run_monitoring_writer" {
  project = var.project_id
  role    = "roles/monitoring.metricWriter"
  member  = "serviceAccount:${local.service_account}"
}

# Allow Cloud Tasks to invoke this Cloud Run service via OIDC (using service account)
resource "google_cloud_run_v2_service_iam_member" "invoker_sa" {
  location = google_cloud_run_v2_service.job_management_service.location
  name     = google_cloud_run_v2_service.job_management_service.name
  project  = var.project_id
  role     = "roles/run.invoker"
  member   = "serviceAccount:${local.service_account}"
}

# Grant the service account permission to act as itself for OIDC token creation
# This is required for Cloud Tasks to create OIDC tokens using this service account
resource "google_service_account_iam_member" "service_account_token_creator" {
  service_account_id = "projects/${var.project_id}/serviceAccounts/${local.service_account}"
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${local.service_account}"
}

# Grant the service account permission to act as itself (includes iam.serviceAccounts.actAs)
# This is required for the service account to impersonate itself for OIDC token creation
resource "google_service_account_iam_member" "service_account_user" {
  service_account_id = "projects/${var.project_id}/serviceAccounts/${local.service_account}"
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${local.service_account}"
}
