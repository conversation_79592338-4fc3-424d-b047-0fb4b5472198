# ============================================================================
# CLOUD RUN SERVICE FOR JOB MANAGEMENT
# ============================================================================

locals {
  service_account = var.service_account_email != "" ? var.service_account_email : "${var.project_number}-<EMAIL>"
  
  # Combine all environment variables
  all_env_vars = merge(
    var.plain_env_vars,
    {
      ENVIRONMENT                = var.environment
      CLOUD_TASKS_LOCATION      = var.region
      GCS_STORAGE_BUCKET        = var.storage_bucket_name
      GCS_ASSETS_PREFIX         = "assets/"
      GCS_TEMP_PREFIX           = "temp/"
      GCS_RESULTS_PREFIX        = "results/"
      JOB_QUEUE_NAME            = var.queue_names.job_queue
      TASK_QUEUE_NAME           = var.queue_names.task_queue
      MUSICGPT_QUEUE_NAME       = var.queue_names.musicgpt_queue
      ELEVENLABS_QUEUE_NAME     = var.queue_names.elevenlabs_queue
      PRIORITY_QUEUE_NAME       = var.queue_names.priority_queue
      # Public base URL for this Cloud Run service (used by Cloud Tasks callbacks)
      JOB_MANAGEMENT_BASE_URL   = "https://${var.app_name}-job-mgmt-${var.environment}-${var.project_number}.${var.region}.run.app"

      # HTTP Client Timeout Configuration
      HTTP_CONNECTION_TIMEOUT   = tostring(var.http_connection_timeout)
      HTTP_READ_TIMEOUT        = tostring(var.http_read_timeout)
      HTTP_TOTAL_TIMEOUT       = tostring(var.http_total_timeout)
      HTTP_DOWNLOAD_TIMEOUT    = tostring(var.http_download_timeout)
      MUSIC_GEN_TIMEOUT        = tostring(var.music_gen_timeout)
    }
  )
}

resource "google_cloud_run_v2_service" "job_management_service" {
  name     = "${var.app_name}-job-mgmt-${var.environment}"
  location = var.region
  project  = var.project_id
  client   = "terraform"

  template {
    timeout = "${var.request_timeout_seconds}s"

    # Set per-instance container concurrency to avoid CPU thrash on long-running requests
    max_instance_request_concurrency = var.container_concurrency

    scaling {
      min_instance_count = var.min_instance_count
      max_instance_count = var.max_instance_count
    }

    service_account = local.service_account

    containers {
      name  = "${var.app_name}-job-mgmt"
      image = var.image

      ports {
        container_port = var.container_port
      }

      resources {
        limits = {
          cpu    = var.cpu_limit
          memory = var.memory_limit
        }
        cpu_idle = var.cpu_idle
      }

      # Plain environment variables
      dynamic "env" {
        for_each = local.all_env_vars
        content {
          name  = env.key
          value = env.value
        }
      }

      # Database URL from secret
      env {
        name = "DATABASE_URL"
        value_source {
          secret_key_ref {
            secret  = var.database_url_secret_id
            version = "latest"
          }
        }
      }

      # Secret-based environment variables (only include non-empty secrets)
      dynamic "env" {
        for_each = { for k, v in var.secret_env_vars : k => v if v != "" }
        content {
          name = env.key
          value_source {
            secret_key_ref {
              secret  = var.environment == "dev" ? env.key : google_secret_manager_secret.secret[env.key].secret_id
              version = "latest"
            }
          }
        }
      }
    }
  }

  traffic {
    percent = 100
    type    = "TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST"
  }

  depends_on = [
    google_project_iam_member.cloud_run_secret_accessor
  ]
}
