# ============================================================================
# SECRETS MANAGEMENT FOR JOB MANAGEMENT CLOUD RUN
# ============================================================================

# Create secrets for non-dev environments only
# In dev environment, secrets are expected to already exist
resource "google_secret_manager_secret" "secret" {
  for_each = var.environment == "dev" ? {} : { for k, v in var.secret_env_vars : k => v if v != "" }

  project   = var.project_id
  secret_id = each.key
  
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }

  labels = {
    environment = var.environment
    component   = "job-management"
    type        = "application"
  }
}

# Create secret versions for non-dev environments only
resource "google_secret_manager_secret_version" "version" {
  for_each = var.environment == "dev" ? {} : { for k, v in var.secret_env_vars : k => v if v != "" }

  secret      = google_secret_manager_secret.secret[each.key].id
  secret_data = each.value
}
