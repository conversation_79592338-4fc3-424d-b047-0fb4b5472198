variable "app_name" {
  description = "Friendly app name"
  type        = string
}

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "project_number" {
  description = "Google Cloud project number"
  type        = number
}

variable "environment" {
  description = "The environment to deploy to"
  type        = string
}

variable "region" {
  description = "The region to deploy to"
  type        = string
}

variable "image" {
  description = "Container image to deploy"
  type        = string
}

variable "container_port" {
  description = "Container port"
  type        = number
  default     = 8000
}

variable "container_concurrency" {
  description = "Maximum number of requests per instance (Cloud Run container concurrency)"
  type        = number
  default     = 10
}

variable "service_account_email" {
  description = "Service account email for the Cloud Run service"
  type        = string
  default     = ""
}

variable "cpu_limit" {
  description = "CPU limit for the container"
  type        = string
  default     = "2"
}

variable "memory_limit" {
  description = "Memory limit for the container"
  type        = string
  default     = "4Gi"
}

variable "min_instance_count" {
  description = "Minimum number of instances"
  type        = number
  default     = 1
}

variable "max_instance_count" {
  description = "Maximum number of instances"
  type        = number
  default     = 20
}

variable "request_timeout_seconds" {
  description = "Request timeout in seconds"
  type        = number
  default     = 3600
}

variable "cpu_idle" {
  description = "Enable CPU idle optimization"
  type        = bool
  default     = true
}

# HTTP Client Timeout Configuration
variable "http_connection_timeout" {
  description = "HTTP connection timeout in seconds"
  type        = number
  default     = 30
}

variable "http_read_timeout" {
  description = "HTTP read timeout in seconds"
  type        = number
  default     = 180
}

variable "http_total_timeout" {
  description = "HTTP total operation timeout in seconds"
  type        = number
  default     = 600
}

variable "http_download_timeout" {
  description = "HTTP download timeout in seconds"
  type        = number
  default     = 300
}

variable "music_gen_timeout" {
  description = "Music generation WebSocket timeout in seconds"
  type        = number
  default     = 180
}

variable "plain_env_vars" {
  description = "Map of plain environment variables"
  type        = map(string)
  default     = {}
}

variable "secret_env_vars" {
  description = "Map of secret environment variables with secret IDs"
  type        = map(string)
  default     = {}
}

variable "database_url_secret_id" {
  description = "Secret ID for database URL"
  type        = string
}

variable "storage_bucket_name" {
  description = "Name of the main storage bucket (single bucket with subfolders)"
  type        = string
}

variable "queue_names" {
  description = "Map of queue names"
  type = object({
    job_queue        = string
    task_queue       = string
    musicgpt_queue   = string
    elevenlabs_queue = string
    priority_queue   = string
  })
}

variable "allow_unauthenticated" {
  description = "Allow unauthenticated access to the service"
  type        = bool
  default     = true
}
