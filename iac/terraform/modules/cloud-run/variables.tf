variable "app_name" {
  description = "Friendly app name"
  type        = string
}

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "project_number" {
  description = "Google Cloud project number"
  type        = number
}


variable "environment" {
  description = "The environment to deploy to"
  type        = string
}

variable "region" {
  description = "The region to deploy to"
  type        = string
}

variable "image" {
  description = "Image to deploy"
  type        = string
}

variable "container_port" {
  description = "Container Port"
  type        = number
  default     = 3000
}

variable "secret_env_vars" {
  description = "Map of secret env vars where values are secret content"
  type        = map(string)
}

variable "request_timeout_seconds" {
  description = "Request timeout in seconds"
  type        = number
  default     = 300
}

variable "plain_env_vars" {
  description = "Map of plain env vars"
  type        = map(string)
}
