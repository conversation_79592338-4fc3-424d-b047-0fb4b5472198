variable "app_name" {
  description = "Friendly app name"
  type        = string
}

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "project_number" {
  description = "Google Cloud project number"
  type        = number
}


variable "environment" {
  description = "The environment to deploy to"
  type        = string
}

variable "region" {
  description = "The region to deploy to"
  type        = string
}

variable "image" {
  description = "Image to deploy"
  type        = string
}



variable "container_port" {
  description = "Container Port"
  type        = number
  default     = 3000
}

variable "secret_env_vars" {
  description = "Map of secret env vars where values are secret content"
  type        = map(string)
}

variable "request_timeout_seconds" {
  description = "Request timeout in seconds"
  type        = number
  default     = 300
}

variable "plain_env_vars" {
  description = "Map of plain env vars"
  type        = map(string)
}

# MusicGPT HTTP Client Timeout Configuration
variable "musicgpt_connection_timeout" {
  description = "MusicGPT HTTP connection timeout in seconds"
  type        = number
  default     = 30
}

variable "musicgpt_read_timeout" {
  description = "MusicGPT HTTP read timeout in seconds"
  type        = number
  default     = 180
}

variable "musicgpt_generation_timeout" {
  description = "MusicGPT generation request timeout in seconds"
  type        = number
  default     = 180
}

variable "musicgpt_status_timeout" {
  description = "MusicGPT status check timeout in seconds"
  type        = number
  default     = 30
}

# Resource Configuration
variable "cpu_limit" {
  description = "CPU limit for the container (e.g., '1000m' for 1 CPU)"
  type        = string
  default     = "2000m"
}

variable "memory_limit" {
  description = "Memory limit for the container (e.g., '512Mi', '1Gi')"
  type        = string
  default     = "4Gi"
}

variable "container_concurrency" {
  description = "Maximum number of concurrent requests per container"
  type        = number
  default     = 50
}
