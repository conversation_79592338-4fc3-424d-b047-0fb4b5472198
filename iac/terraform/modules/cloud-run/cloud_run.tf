resource "google_cloud_run_v2_service" "cloud_run" {
  name     = "${var.app_name}-${var.environment}"
  location = var.region
  client   = "terraform"
  project  = var.project_id

  template {
    timeout = "${var.request_timeout_seconds}s"
    containers {
      name  = var.app_name
      image = var.image
      ports {
        container_port = var.container_port
      }

      dynamic "env" {
        for_each = var.plain_env_vars
        content {
          name  = env.key
          value = env.value
        }
      }

      # Secret-based env vars
      dynamic "env" {
        for_each = var.secret_env_vars
        content {
          name = env.key
          value_source {
            secret_key_ref {
              secret  = google_secret_manager_secret.secret[env.key].secret_id
              version = "latest"
            }
          }
        }
      }
    }
  }
}
