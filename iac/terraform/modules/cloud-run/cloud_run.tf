resource "google_cloud_run_v2_service" "cloud_run" {
  name     = "${var.app_name}-${var.environment}"
  location = var.region
  client   = "terraform"
  project  = var.project_id

  template {
    timeout = "${var.request_timeout_seconds}s"
    max_instance_request_concurrency = var.container_concurrency
    containers {
      name  = var.app_name
      image = var.image
      resources {
        limits = {
          cpu    = var.cpu_limit
          memory = var.memory_limit
        }
      }
      ports {
        container_port = var.container_port
      }


      # Plain environment variables with timeout configuration
      dynamic "env" {
        for_each = merge(
          var.plain_env_vars,
          {
            # MusicGPT HTTP Client Timeout Configuration
            MUSICGPT_CONNECTION_TIMEOUT = tostring(var.musicgpt_connection_timeout)
            MUSICGPT_READ_TIMEOUT      = tostring(var.musicgpt_read_timeout)
            MUSICGPT_GENERATION_TIMEOUT = tostring(var.musicgpt_generation_timeout)
            MUSICGPT_STATUS_TIMEOUT    = tostring(var.musicgpt_status_timeout)
          }
        )
        content {
          name  = env.key
          value = env.value
        }
      }

      # Secret-based env vars
      dynamic "env" {
        for_each = var.secret_env_vars
        content {
          name = env.key
          value_source {
            secret_key_ref {
              secret  = var.environment == "dev" ? env.key : google_secret_manager_secret.secret[env.key].secret_id
              version = "latest"
            }
          }
        }
      }
    }
  }
}
