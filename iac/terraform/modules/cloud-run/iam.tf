locals {
  default_sa = "${var.project_number}-<EMAIL>"
}

resource "google_cloud_run_v2_service_iam_member" "noauth" {
  location = google_cloud_run_v2_service.cloud_run.location
  name     = google_cloud_run_v2_service.cloud_run.name
  project  = var.project_id
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# add secrets permissions to default SA used by cloud run
resource "google_project_iam_member" "cloud_run_secret_access" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${local.default_sa}"
}
