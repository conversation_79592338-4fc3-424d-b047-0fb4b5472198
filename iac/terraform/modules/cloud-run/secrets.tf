resource "google_secret_manager_secret" "secret" {
  for_each = var.environment == "dev" ? {} : var.secret_env_vars

  project   = var.project_id
  secret_id = each.key
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}

resource "google_secret_manager_secret_version" "version" {
  for_each = var.environment == "dev" ? {} : var.secret_env_vars

  secret      = google_secret_manager_secret.secret[each.key].id
  secret_data = each.value
}
