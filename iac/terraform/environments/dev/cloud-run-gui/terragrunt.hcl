include "root" {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = local.base_source_url
}

locals {
  env_vars        = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  base_source_url = "${get_repo_root()}/iac/terraform/modules/cloud-run"
}

dependency "registry" {
  config_path = "../registry"
}

dependency "cloud-run" {
  config_path = "../cloud-run"
}

inputs = {
  app_name       = "music-generation-gui"
  project_id     = local.env_vars.locals.project_id
  project_number = local.env_vars.locals.project_number
  environment    = local.env_vars.locals.env
  region         = local.env_vars.locals.region
  image = get_env(
    "MUSIC_GENERATION_GUI_IMAGE",
    "us-central1-docker.pkg.dev/ir-ai-dev/music-generation-gui-dev/music-generation-gui:latest"
  )
  container_port = 7860
  plain_env_vars = {
    MUSIC_GENERATION_WS_URL = replace(dependency.cloud-run.outputs.cloudrun_url, "http", "ws")
  }
  secret_env_vars = {
  }
  request_timeout_seconds = 600
}
