include "root" {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = local.base_source_url
}

locals {
  env_vars        = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  base_source_url = "${get_repo_root()}/iac/terraform/modules/cloud-run"
}

dependency "registry" {
  config_path = "../registry"
}

inputs = {
  app_name       = "music-generation"
  project_id     = local.env_vars.locals.project_id
  project_number = local.env_vars.locals.project_number
  environment    = local.env_vars.locals.env
  region         = local.env_vars.locals.region
  image = get_env(
    "MUSIC_GENERATION_IMAGE",
    "us-central1-docker.pkg.dev/ir-ai-dev/music-generation-dev/music-generation:latest"
  )
  container_port = 8000

  # Resource configuration for stability with ElevenLabs processing
  cpu_limit = "2000m"              # 2 CPU cores (increased from 1)
  memory_limit = "4Gi"             # 4GB memory (increased from 512Mi)
  container_concurrency = 10       # Reduced concurrency (was 80)

  plain_env_vars = {
    ENVIRONMENT      = local.env_vars.locals.env
  }
  secret_env_vars = {
    MUSIC_GPT_API_KEY  = get_env("MUSIC_GPT_API_KEY", "")
    GEMINI_API_KEY     = get_env("GEMINI_API_KEY", "")
    ELEVENLABS_API_KEY = get_env("ELEVENLABS_API_KEY", "")
    OPENAI_API_KEY     = get_env("OPENAI_API_KEY", "")
  }
  request_timeout_seconds = 600
}
