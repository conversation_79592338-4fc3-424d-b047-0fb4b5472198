include "root" {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = local.base_source_url
}

locals {
  env_vars        = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  base_source_url = "${get_repo_root()}/iac/terraform/modules/registry"
}

inputs = {
  project_id  = local.env_vars.locals.project_id
  environment = local.env_vars.locals.env
  registries = {
    "music-generation" = {
      region = local.env_vars.locals.region
    }
    "music-generation-gui" = {
      region = local.env_vars.locals.region
    }
    "music-generation-job-mgmt" = {
      region = local.env_vars.locals.region
    }
  }
}
