include "root" {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = local.base_source_url
}

locals {
  env_vars        = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  base_source_url = "${get_repo_root()}/iac/terraform/modules/job-management-queues"
}

dependency "database" {
  config_path = "../job-management-database"
}

inputs = {
  app_name    = "music-generation"
  project_id  = local.env_vars.locals.project_id
  environment = local.env_vars.locals.env
  region      = local.env_vars.locals.region
  
  # Job Queue Configuration (high-level orchestration)
  # Jobs are mainly music generation (2min avg) + fast tasks (<10s)
  # Higher dispatch rate since jobs coordinate multiple tasks
  job_queue_max_dispatches_per_second   = 20
  job_queue_max_concurrent_dispatches  = 100
  job_queue_max_attempts               = 5
  job_queue_max_retry_duration         = "3600s"

  # Task Queue Configuration (individual task processing)
  # Fast tasks like prompt enhancement and image generation (<10s each)
  # High throughput needed for quick task processing
  task_queue_max_dispatches_per_second  = 100
  task_queue_max_concurrent_dispatches = 200
  task_queue_max_attempts              = 3
  task_queue_max_retry_duration        = "600s"  # Reduced from 30min to 10min for fast tasks

  # MusicGPT Queue Configuration (respects API limits)
  musicgpt_queue_max_dispatches_per_second  = 20   # API limited
  musicgpt_queue_max_concurrent_dispatches  = 10  # API limited
  musicgpt_queue_max_attempts               = 3
  musicgpt_queue_max_retry_duration         = "1800s"

  # Priority Queue Configuration (urgent tasks)
  priority_queue_max_dispatches_per_second  = 50
  priority_queue_max_concurrent_dispatches  = 50
  priority_queue_max_attempts               = 2
  priority_queue_max_retry_duration         = "300s"

  # Logging configuration
  enable_stackdriver_logging = true
  stackdriver_sampling_ratio = 1.0
  
  # Priority Queue Configuration (urgent tasks)
  priority_queue_max_dispatches_per_second  = 50
  priority_queue_max_concurrent_dispatches  = 50
  priority_queue_max_attempts               = 2
  priority_queue_max_retry_duration         = "300s"

  # ElevenLabs Queue Configuration
  elevenlabs_queue_max_dispatches_per_second  = 2
  elevenlabs_queue_max_concurrent_dispatches  = 2
  elevenlabs_queue_max_attempts               = 3
  elevenlabs_queue_max_retry_duration         = "1800s"

  # Service account for access
  service_account_email = dependency.database.outputs.service_account_email
}
