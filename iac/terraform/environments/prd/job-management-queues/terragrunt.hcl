include "root" {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = local.base_source_url
}

locals {
  env_vars        = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  base_source_url = "${get_repo_root()}/iac/terraform/modules/job-management-queues"
}

dependency "database" {
  config_path = "../job-management-database"
}

inputs = {
  app_name    = "music-generation"
  project_id  = local.env_vars.locals.project_id
  environment = local.env_vars.locals.env
  region      = local.env_vars.locals.region

  # Production Queue Configuration - Higher limits for production workload
  # Job Queue Configuration (high-level orchestration)
  job_queue_max_dispatches_per_second   = 30   # Higher than dev for production load
  job_queue_max_concurrent_dispatches  = 150   # Higher than dev
  job_queue_max_attempts               = 5
  job_queue_max_retry_duration         = "3600s"

  # Task Queue Configuration (individual task processing)
  task_queue_max_dispatches_per_second  = 150  # Higher than dev
  task_queue_max_concurrent_dispatches = 300   # Higher than dev
  task_queue_max_attempts              = 3
  task_queue_max_retry_duration        = "600s"

  # MusicGPT Queue Configuration (respects API limits)
  musicgpt_queue_max_dispatches_per_second  = 10  # Saturate 10 parallel limit
  musicgpt_queue_max_concurrent_dispatches  = 10  # Match slot limit
  musicgpt_queue_max_attempts               = 5   # More resilient for batch spikes
  musicgpt_queue_max_retry_duration         = "3600s"  # 1 hour window
  # ElevenLabs Queue Configuration (batch-ready)
  elevenlabs_queue_max_dispatches_per_second  = 2
  elevenlabs_queue_max_concurrent_dispatches  = 2
  elevenlabs_queue_max_attempts               = 5
  elevenlabs_queue_max_retry_duration         = "3600s"


  # Priority Queue Configuration (urgent tasks) - Higher limits for production
  priority_queue_max_dispatches_per_second  = 100  # Higher than dev
  priority_queue_max_concurrent_dispatches  = 100  # Higher than dev
  priority_queue_max_attempts               = 2
  priority_queue_max_retry_duration         = "300s"

  # Logging configuration
  enable_stackdriver_logging = true
  stackdriver_sampling_ratio = 0.1  # Reduced sampling in production

  # Service account for access
  service_account_email = dependency.database.outputs.service_account_email
}
