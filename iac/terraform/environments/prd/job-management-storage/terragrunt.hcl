include "root" {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = local.base_source_url
}

locals {
  env_vars        = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  base_source_url = "${get_repo_root()}/iac/terraform/modules/job-management-storage"
}

dependency "database" {
  config_path = "../job-management-database"
}

inputs = {
  app_name    = "music-generation"
  project_id  = local.env_vars.locals.project_id
  environment = local.env_vars.locals.env
  region      = local.env_vars.locals.region
  
  # Bucket configuration (single bucket with subfolders)
  storage_bucket_force_destroy = false  # Protect production storage
  
  # Lifecycle policies
  assets_lifecycle_age_days = 90   # Delete assets after 90 days
  assets_coldline_age_days  = 30   # Move to coldline after 30 days
  temp_lifecycle_age_days   = 7    # Delete temp files after 7 days
  
  # Versioning
  enable_versioning = true
  
  # CORS configuration
  cors_origins          = ["*"]  # TODO: Restrict in production
  cors_methods          = ["GET", "HEAD", "POST"]
  cors_response_headers = ["*"]
  cors_max_age_seconds  = 3600
  
  # Access control
  uniform_bucket_level_access = true
  
  # Service account for access
  service_account_email = dependency.database.outputs.service_account_email
}
