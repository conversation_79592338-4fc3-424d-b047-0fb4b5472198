remote_state {
  backend = "gcs"
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
  config = {
    bucket  = "ir-ai-dev-terraform-states"
    prefix  = "music-generation/prd/${path_relative_to_include()}" 
    project = "ir-ai-dev"
    skip_bucket_creation = true
  }
}

# For each module, pin the google provider versions and the terraform version
generate "providers" {
  path      = "providers.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
terraform {
  required_version = ">= 1.11.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.45.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 6.45.0"
    }
  }
}
EOF
}
