include "root" {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = local.base_source_url
}

locals {
  env_vars        = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  base_source_url = "${get_repo_root()}/iac/terraform/modules/job-management-database"
}

inputs = {
  app_name       = "music-generation"
  project_id     = local.env_vars.locals.project_id
  project_number = local.env_vars.locals.project_number
  environment    = local.env_vars.locals.env
  region         = local.env_vars.locals.region
  
  # Database configuration
  db_password = get_env("JOB_MANAGEMENT_DB_PASSWORD", "changeme123!")
  db_tier     = "db-custom-2-4096"  # 2 vCPU, 4GB RAM
  db_disk_size = 100
  db_disk_autoresize_limit = 500
  
  # Backup configuration
  backup_retention_days = 30
  backup_start_time     = "03:00"
  
  # Performance tuning
  max_connections = 200
  
  # Security configuration
  authorized_networks = [
    {
      name  = "cloud-run-access"
      value = "0.0.0.0/0"  # TODO: Restrict this in production
    }
  ]
  
  # Protection settings
  deletion_protection = true
  availability_type   = "REGIONAL"
}
