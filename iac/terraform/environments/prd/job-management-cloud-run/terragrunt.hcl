include "root" {
  path = find_in_parent_folders("root.hcl")
}

terraform {
  source = local.base_source_url
}

locals {
  env_vars        = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  base_source_url = "${get_repo_root()}/iac/terraform/modules/job-management-cloud-run"
}

dependency "database" {
  config_path = "../job-management-database"
}

dependency "storage" {
  config_path = "../job-management-storage"

  mock_outputs = {
    storage_bucket_name = "mock-storage-bucket-prd"
  }
}

dependency "queues" {
  config_path = "../job-management-queues"

  mock_outputs = {
    job_queue_name        = "mock-job-queue-prd"
    task_queue_name       = "mock-task-queue-prd"
    musicgpt_queue_name   = "mock-musicgpt-queue-prd"
    elevenlabs_queue_name = "mock-elevenlabs-queue-prd"
    priority_queue_name   = "mock-priority-queue-prd"
  }
}

dependency "registry" {
  config_path = "../registry"
}

dependency "cloud-run" {
  config_path = "../cloud-run"
}

inputs = {
  app_name       = "music-generation"
  project_id     = local.env_vars.locals.project_id
  project_number = local.env_vars.locals.project_number
  environment    = local.env_vars.locals.env
  region         = local.env_vars.locals.region

  # Container configuration
  image = get_env(
    "MUSIC_GENERATION_JOB_MGMT_IMAGE",
    "us-central1-docker.pkg.dev/ir-ai-dev/music-generation-prd/music-generation-job-mgmt:latest"
  )
  container_port = 8001
  # Instance concurrency tuning
  container_concurrency = 10


  # Resource configuration
  cpu_limit    = "2"
  memory_limit = "4Gi"
  cpu_idle     = true

  # Scaling configuration
  min_instance_count = 3
  max_instance_count = 30

  # Timeout configuration
  request_timeout_seconds = 900   # 15 minutes for job management

  # Service account
  service_account_email = dependency.database.outputs.service_account_email

  # Database configuration
  database_url_secret_id = dependency.database.outputs.database_url_secret_id

  # Storage configuration (single bucket with subfolders)
  storage_bucket_name = dependency.storage.outputs.storage_bucket_name

  # Queue configuration
  queue_names = {
    job_queue        = dependency.queues.outputs.job_queue_name
    task_queue       = dependency.queues.outputs.task_queue_name
    musicgpt_queue   = dependency.queues.outputs.musicgpt_queue_name
    elevenlabs_queue = dependency.queues.outputs.elevenlabs_queue_name
    priority_queue   = dependency.queues.outputs.priority_queue_name
  }

  # Plain environment variables
  plain_env_vars = {
    GOOGLE_CLOUD_PROJECT        = local.env_vars.locals.project_id
    GOOGLE_CLOUD_PROJECT_NUMBER = local.env_vars.locals.project_number
    DEBUG                       = "false"
    LOG_LEVEL                  = "INFO"
    DB_POOL_SIZE               = "20"
    DB_MAX_OVERFLOW            = "20"
    DB_POOL_TIMEOUT            = "30"
    DB_POOL_RECYCLE            = "3600"
    CLOUD_TASKS_DISPATCH_DEADLINE_SECONDS = "880"
    # API service URL for task execution
    API_BASE_URL               = dependency.cloud-run.outputs.cloudrun_url
    # Service account email for Cloud Tasks OIDC
    GOOGLE_CLOUD_RUN_TASK_SA   = dependency.database.outputs.service_account_email
    # Transcoder service URL
    TRANSCODER_BASE_URL        = "https://napster-ai-transcoder-************.us-central1.run.app"
  }

  # Secret environment variables
  secret_env_vars = {
    MUSIC_GPT_API_KEY  = get_env("MUSIC_GPT_API_KEY", "")
    GEMINI_API_KEY     = get_env("GEMINI_API_KEY", "")
    ELEVENLABS_API_KEY = get_env("ELEVENLABS_API_KEY", "")
    OPENAI_API_KEY     = get_env("OPENAI_API_KEY", "")
  }

  # Access configuration
  allow_unauthenticated = true
}
