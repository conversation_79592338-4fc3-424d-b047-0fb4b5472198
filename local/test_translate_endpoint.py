import requests
import time


BASE_URL = "http://localhost:8000"
TRANSLATE_ENDPOINT = f"{BASE_URL}/translate-text"


def test_translation(text, target_language, source_language=None, context=None):
    """Test translation with given parameters."""
    payload = {
        "text": text,
        "target_language": target_language,
        "quality": "standard",
        "model_creativity": 0.2
    }

    if source_language:
        payload["source_language"] = source_language

    if context:
        payload["context"] = context

    print(f"\n{'='*60}")
    print(f"Testing translation to {target_language}")
    print(f"Source language: {source_language or 'None (should default to English)'}")
    print(f"Text: {text}")
    if context:
        print(f"Context: {context}")
    print(f"{'='*60}")

    try:
        response = requests.post(TRANSLATE_ENDPOINT, json=payload)

        if response.status_code == 200:
            result = response.json()
            print(f"✓ Translation successful!")
            print(f"  Original: {text}")
            print(f"  Translation: {result['translation']}")
            print(f"  Source Language: {result['source_language']}")
            print(f"  Target Language: {result['target_language']}")

            if result.get('confidence_score'):
                print(f"  Confidence Score: {result['confidence_score']}")

            if result.get('translation_notes'):
                print(f"  Notes: {result['translation_notes']}")

            if result.get('alternative_translations'):
                print(f"  Alternatives: {result['alternative_translations']}")

            return result
        else:
            print(f"✗ Translation failed with status {response.status_code}")
            print(f"  Error: {response.text}")
            return None

    except requests.exceptions.ConnectionError:
        print("✗ Connection failed - make sure the API server is running on localhost:8000")
        return None
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return None

def main():
    """Run translation tests."""
    print("Testing Translation Endpoint")
    print("============================")
    print("This script tests the updated translation service that:")
    print("- Defaults to English when no source language is specified")
    print("- No longer uses auto-detect functionality")
    print()

    # Sample texts for testing
    sample_texts = [
        {
            "text": "Hello, how are you today? I hope you're having a wonderful day!",
            "context": "Casual greeting"
        },
        {
            "text": "The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet.",
            "context": "Classic pangram sentence"
        },
        {
            "text": "Artificial intelligence is transforming the way we work and live. It has applications in healthcare, education, and entertainment.",
            "context": "Technology discussion"
        }
    ]

    target_languages = ["German", "French"]

    results = []

    for i, sample in enumerate(sample_texts, 1):
        print(f"\n🔤 Sample Text {i}")
        print(f"Text: {sample['text']}")
        print(f"Context: {sample['context']}")

        for target_lang in target_languages:
            # Test with no source language (should default to English)
            result = test_translation(
                text=sample['text'],
                target_language=target_lang,
                source_language=None,  # This should default to English
                context=sample['context']
            )

            if result:
                results.append({
                    'sample_number': i,
                    'original_text': sample['text'],
                    'target_language': target_lang,
                    'translation': result['translation'],
                    'source_language': result['source_language'],
                    'success': True
                })
            else:
                results.append({
                    'sample_number': i,
                    'original_text': sample['text'],
                    'target_language': target_lang,
                    'success': False
                })

            # Small delay between requests
            time.sleep(1)

    # Test with explicit source language
    print(f"\n🔤 Testing with Explicit Source Language")
    result = test_translation(
        text="Hello world",
        target_language="German",
        source_language="English",  # Explicitly set to English
        context="Simple greeting"
    )

    if result:
        results.append({
            'sample_number': 'explicit',
            'original_text': "Hello world",
            'target_language': "German",
            'translation': result['translation'],
            'source_language': result['source_language'],
            'success': True
        })

    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")

    successful_translations = [r for r in results if r['success']]
    failed_translations = [r for r in results if not r['success']]

    print(f"✓ Successful translations: {len(successful_translations)}")
    print(f"✗ Failed translations: {len(failed_translations)}")

    if successful_translations:
        print(f"\nSuccessful Translations:")
        for result in successful_translations:
            print(f"  Sample {result['sample_number']} → {result['target_language']}: '{result['translation'][:50]}...'")
            print(f"    Source language used: {result['source_language']}")

    if failed_translations:
        print(f"\nFailed Translations:")
        for result in failed_translations:
            print(f"  Sample {result['sample_number']} → {result['target_language']}")

    # Verify that source language is always English when not specified
    english_sources = [r for r in successful_translations if r['source_language'] == 'English']
    print(f"\n🔍 Verification:")
    print(f"  Translations using English as source: {len(english_sources)}/{len(successful_translations)}")

    if len(english_sources) == len(successful_translations):
        print("  ✓ All translations correctly used English as source language")
    else:
        print("  ⚠️  Some translations did not use English as source language")
        non_english = [r for r in successful_translations if r['source_language'] != 'English']
        for result in non_english:
            print(f"    Sample {result['sample_number']} used: {result['source_language']}")


if __name__ == "__main__":
    main()
