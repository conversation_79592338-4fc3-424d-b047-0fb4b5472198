import requests
from PIL import Image
from io import BytesIO
import base64

BASE_URL = "http://localhost:8000"


def get_api_response(prompt: str):
    data = {"prompt": prompt}
    response = requests.post(
        f"{BASE_URL}/album-cover-art",
        json=data,
    )

    return response.json()


if __name__ == "__main__":
    prompt = "a happy song. and turtles, definitely turtles"
    response = get_api_response(prompt)
    image_base64 = response["image_base64"]
    image = Image.open(BytesIO((base64.b64decode(image_base64))))
    image.show()
