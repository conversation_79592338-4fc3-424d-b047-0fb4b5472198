import requests

BASE_URL = "http://localhost:8000"


def get_api_response(prompt: str):
    headers = {
        "X-Api-Key": "api-key"
    }
    data = {"prompt": prompt}
    response = requests.post(
        f"{BASE_URL}/artist-generation",
        json=data,
        headers=headers
    )

    return response.json()


if __name__ == "__main__":
    prompt = "A young female pop singer who tends to make love songs. Somewhat of an intersection between <PERSON> and <PERSON>."
    response = get_api_response(prompt)
    print(response)
