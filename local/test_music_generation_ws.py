import asyncio
import websockets
import json


BASE_URL = "ws://localhost:8000"


async def test_music_generation():
    """Connect to the WebSocket endpoint and test music generation"""

    uri = f"{BASE_URL}/generate-music"
    try:
        async with websockets.connect(uri) as websocket:
            test_data = {
                "prompt": "Generate a cheerful piano melody",
            }

            await websocket.send(json.dumps(test_data))
            print(f"Sent: {test_data}")

            while True:
                try:
                    response = await websocket.recv()
                    data = json.loads(response)
                    print(f"Received: {data}")

                    if data.get("error"):
                        print(f"Error: {data['data']}")
                        break

                except websockets.exceptions.ConnectionClosed:
                    print("Connection closed")
                    break

    except Exception as e:
        print(f"Connection failed: {e}")


if __name__ == "__main__":
    asyncio.run(test_music_generation())
