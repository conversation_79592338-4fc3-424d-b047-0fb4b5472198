import requests

BASE_URL = "http://localhost:8000"


def get_api_response(prompt: str):
    headers = {
        "X-Api-Key": "api-key"
    }
    data = {"prompt": prompt}
    response = requests.post(
        f"{BASE_URL}/auto-prompt",
        json=data,
        headers=headers
    )

    return response.json()


if __name__ == "__main__":
    prompt = "Generate a happy song. and turtles, definitely turtles"
    response = get_api_response(prompt)
    print(response)
