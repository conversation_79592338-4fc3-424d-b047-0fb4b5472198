import asyncio
import websockets
import json
import base64
import wave
import gzip
from pathlib import Path

DOWNLOADS_DIR = Path.home() / "Downloads"
WS_BASE_URL = "ws://localhost:8000"

VOICE_ID = "21m00Tcm4TlvDq8ikWAM"
SCRIPT = """
Gzip compression is a widely-used data compression algorithm that combines two fundamental compression techniques: LZ77 (<PERSON><PERSON><PERSON> 1977) and <PERSON><PERSON><PERSON> coding. The algorithm works by identifying and eliminating redundancy in data through a two-stage process that first finds repeated sequences and then applies optimal encoding to the resulting symbols. When you run gzip on a file, it reads through the data in a sliding window fashion, maintaining a buffer of recently seen data that it constantly references to find matching patterns.
The first stage of gzip compression employs the LZ77 algorithm, which operates on a simple but powerful principle: instead of storing repeated sequences of bytes multiple times, it stores them once and then replaces future occurrences with references pointing back to the original sequence. The algorithm maintains a sliding window, typically 32KB in size, that moves through the input data. This window is conceptually divided into two parts: a search buffer (also called the dictionary) containing data that has already been processed, and a look-ahead buffer containing data that is about to be encoded. As the algorithm processes the input stream, it continuously searches for the longest match between the upcoming data in the look-ahead buffer and any sequence in the search buffer.
When LZ77 finds a matching sequence, it replaces the matched data with a pair of numbers: the distance back to where the match occurred and the length of the match. For example, if the text "the quick brown fox jumps over the lazy dog" is followed later by "the quick brown", the algorithm might encode the second occurrence as something like "go back 45 bytes and copy 15 bytes." This reference takes far fewer bits to store than the original text. The power of this approach becomes especially apparent with highly repetitive data, where long sequences might repeat hundreds or thousands of times throughout a file. The algorithm also handles partial matches intelligently, always seeking the longest possible match within the constraints of the window size.
The sliding window size represents a crucial trade-off in the algorithm's design. A larger window allows the algorithm to find matches from further back in the file, potentially improving compression for files with long-range redundancies. However, it also requires more memory during compression and decompression, and increases the time needed to search for matches. The 32KB window size used by gzip was chosen as a practical balance that works well for most data types while remaining efficient on the modest hardware available when the algorithm was developed. Modern variants and similar algorithms sometimes use larger windows, but gzip maintains this size for compatibility reasons.
After LZ77 processing, the output consists of a mixture of literal bytes (characters that couldn't be matched) and length-distance pairs (references to previous occurrences). This is where the second stage, Huffman coding, comes into play. Huffman coding is a variable-length encoding scheme that assigns shorter bit sequences to more frequently occurring symbols and longer sequences to rarer symbols. The algorithm builds a binary tree based on the frequency of each symbol in the data, with the most common symbols placed closer to the root, resulting in shorter paths and therefore shorter codes.
The Huffman coding stage in gzip is particularly sophisticated because it doesn't just encode the raw symbols. Instead, it creates two separate Huffman trees: one for the literal bytes and length values, and another for the distance values. The length and distance values are further processed through a clever scheme that groups similar values together, recognizing that certain ranges of lengths and distances are more common than others. For instance, very short distances (referring to recently seen data) are typically more common than very long distances, so the encoding scheme is optimized for this pattern. This two-tree approach allows the algorithm to better capture the different statistical properties of literals versus back-references.
The compression levels in gzip, ranging from 1 (fastest) to 9 (best compression), primarily control how much effort the algorithm puts into finding matches during the LZ77 stage. At level 1, the algorithm does minimal searching, quickly accepting the first reasonable match it finds. This results in faster compression but potentially misses better matches. At level 9, the algorithm exhaustively searches for the longest possible matches, even using sophisticated techniques like lazy matching, where it temporarily delays encoding a match to see if an even better match starts at the next position. The default level 6 represents a carefully chosen balance where the algorithm performs reasonably thorough searching without the diminishing returns seen at the highest levels.
The lazy matching strategy employed at higher compression levels is particularly interesting. When the algorithm finds a match, instead of immediately encoding it, it checks if waiting one more byte would yield a longer match. If the match starting at the next position is sufficiently better, it outputs the current byte as a literal and uses the better match instead. This can significantly improve compression for certain data patterns, though it requires additional computation. The algorithm might even chain multiple lazy evaluations, constantly seeking the optimal parsing of the input data.
Gzip also includes several important implementation details that affect its performance and compatibility. The compressed data is structured in blocks, with each block independently compressed. This block structure allows for streaming compression and decompression, where you don't need the entire file in memory at once. The format includes a header with metadata such as the original filename, timestamp, and operating system, followed by the compressed blocks, and finally a trailer containing a CRC32 checksum and the original file size. This structure ensures data integrity and provides useful information for decompression.
The effectiveness of gzip on different data types stems directly from these algorithmic choices. Text files compress well because they contain many repeated words, phrases, and patterns that LZ77 can efficiently reference. Programming source code is especially compressible due to repeated keywords, indentation patterns, and structural elements. The variable-length encoding of Huffman coding further helps because text files typically use only a subset of possible byte values, allowing for very efficient encoding of common characters. Structured data formats like JSON or XML compress exceptionally well because they have both repetitive structure (tags, field names) and often repetitive content.
Binary data presents more variation in compressibility. Uncompressed formats like PCM audio or BMP images often compress reasonably well because they contain patterns, even if those patterns aren't immediately obvious to human observation. For instance, PCM audio during quiet passages contains many similar or identical samples that LZ77 can reference efficiently. However, data that has already been compressed, encrypted, or is truly random will see little to no benefit from gzip. This is because these types of data have already had their redundancy removed or obscured, leaving no patterns for LZ77 to exploit, and their byte frequencies are often already nearly uniform, nullifying the benefits of Huffman coding.
The sliding window nature of gzip means that it has limited context memory, which affects its compression ratio on very large files with long-range redundancies. If similar data appears in a file but separated by more than 32KB, gzip cannot create a back-reference between them. This limitation is one reason why specialized compression tools for specific data types can outperform gzip; they can use knowledge about the data structure to maintain longer-range context or employ entirely different compression strategies suited to that particular data type.
Understanding gzip's operation also explains why compressing already-compressed data is ineffective and sometimes counterproductive. When data has already been compressed, its entropy is near maximum, meaning the symbols appear random and uniformly distributed. LZ77 finds few or no repeated sequences to reference, and Huffman coding cannot create shorter codes because all symbols appear with similar frequency. The small overhead of gzip's block structure and metadata might even cause the file to grow slightly. This is why file formats like JPEG, MP3, and ZIP show negligible compression with gzip, as they've already applied their own, often more sophisticated, compression algorithms.
The CPU and memory requirements of gzip compression vary significantly based on the chosen compression level and the nature of the input data. During compression, the algorithm must maintain the sliding window in memory and perform pattern matching, which at higher levels involves sophisticated data structures like hash tables and potentially suffix arrays for efficient searching. Decompression, however, is much simpler and faster, requiring only the sliding window buffer and the Huffman decoding tables. This asymmetry is intentional and beneficial: files are typically compressed once but decompressed many times, so optimizing for fast decompression makes sense.
The practical implications of gzip's design choices become apparent in real-world usage scenarios. Web servers commonly use gzip to compress HTML, CSS, and JavaScript files on-the-fly because these text-based formats compress well and the reduction in transfer time outweighs the CPU cost of compression. The streaming nature of gzip allows servers to begin sending compressed data before the entire response is ready. Database backups benefit from gzip because SQL dumps contain highly repetitive structure and data. Log files, with their repetitive timestamps and message formats, often achieve exceptional compression ratios. The algorithm's deterministic nature means that the same input always produces the same output, which is crucial for applications requiring reproducible results.
Modern computing environments have led to various optimizations and variations of the basic gzip algorithm. Hardware acceleration, parallel processing implementations, and improved pattern matching algorithms have all been applied while maintaining compatibility with the original format. Libraries like zlib provide the core compression functionality with various API options, allowing developers to fine-tune the compression process for their specific needs. Some implementations offer additional compression levels beyond the standard 1-9 range, and others provide options to optimize for specific data types while remaining compatible with standard gzip decompressors.
The longevity and continued relevance of gzip, despite being based on algorithms from the 1970s and 1980s, speaks to the fundamental soundness of its approach. While newer algorithms like Brotli, Zstandard, or LZ4 may offer better compression ratios or speed in certain scenarios, gzip's combination of decent compression, reasonable speed, wide compatibility, and robust implementation has made it a cornerstone of data compression in computing systems. Its presence in everything from web servers to build tools to data pipelines ensures that understanding its operation remains valuable for anyone working with data compression.
"""


async def generate_voice_over_ws(script, voice_id, compress_audio=True):
    """Generate voice over using WebSocket streaming"""
    uri = f"{WS_BASE_URL}/generate-voice-over"

    try:
        async with websockets.connect(uri) as websocket:
            # Send request
            payload = {
                "script": script,
                "voice_id": voice_id,
                "compress_audio": compress_audio
            }
            await websocket.send(json.dumps(payload))
            print(f"Sent request for script: {script[:50]}...")

            audio_chunks = []

            while True:
                try:
                    response = await websocket.recv()
                    data = json.loads(response)

                    if data.get("error"):
                        raise Exception(f"Server error: {data['data']}")

                    status = data.get("status")

                    if status == "initiating voice over generation":
                        print(f"Starting generation for {data['data']['script_length']} characters")

                    elif status == "streaming_audio":
                        chunk_data = data["data"]
                        chunk_number = chunk_data["chunk_number"]
                        audio_chunk = base64.b64decode(chunk_data["audio_chunk"])

                        # Decompress if needed
                        if chunk_data.get("compressed", False):
                            audio_chunk = gzip.decompress(audio_chunk)

                        audio_chunks.append(audio_chunk)
                        print(f"Received chunk {chunk_number}")

                    elif status == "completed":
                        total_chunks = data["data"]["total_chunks"]
                        print(f"Generation completed! Total chunks: {total_chunks}")
                        break

                except websockets.exceptions.ConnectionClosed:
                    print("Connection closed")
                    break

            return b''.join(audio_chunks)

    except Exception as e:
        print(f"WebSocket connection failed: {e}")
        return None


def save_audio_to_file(audio_data, filename="output.wav"):
    """Save audio data to a WAV file"""
    output_path = DOWNLOADS_DIR / filename
    with wave.open(str(output_path), 'wb') as wav_file:
        wav_file.setnchannels(1)  # mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(44100)  # 44kHz
        wav_file.writeframes(audio_data)
    print(f"Audio saved to: {output_path}")


async def main():
    print("=== WebSocket Voice Over Test ===")
    audio_data = await generate_voice_over_ws(SCRIPT, VOICE_ID, compress_audio=True)

    if audio_data:
        save_audio_to_file(audio_data, "test_voice_over_ws.wav")
        print("Test completed successfully!")
    else:
        print("Test failed!")


if __name__ == "__main__":
    asyncio.run(main())
