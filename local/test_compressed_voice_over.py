import requests
import base64
import wave
import gzip
from pathlib import Path

DOWNLOADS_DIR = Path.home() / "Downloads"
API_BASE_URL = "http://localhost:8000"


def generate_voice_over(script, voice_id):
    """Generate voice over using script and voice ID"""
    url = f"{API_BASE_URL}/generate-voice-over"
    payload = {
        "script": script,
        "voice_id": voice_id,
        "compress_audio": True
    }

    response = requests.post(url, json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Failed to generate voice over: {response.status_code} - {response.text}")


def save_audio_to_file(audio_data, filename="output.wav"):
    """Save audio data to a WAV file"""
    output_path = DOWNLOADS_DIR / filename
    with wave.open(str(output_path), 'wb') as wav_file:
        wav_file.setnchannels(1)  # mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(44100)  # 24kHz
        wav_file.writeframes(audio_data)
    print(f"Audio saved to: {output_path}")


def main():
    script = "Hello world. This is a test. This is only a test."
    voice_id = "21m00Tcm4TlvDq8ikWAM"

    voice_over_response = generate_voice_over(script, voice_id)
    compressed_audio = base64.b64decode(voice_over_response["audio_base64"])

    # Decompress if compressed
    if voice_over_response.get("compressed", False):
        audio_data = gzip.decompress(compressed_audio)
        print("Audio decompressed successfully")
    else:
        audio_data = compressed_audio

    # Save to file
    save_audio_to_file(audio_data, "test_voice_over.wav")


if __name__ == "__main__":
    main()
