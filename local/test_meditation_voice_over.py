import requests
import base64
import wave
from pathlib import Path

HOME_DIR = Path.home()
API_BASE_URL = "http://localhost:8000"

SPEAKER_DESCRIPTION = """
A calm, soothing MALE voice with a warm and nurturing tone.
The speaker has a gentle, meditative quality with slight pauses between sentences.
<PERSON><PERSON> speaks with wisdom and tranquility, perfect for guided meditation.
Age: mid-30s, with a slight British accent.
"""

MEDITATION_DESCRIPTION = """
A 5-minute mindfulness meditation focused on breath awareness and present moment attention.
The meditation guides listeners through breathing exercises, body awareness,
and gentle encouragement to return attention to the breath when the mind wanders.
The tone should be peaceful, non-judgmental, and supportive.
"""


def generate_spoken_word_script(meditation_prompt, creativity=0.7):
    """Generate a spoken word script for meditation"""
    url = f"{API_BASE_URL}/spoken-word-script"
    payload = {
        "prompt": meditation_prompt,
        "model_creativity": creativity
    }

    response = requests.post(url, json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Failed to generate script: {response.status_code} - {response.text}")


def generate_voice(voice_description):
    """Generate a voice based on description"""
    url = f"{API_BASE_URL}/generate-voice"
    payload = {
        "voice_description": voice_description
    }

    response = requests.post(url, json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Failed to generate voice: {response.status_code} - {response.text}")


def generate_voice_over(script, voice_id, language_code="en"):
    """Generate voice over using script and voice ID"""
    url = f"{API_BASE_URL}/generate-voice-over"
    payload = {
        "script": script,
        "voice_id": voice_id,
        "language_code": language_code
    }

    response = requests.post(url, json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Failed to generate voice over: {response.status_code} - {response.text}")


def save_audio_file(base64_audio, filename):
    """Save base64 encoded PCM audio as WAV file"""
    audio_data = base64.b64decode(base64_audio)

    with wave.open(filename, 'wb') as wav_file:
        wav_file.setnchannels(1)  # mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(44100)  # 44.1kHz
        wav_file.writeframes(audio_data)

    print(f"Audio saved to {filename}")


def translate_audio(audio_base64, target_language):
    """Translate audio to target language"""
    url = f"{API_BASE_URL}/translate-wav-audio"
    payload = {
        "audio_base64": audio_base64,
        "target_language": target_language
    }

    response = requests.post(url, json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Failed to translate audio: {response.status_code} - {response.text}")


def main():
    try:
        print("=== Meditation Voice Over Generator ===")
        print(f"Speaker: {SPEAKER_DESCRIPTION.strip()}")
        print(f"Meditation: {MEDITATION_DESCRIPTION.strip()}")
        print()

        print("Step 1: Generating spoken word script...")

        voice_response = generate_voice(SPEAKER_DESCRIPTION)
        voice_id = voice_response.get("voice_id")
        if not voice_id:
            raise Exception(f"Failed to generate voice: {voice_response}")
        print(f"Generated voice ID: {voice_id}")
        print()

        print("Step 2: Generating voice...")
        print("NOTE: Only do this step once per individual voice.")
        script_response = generate_spoken_word_script(MEDITATION_DESCRIPTION)
        script = script_response.get("script")
        print(f"Generated script: {script[:100]}...")
        print()

        print("Step 3: Generating voice over...")
        voice_over_response = generate_voice_over(script, voice_id)
        english_audio_file = Path(HOME_DIR, "Downloads", "meditation_voice_over.wav")
        if not voice_over_response.get("error", False) and "audio_base64" in voice_over_response:
            audio_base64 = voice_over_response["audio_base64"]
            save_audio_file(audio_base64, str(english_audio_file))
            print("Voice over generation completed successfully!")
        else:
            print(f"Voice over response: {voice_over_response}")

        audio_base64 = base64.b64encode(english_audio_file.read_bytes()).decode("utf-8")
        print("Step 4: Generating voice over...")
        languages = ("fr", "de")  # french, german
        for lang in languages:
            print(f"Translating to {lang}...")
            translated_audio_response = translate_audio(audio_base64, lang)
            if not translated_audio_response.get("error", False) and "audio_base64" in translated_audio_response:
                audio_base64 = translated_audio_response["audio_base64"]
                save_audio_file(audio_base64, str(english_audio_file.parent / f"meditation_voice_over_{lang}.wav"))
                print(f"Translation to {lang} completed successfully!")
            else:
                print(f"Translation response: {translated_audio_response}")

        print("Step 5: Generate instrumental using music generation API with instrumenal only flag...")
        print("Step 6: Generate music art")

    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
