{"description": "Workflow templates - one example for each job type with all parameters documented", "jobs": [{"job_type": "workflow", "workflow_name": "prompt_enhancement_example", "tasks": [{"task_type": "prompt_enhancement", "parameters": {"prompt": "Create a peaceful ambient track", "creativity": 0.6}}]}, {"job_type": "workflow", "workflow_name": "music_generation_example", "tasks": [{"task_type": "music_generation", "parameters": {"prompt": "Create an upbeat electronic dance track with energetic beats", "music_style": "Electronic", "lyrics": "Dancing through the night, feeling so alive, electronic beats make us thrive", "make_instrumental": false, "vocal_only": false, "voice_id": null}}]}, {"job_type": "workflow", "workflow_name": "album_cover_generation_example", "tasks": [{"task_type": "album_cover_generation", "parameters": {"prompt": "A vibrant sunset over mountains with musical notes floating in the air", "creativity": 0.7}}]}, {"job_type": "workflow", "workflow_name": "spoken_word_script_example", "tasks": [{"task_type": "spoken_word_script", "parameters": {"prompt": "Write an inspiring spoken word piece about overcoming challenges and finding inner strength", "creativity": 0.4, "agent_description": "A passionate motivational speaker with a powerful, inspiring voice"}}]}, {"job_type": "workflow", "workflow_name": "artist_generation_example", "tasks": [{"task_type": "artist_generation", "parameters": {"prompt": "Create a unique indie folk artist with a soulful voice and authentic storytelling style", "creativity": 0.3, "agent_description": "A creative music industry professional specializing in artist development and branding"}}]}, {"job_type": "workflow", "workflow_name": "sound_generation_example", "tasks": [{"task_type": "sound_generation", "parameters": {"prompt": "Ocean waves crashing on a peaceful beach with seagulls in the distance", "audio_length_seconds": 30}}]}, {"job_type": "workflow", "workflow_name": "text_translation_example", "tasks": [{"task_type": "text_translation", "parameters": {"text": "Welcome to our music generation platform! Create amazing songs with AI technology and share them with the world.", "target_language": "Spanish", "source_language": "English", "quality": "high", "context": "Marketing message for a music platform"}}]}, {"job_type": "workflow", "workflow_name": "complete_music_production_example", "tasks": [{"task_type": "prompt_enhancement", "parameters": {"prompt": "Create upbeat electronic music", "creativity": 0.8}}, {"task_type": "music_generation", "parameters": {"prompt": "Create upbeat electronic music with synthesizers and driving beats", "music_style": "Electronic", "lyrics": "", "make_instrumental": false, "vocal_only": false, "voice_id": null}}, {"task_type": "album_cover_generation", "parameters": {"prompt": "Electronic music album cover with neon lights", "creativity": 0.7}}]}, {"job_type": "workflow", "workflow_name": "ambient_soundscape_production_example", "tasks": [{"task_type": "prompt_enhancement", "parameters": {"prompt": "Create a relaxing nature soundscape", "creativity": 0.6}}, {"task_type": "sound_generation", "parameters": {"prompt": "Gentle rain falling on leaves with distant thunder and bird songs", "audio_length_seconds": 60}}, {"task_type": "album_cover_generation", "parameters": {"prompt": "Peaceful forest scene with rain drops on green leaves", "creativity": 0.5}}]}, {"job_type": "workflow", "workflow_name": "sound_to_music_extension_workflow_example", "tasks": [{"task_type": "prompt_enhancement", "parameters": {"prompt": "Create a short ambient intro with gentle piano", "creativity": 0.7}}, {"task_type": "sound_generation", "parameters": {"prompt": "Gentle piano melody with soft ambient background", "audio_length_seconds": 20}}, {"task_type": "music_extension", "parameters": {"extend_after_seconds": 15, "prompt": "Develop into a full ambient track with strings and gentle percussion"}}]}, {"job_type": "workflow", "workflow_name": "sound_to_music_remixing_workflow_example", "tasks": [{"task_type": "prompt_enhancement", "parameters": {"prompt": "Create a melodic piano piece", "creativity": 0.6}}, {"task_type": "sound_generation", "parameters": {"prompt": "Gentle piano melody with soft harmonies", "audio_length_seconds": 30}}, {"task_type": "music_remixing", "parameters": {"prompt": "Remix this piano piece into a modern hip-hop beat with urban elements", "lyrics": "Smooth piano flows, urban beats grow, remix the sound, new vibes all around"}}]}, {"job_type": "workflow", "workflow_name": "export_results_example", "tasks": [{"task_type": "music_generation", "parameters": {"prompt": "Create an upbeat electronic dance track with energetic beats", "music_style": "Electronic"}}, {"task_type": "album_cover_generation", "parameters": {"prompt": "A vibrant sunset with neon accents"}}, {"task_type": "export", "parameters": {"target_bucket": "gs://gen-tc-ingest", "target_prefix": "genai", "assets": ["audio", "images", "json"], "audio_filenames": ["song_1.wav"], "image_filenames": ["album_cover.jpg"], "json_filenames": ["job_result.json"]}}]}, {"job_type": "workflow", "workflow_name": "multilingual_content_creation_example", "tasks": [{"task_type": "text_translation", "parameters": {"text": "Create a peaceful meditation experience with soothing sounds and gentle melodies.", "target_language": "French", "source_language": "English", "quality": "high", "context": "Description for a meditation music album"}}, {"task_type": "prompt_enhancement", "parameters": {"prompt": "Create peaceful meditation music", "creativity": 0.5}}, {"task_type": "music_generation", "parameters": {"prompt": "Peaceful meditation music with soft piano and nature sounds", "music_style": "Ambient", "make_instrumental": true, "vocal_only": false}}, {"task_type": "album_cover_generation", "parameters": {"prompt": "Serene meditation scene with soft colors and peaceful nature elements", "creativity": 0.6}}]}, {"job_type": "workflow", "workflow_name": "voice_generation_example", "tasks": [{"task_type": "voice_generation", "parameters": {"voice_description": "Warm, calm female narrator with a slight British accent, friendly and clear", "voice_name": "TemplateNarratorWarm"}}]}, {"job_type": "workflow", "workflow_name": "voice_over_generation_example", "tasks": [{"task_type": "voice_over_generation", "parameters": {"script": "Welcome to our product demo. Today we will explore the latest features and improvements.", "voice_id": "21m00Tcm4TlvDq8ikWAM", "language_code": "en", "filename_base": "demo_voice_over"}}]}, {"job_type": "workflow", "workflow_name": "music_to_vocal_extraction_workflow_example", "tasks": [{"task_type": "prompt_enhancement", "parameters": {"prompt": "Create a pop song with clear vocals", "creativity": 0.7}}, {"task_type": "music_generation", "parameters": {"prompt": "Create an upbeat pop song with clear vocals and catchy melody", "music_style": "Pop", "lyrics": "Dancing in the moonlight, feeling so free, music in my heart, that's the key", "make_instrumental": false, "vocal_only": false}}, {"task_type": "vocal_extraction", "parameters": {"preprocessing_options": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}]}, {"job_type": "workflow", "workflow_name": "vocal_extraction_and_remix_workflow_example", "tasks": [{"task_type": "music_generation", "parameters": {"prompt": "Create a rock song with powerful vocals and guitar", "music_style": "Rock", "lyrics": "Break the chains, rise above, rock and roll is what we love", "make_instrumental": false}}, {"task_type": "vocal_extraction", "parameters": {"preprocessing_options": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}}, {"task_type": "music_remixing", "parameters": {"prompt": "Transform the instrumental into an electronic dance version while keeping the original vocals", "lyrics": "Electronic beats, rock vocals meet, dance floor heat, can't be beat"}}]}], "parameter_schema": {"workflow_structure": {"job_type": {"type": "string", "description": "Must be 'workflow'", "required": true, "value": "workflow"}, "workflow_name": {"type": "string", "description": "Unique identifier for the workflow", "required": true, "examples": ["prompt_enhancement_example", "music_generation_example", "album_cover_generation_example", "spoken_word_script_example", "artist_generation_example", "sound_generation_example", "text_translation_example", "audio_translation_example", "music_extension_example", "music_remixing_example", "vocal_extraction_example", "sound_to_music_extension_workflow_example", "sound_to_music_remixing_workflow_example", "music_to_vocal_extraction_workflow_example", "vocal_extraction_and_remix_workflow_example", "complete_music_production_example", "complete_music_production_with_remix_example", "ambient_soundscape_production_example", "multilingual_content_creation_example"]}, "tasks": {"type": "array", "description": "Array of tasks to execute in sequence", "required": true, "min_items": 1}}, "task_types": {"prompt_enhancement": {"description": "Enhances user prompts for better music generation", "required_parameters": {"prompt": {"type": "string", "description": "The music prompt to enhance", "required": true, "examples": ["Create a peaceful ambient track", "Generate upbeat electronic music"]}, "creativity": {"type": "number", "description": "Creativity level for prompt enhancement", "required": true, "min": 0.0, "max": 1.0, "examples": [0.3, 0.5, 0.6, 0.8, 0.9]}}}, "music_generation": {"description": "Generates audio tracks using MusicGPT API", "required_parameters": {"prompt": {"type": "string", "description": "Text prompt describing the music to generate", "required": true, "examples": ["Create an upbeat electronic dance track", "Generate a peaceful ambient soundscape", "Make a rock song with guitar solos"]}}, "optional_parameters": {"music_style": {"type": "string", "description": "Musical style/genre", "required": false, "examples": ["Pop", "Rock", "Jazz", "Classical", "Hip-Hop", "Electronic", "Country", "R&B", "Folk", "Blues", "Lo-Fi", "Metal"]}, "lyrics": {"type": "string", "description": "Custom lyrics for the song", "required": false, "examples": ["Dancing through the night, feeling so alive", "Custom verse and chorus lyrics"]}, "make_instrumental": {"type": "boolean", "description": "If true, generate instrumental only (no vocals)", "required": false, "default": false}, "vocal_only": {"type": "boolean", "description": "If true, generate vocals only (no instruments)", "required": false, "default": false}, "voice_id": {"type": "string", "description": "Specific voice to use for vocals (e.g., '<PERSON>')", "required": false, "default": null, "examples": ["<PERSON>", "<PERSON>", null]}}}, "album_cover_generation": {"description": "Creates visual artwork for music albums", "required_parameters": {"prompt": {"type": "string", "description": "Visual description for the album cover artwork", "required": true, "examples": ["A vibrant sunset over mountains with musical notes", "Abstract geometric patterns in neon colors", "Vintage vinyl record with cosmic background"]}, "creativity": {"type": "number", "description": "Creativity level for artwork generation", "required": true, "min": 0.0, "max": 1.0, "examples": [0.4, 0.6, 0.7, 0.8, 0.9]}}}, "spoken_word_script": {"description": "Generates spoken word scripts using Google Gemini AI", "required_parameters": {"prompt": {"type": "string", "description": "Description of the spoken word piece to create", "required": true, "examples": ["Write an inspiring piece about overcoming challenges", "Create a motivational script about pursuing dreams", "Generate a reflective piece about personal growth"]}, "creativity": {"type": "number", "description": "Creativity level for script generation", "required": true, "min": 0.0, "max": 1.0, "examples": [0.2, 0.3, 0.4, 0.5, 0.6]}}, "optional_parameters": {"agent_description": {"type": "string", "description": "Description of the speaker persona or style", "required": false, "examples": ["A passionate motivational speaker", "A thoughtful poet with a gentle voice", "An energetic performer with dynamic delivery"]}}}, "artist_generation": {"description": "Creates comprehensive artist profiles using Google Gemini AI", "required_parameters": {"prompt": {"type": "string", "description": "Description of the artist to create", "required": true, "examples": ["Create a unique indie folk artist with authentic storytelling", "Generate a pop artist with electronic influences", "Design a jazz musician with classical training"]}, "creativity": {"type": "number", "description": "Creativity level for artist generation", "required": true, "min": 0.0, "max": 1.0, "examples": [0.2, 0.3, 0.4, 0.5, 0.6]}}, "optional_parameters": {"agent_description": {"type": "string", "description": "Description of the music industry professional creating the artist profile", "required": false, "examples": ["A creative music industry professional specializing in artist development", "An experienced A&R representative with deep genre knowledge", "A music producer focused on emerging talent"]}}}, "sound_generation": {"description": "Generates sound effects and ambient audio using MusicGPT API", "required_parameters": {"prompt": {"type": "string", "description": "Text prompt describing the sound to generate", "required": true, "examples": ["Ocean waves crashing on a beach", "Rain falling on leaves with distant thunder", "Birds chirping in a forest", "City traffic with car horns", "Crackling fireplace with wood burning", "Wind blowing through trees"]}}, "optional_parameters": {"audio_length_seconds": {"type": "integer", "description": "Duration of the generated sound in seconds", "required": false, "min": 1, "max": 300, "default": 10, "examples": [10, 30, 60, 120, 180]}}}, "music_extension": {"description": "Extends existing audio tracks using MusicGPT API", "required_parameters": {"audio_gcs_path": {"type": "string", "description": "GCS path to the source audio file to extend (e.g., output from sound_generation or music_generation)", "required": true, "pattern": "^gs://[a-zA-Z0-9._-]+/.*\\.(wav|mp3|flac|m4a)$", "examples": ["gs://music-generation-storage/assets/music/job-id/song_1.wav", "gs://bucket-name/path/to/audio.wav"]}, "extend_after_seconds": {"type": "integer", "description": "Extend the music after this many seconds from the beginning", "required": true, "min": 1, "max": 300, "examples": [15, 30, 45, 60, 90]}, "prompt": {"type": "string", "description": "Text prompt describing how to extend the music", "required": true, "examples": ["Continue with a more energetic section", "Add a dramatic breakdown and build-up", "Develop into a full orchestral arrangement", "Extend with jazz improvisation"]}}, "optional_parameters": {"lyrics": {"type": "string", "description": "Custom lyrics for the extended section", "required": false, "examples": ["Building up the energy, taking it higher", "Extended verse and chorus lyrics", ""]}}}, "text_translation": {"description": "Translates text from one language to another using OpenAI GPT", "required_parameters": {"text": {"type": "string", "description": "Text to translate", "required": true, "min_length": 1, "max_length": 10000, "examples": ["Welcome to our music platform!", "Create amazing songs with AI technology.", "This is a beautiful melody that touches the heart."]}, "target_language": {"type": "string", "description": "Target language for translation", "required": true, "examples": ["Spanish", "French", "German", "Italian", "Portuguese", "Chinese (Simplified)", "Japanese", "Korean", "Arabic", "Russian", "Dutch", "Swedish", "Hindi"]}}, "optional_parameters": {"source_language": {"type": "string", "description": "Source language (defaults to English if not provided)", "required": false, "default": "English", "examples": ["English", "Spanish", "French", "German", "Italian", "Portuguese", "Chinese (Simplified)", "Japanese", "Korean"]}, "context": {"type": "string", "description": "Additional context to help with translation", "required": false, "max_length": 500, "examples": ["Marketing message for a music platform", "Technical documentation", "Creative content for social media", "Formal business communication"]}, "quality": {"type": "string", "description": "Translation quality level", "required": false, "default": "standard", "enum": ["standard", "high", "creative"], "examples": ["standard", "high", "creative"]}, "model_creativity": {"type": "number", "description": "Model creativity/temperature (0.0 to 1.0)", "required": false, "min": 0.0, "max": 1.0, "default": 0.2, "examples": [0.1, 0.2, 0.3, 0.4, 0.5]}}}, "music_remixing": {"description": "Remixes existing audio tracks using MusicGPT API to create new variations", "required_parameters": {"audio_gcs_path": {"type": "string", "description": "GCS path to the source audio file to remix (e.g., output from sound_generation or music_generation)", "required": true, "pattern": "^gs://[a-zA-Z0-9._-]+/.*\\.(wav|mp3|flac|m4a)$", "examples": ["gs://music-generation-storage/assets/music/job-id/song_1.wav", "gs://bucket-name/path/to/audio.wav"]}, "prompt": {"type": "string", "description": "Text prompt describing how to remix the music", "required": true, "min_length": 5, "max_length": 500, "examples": ["Transform this into an electronic dance remix with heavy bass", "Create an acoustic version with guitar and soft vocals", "Remix into a hip-hop beat with urban elements", "Convert to a jazz arrangement with saxophone and piano", "Make it a rock version with electric guitars and drums"]}}, "optional_parameters": {"lyrics": {"type": "string", "description": "Custom lyrics for the remixed song", "required": false, "max_length": 2000, "examples": ["Dance all night, feel the beat, electronic vibes so sweet", "Smooth piano flows, urban beats grow, remix the sound, new vibes all around", "Rock and roll, heart and soul, music makes us whole"]}}}, "voice_generation": {"description": "Designs and creates a custom synthetic voice using ElevenLabs", "required_parameters": {"voice_description": {"type": "string", "description": "Natural language description of the desired voice (tone, gender, accent, age, style)", "required": true, "min_length": 10, "max_length": 1000, "examples": ["Warm, calm female narrator with a slight British accent, friendly and clear", "Deep, confident male voice with a neutral American accent, suitable for product demos", "Energetic, expressive voice with a youthful tone and subtle Australian accent"]}}, "optional_parameters": {"voice_name": {"type": "string", "description": "Optional display name for the generated voice profile", "required": false, "max_length": 100, "examples": ["TemplateNarratorWarm", "ProductDemoVoice", "YouthfulGuide"]}}}, "voice_over_generation": {"description": "Generates spoken-word audio (WAV) from a script using a specified ElevenLabs voice", "required_parameters": {"script": {"type": "string", "description": "The text/script to be spoken", "required": true, "min_length": 5, "max_length": 5000, "examples": ["Welcome to our product demo. Today, we'll explore the latest features.", "Take a deep breath in... and out... Let your body relax as you listen."]}, "voice_id": {"type": "string", "description": "Identifier of the ElevenLabs voice to use", "required": true, "examples": ["voice_1234567890abcdef"]}}, "optional_parameters": {"language_code": {"type": "string", "description": "Language/accent code for generation", "required": false, "default": "en", "examples": ["en", "en-GB", "es", "fr"]}, "filename_base": {"type": "string", "description": "Base filename for the generated audio asset (stored as WAV)", "required": false, "default": "voice_over", "examples": ["voice_over", "narration_intro"]}}}, "vocal_extraction": {"description": "Extracts vocals and instrumental tracks from existing audio using MusicGPT API", "required_parameters": {"audio_gcs_path": {"type": "string", "description": "GCS path to the source audio file to extract vocals from (e.g., output from music_generation)", "required": true, "pattern": "^gs://[a-zA-Z0-9._-]+/.*\\.(wav|mp3|flac|m4a)$", "examples": ["gs://music-generation-storage/assets/music/job-id/song_with_vocals.wav", "gs://bucket-name/path/to/audio.mp3"]}}, "optional_parameters": {"preprocessing_options": {"type": "array", "description": "Array of preprocessing options to apply before vocal extraction", "required": false, "items": {"type": "string", "enum": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, "examples": [["<PERSON><PERSON>", "<PERSON><PERSON>"], ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]]}}}, "audio_translation": {"description": "Translates audio from one language to another using ElevenLabs dubbing API", "required_parameters": {"audio_base64": {"type": "string", "description": "Base64-encoded audio data to translate", "required": true, "examples": ["UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA="]}, "target_language": {"type": "string", "description": "Target language for translation", "required": true, "examples": ["Spanish", "French", "German", "Italian", "Portuguese", "Chinese", "Japanese", "Korean", "Russian", "Arabic", "Hindi", "Dutch"]}}, "optional_parameters": {"source_language": {"type": "string", "description": "Source language (defaults to English)", "required": false, "default": "English", "examples": ["English", "Spanish", "French"]}, "filename_base": {"type": "string", "description": "Base filename for the translated audio asset (stored as WAV)", "required": false, "default": "translated_audio", "examples": ["translated_audio", "welcome_message", "product_demo"]}}}, "export": {"description": "Exports final result assets (audio, images, JSON) to an external GCS bucket, preserving filenames and formats.", "required_parameters": {"target_bucket": {"type": "string", "description": "Destination GCS bucket. Accepts gs://bucket or bare bucket name.", "required": true, "examples": ["gs://gen-tc-ingest", "gen-tc-ingest"]}}, "optional_parameters": {"target_prefix": {"type": "string", "description": "Base prefix in destination bucket. Subfolders are applied per type: audio/{job_id}, images/{job_id}, json/{job_id}.", "required": false, "default": "genai", "examples": ["genai", "exports/team-a"]}, "assets": {"type": "string", "description": "Which assets to export. Accepts a single value or an array in task parameters. If 'all', exports audio, images, and json.", "required": false, "default": "all", "enum": ["all", "audio", "images", "json"], "examples": ["all", "audio", ["audio", "json"]]}, "source_job_id": {"type": "string", "description": "Optional job ID to read internal assets from. Defaults to the current job.", "required": false, "examples": ["4e6d2a6d-3c41-4a2f-8c63-2c8128b6a1f2"]}, "destination_job_id": {"type": "string", "description": "Optional job ID to use when constructing destination paths. Defaults to the current job.", "required": false, "examples": ["4e6d2a6d-3c41-4a2f-8c63-2c8128b6a1f2"]}, "audio_filenames": {"type": "array", "description": "Explicit audio filenames to export from internal assets/music/{job_id}/ (e.g., 'song_1.wav'). If provided, discovery is skipped for audio.", "required": false, "items": {"type": "string"}, "examples": [["song_1.wav"], ["song_1.wav", "song_2.wav"]]}, "image_filenames": {"type": "array", "description": "Explicit image filenames to export from internal assets/images/{job_id}/ (e.g., 'album_cover.jpg'). If provided, discovery is skipped for images.", "required": false, "items": {"type": "string"}, "examples": [["album_cover.jpg"], ["album_cover.jpg", "thumbnail.png"]]}, "json_filenames": {"type": "array", "description": "Explicit result JSON filenames to export from internal results/{job_id}/ (default includes 'job_result.json'). If provided, discovery is skipped for JSON.", "required": false, "items": {"type": "string"}, "examples": [["job_result.json"]]}, "destination_credentials_json": {"type": "string", "description": "Optional raw service account JSON used to write to the destination bucket. Omit if the running service account already has access.", "required": false}, "destination_credentials_secret": {"type": "string", "description": "Optional environment variable name containing service account JSON (checked as NAME or NAME.upper()).", "required": false, "examples": ["EXTERNAL_BUCKET_SA_JSON"]}}, "path_schema": {"type": "object", "description": "Destination layout in the external bucket.", "properties": {"audio": {"example": "gs://<bucket>/<prefix>/audio/<job_id>/<filename>"}, "images": {"example": "gs://<bucket>/<prefix>/images/<job_id>/<filename>"}, "json": {"example": "gs://<bucket>/<prefix>/json/<job_id>/job_result.json"}}}}}}}