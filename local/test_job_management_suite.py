#!/usr/bin/env python3
"""
Job Management End-to-End Test Suite

Testing tool for job management system with updated timeout and retry configurations.

Commands:
    health                                  - Check system health
    details <job_id>                        - Get detailed job info
    analyze                                 - Show analytics dashboard (job stats + recent jobs)

Workflow testing (flags only, no command required):
    --run-jobs <JSON_FILE>                  - Submit jobs from JSON and then monitor (combined mode)
    --submit-jobs <JSON_FILE>               - Submit jobs only; print and save submitted job IDs
    --monitor-jobs <JOB_IDS_FILE>           - Monitor previously submitted jobs listed in file
    --num-jobs N                            - Only with --run-jobs/--submit-jobs: limit to first N jobs
    --submitted-jobs-file <FILE>            - Optional: where to save job IDs on submit (default: submitted_jobs.json)

Usage:
    python test_job_management_suite.py <command> [options] [--url <URL>]
    python test_job_management_suite.py --run-jobs jobs.json [--num-jobs N]
    python test_job_management_suite.py --submit-jobs jobs.json [--num-jobs N] [--submitted-jobs-file FILE]
    python test_job_management_suite.py --monitor-jobs my_jobs.json
"""

import asyncio
import aiohttp
import sys
import time
import traceback
import json
import os
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

# Configuration
DEFAULT_JOB_MANAGEMENT_URL = "http://localhost:8001"
INSECURE = False

TIMEOUT_CONFIG = {
    "http_connection": 30, "http_read": 300, "websocket": 300,
    "cloud_run": 1200, "queue_retry": 3600
}

SUBMITTED_JOBS_DEFAULT_FILE = "submitted_jobs.json"

RETRY_CONFIG = {
    "http_client_max": 2, "application_max": 1, "cloud_tasks_max": 3,
    "rate_limit_requeue": True, "resource_constraint_requeue": True
}

# Utilities
class Colors:
    RED, GREEN, YELLOW, BLUE, NC = '\033[0;31m', '\033[0;32m', '\033[1;33m', '\033[0;34m', '\033[0m'

@dataclass
class JobResult:
    job_id: str
    index: int
    success: bool
    has_results: bool = False
    monitor_result: Optional[Dict[str, Any]] = None
    job_data: Optional[Dict[str, Any]] = None

def print_status(msg: str): print(f"{Colors.BLUE}ℹ️  {msg}{Colors.NC}")
def print_success(msg: str): print(f"{Colors.GREEN}✅ {msg}{Colors.NC}")
def print_warning(msg: str): print(f"{Colors.YELLOW}⚠️  {msg}{Colors.NC}")
def print_error(msg: str): print(f"{Colors.RED}❌ {msg}{Colors.NC}")

def truncate(text: str, max_len: int = 12) -> str:
    return text[:max_len] + "..." if len(text) > max_len else text


# Error handling
def is_rate_limit_error(error_msg: str, status_code: Optional[int] = None) -> bool:
    if status_code == 429:
        return True
    indicators = ["rate limited", "rate limit exceeded", "too many requests", "quota exceeded"]
    return any(indicator in error_msg.lower() for indicator in indicators)

def is_resource_constraint_error(error_msg: str) -> bool:
    indicators = ["no slots available", "slot availability", "resource unavailable",
                 "capacity exceeded", "concurrent limit reached"]
    return any(indicator in error_msg.lower() for indicator in indicators)

def should_requeue_job(error_msg: str, status_code: Optional[int] = None, **kwargs) -> bool:
    return is_rate_limit_error(error_msg, status_code) or is_resource_constraint_error(error_msg)


# HTTP utilities
async def safe_json_response(response: aiohttp.ClientResponse) -> Optional[Dict[str, Any]]:
    try:
        return await response.json()
    except (json.JSONDecodeError, aiohttp.ContentTypeError):
        return None

def create_timeout(timeout_type: str = "http_read") -> aiohttp.ClientTimeout:
    return aiohttp.ClientTimeout(
        connect=TIMEOUT_CONFIG["http_connection"],
        total=TIMEOUT_CONFIG.get(timeout_type, TIMEOUT_CONFIG["http_read"])
    )

async def http_request(session: aiohttp.ClientSession, method: str, url: str,
                      max_retries: int = None, **kwargs) -> Optional[Dict[str, Any]]:
    """Unified HTTP request function with retry logic."""
    if max_retries is None:
        max_retries = RETRY_CONFIG["http_client_max"]

    for attempt in range(max_retries + 1):
        try:
            timeout = create_timeout()
            async with aiohttp.ClientSession(timeout=timeout) as fresh_session:
                async with fresh_session.request(method, url, **kwargs) as response:
                    if response.status == 200:
                        return await safe_json_response(response)
                    elif attempt == max_retries:
                        return None
        except (aiohttp.ClientError, asyncio.TimeoutError):
            if attempt == max_retries:
                return None
            await asyncio.sleep(2 ** attempt)
    return None



# Configuration loading
def load_job_config_from_file(file_path: str) -> Dict[str, Any]:
    """Load and validate job configuration from JSON file."""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)

        if not isinstance(data, dict) or 'jobs' not in data:
            raise ValueError("Invalid JSON structure")

        jobs = data['jobs']
        if not isinstance(jobs, list) or len(jobs) == 0:
            raise ValueError("Jobs array is empty or invalid")

        return data
    except Exception as e:
        print_error(f"Failed to load configuration: {e}")
        raise

# Submission/tracking helpers

def save_submitted_job_ids_to_file(job_ids: List[str], file_path: str) -> None:
    try:
        data = {
            "job_ids": job_ids,
            "count": len(job_ids),
            "submitted_at": datetime.now(timezone.utc).isoformat()
        }
        with open(file_path, "w") as f:
            json.dump(data, f, indent=2)
        print_success(f"Saved {len(job_ids)} submitted job IDs to {file_path}")
    except Exception as e:
        print_warning(f"Failed to save job IDs to {file_path}: {e}")


def load_job_ids_from_file(file_path: str) -> List[str]:
    try:
        with open(file_path, "r") as f:
            data = json.load(f)
        if isinstance(data, list):
            return [str(x) for x in data]
        if isinstance(data, dict) and isinstance(data.get("job_ids"), list):
            return [str(x) for x in data["job_ids"]]
        raise ValueError("Invalid format: expected a list or an object with 'job_ids' list")
    except Exception as e:
        print_error(f"Failed to load submitted job IDs from {file_path}: {e}")
        raise


def build_jobs_from_ids(job_ids: List[str]) -> List[Dict[str, Any]]:
    return [
        {"job_id": jid, "index": i + 1, "workflow_name": f"job_{i+1}", "submitted_at": time.time()}
        for i, jid in enumerate(job_ids)
    ]

# Health checks
async def check_service_health(session: aiohttp.ClientSession, name: str, url: str, endpoint: str) -> bool:
    try:
        async with session.get(f"{url}{endpoint}") as response:
            success = response.status == 200
            status = "Available" if success else f"Unavailable (HTTP {response.status})"
            print(f"{'✅' if success else '❌'} {name}: {status}")
            return success
    except Exception as e:
        print(f"❌ {name}: Error - {e}")
        return False


async def check_system_health(session: aiohttp.ClientSession, job_mgmt_url: str) -> Dict[str, bool]:
    """Check system health and return status."""
    print("🔍 System Health Check")
    print("=" * 50)

    # Check services
    checks = [
        ("Job Management", job_mgmt_url, "/health"),
        ("Database", job_mgmt_url, "/health")
    ]

    results = {}
    for name, url, endpoint in checks:
        results[name.lower().replace(" ", "_")] = await check_service_health(session, name, url, endpoint)

    # Summary
    healthy = sum(results.values())
    total = len(results)
    print(f"\n📊 Health Summary: {healthy}/{total} systems healthy")

    if healthy == total:
        print("🎉 All systems operational!")
    elif healthy > 0:
        print("⚠️  Some systems need attention")
    else:
        print("🚨 Critical: Multiple system failures")

    return results


async def test_error_handling_behavior() -> None:
    """Test error handling and retry behavior."""
    print("🧪 Testing Error Handling Behavior")
    print("=" * 50)

    test_cases = [
        ("Rate limited: Too many requests", 429, True, "Rate limit"),
        ("No slots available", None, True, "Resource constraint"),
        ("Internal server error", 500, False, "Server error"),
        ("Invalid request", 400, False, "Client error")
    ]

    for error_msg, status_code, should_requeue, category in test_cases:
        result = should_requeue_job(error_msg, status_code)
        icon = "✅" if result == should_requeue else "❌"
        action = "requeue" if result else "Cloud Tasks retry"
        print(f"  {icon} {category}: '{error_msg}' -> {action}")

    print("\n✅ Error handling test completed")
    print("📝 Rate limiting and resource constraints trigger requeuing, others use Cloud Tasks retry")


# Job operations
async def submit_job(session: aiohttp.ClientSession, job_mgmt_url: str, job_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Submit a job to the job management service."""
    return await http_request(session, "POST", f"{job_mgmt_url}/jobs", json=job_data,
                             headers={"Content-Type": "application/json"})

async def get_job_status(session: aiohttp.ClientSession, job_mgmt_url: str, job_id: str) -> Optional[Dict[str, Any]]:
    """Get job status with retry logic."""
    return await http_request(session, "GET", f"{job_mgmt_url}/jobs/{job_id}")

async def get_detailed_job_status(session: aiohttp.ClientSession, job_mgmt_url: str, job_id: str) -> Optional[Dict[str, Any]]:
    """Get detailed job status."""
    return await http_request(session, "GET", f"{job_mgmt_url}/jobs/{job_id}/detailed")


# Display utilities
def get_status_icon(status: str) -> str:
    icons = {'completed': '✅', 'running': '🔄', 'failed': '❌', 'cancelled': '🚫'}
    return icons.get(status, '⏳')

def display_task_progress(data: Dict[str, Any]) -> None:
    """Display workflow task progress."""
    tasks = data.get('tasks', [])
    if not tasks:
        return

    progress = data.get('workflow_progress_percentage', 0)
    current_idx = data.get('current_task_index')

    print(f"📊 Progress: {progress}% | Current Task: #{current_idx}")
    for i, task in enumerate(tasks):
        status = task.get('status', 'unknown')
        task_type = task.get('task_type', 'unknown')
        icon = get_status_icon(status)
        current = ' 👈' if i == current_idx else ''
        print(f"  {icon} Task {i}: {task_type} ({status}){current}")


# Task processing
async def trigger_task_processing(session: aiohttp.ClientSession, job_mgmt_url: str, job_id: str, job_index: int = 0) -> None:
    """Trigger task processing for a job."""
    task_data = {
        "job_id": job_id,
        "action": "process_job",
        "queue": "task-queue-local",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

    try:
        timeout = create_timeout("cloud_run")
        async with aiohttp.ClientSession(timeout=timeout) as fresh_session:
            async with fresh_session.post(f"{job_mgmt_url}/tasks/process", json=task_data,
                                        headers={"Content-Type": "application/json"}) as response:
                if response.status != 200:
                    print_warning(f"Job {job_index} processing failed: HTTP {response.status}")
    except asyncio.TimeoutError:
        pass  # Timeout is normal for long-running jobs
    except Exception as e:
        print_warning(f"Job {job_index} processing error: {e}")


# Job configuration
def create_job_configs() -> Dict[str, Dict[str, Any]]:
    """Create job configurations for testing."""
    return {
        'album_cover': {
            "job_type": "workflow", "workflow_name": "album_cover_generation",
            "tasks": [{"task_type": "album_cover_generation",
                      "parameters": {"prompt": "Vibrant sunset over mountains with musical notes", "creativity": 0.7}}]
        },
        'music_generation': {
            "job_type": "workflow", "workflow_name": "music_generation",
            "tasks": [{"task_type": "music_generation",
                      "parameters": {"prompt": "Create a peaceful ambient soundscape", "music_style": "Ambient",
                                   "make_instrumental": True, "vocal_only": False}}]
        },
        'prompt_enhancement': {
            "job_type": "workflow", "workflow_name": "prompt_enhancement",
            "tasks": [{"task_type": "prompt_enhancement",
                      "parameters": {"prompt": "Generate upbeat electronic music", "creativity": 0.8}}]
        },
        'text_translation': {
            "job_type": "workflow", "workflow_name": "text_translation",
            "tasks": [{"task_type": "text_translation",
                      "parameters": {"text": "Welcome to our music generation platform! Create amazing songs with AI.",
                                   "target_language": "Spanish", "source_language": "English", "quality": "high"}}]
        },
        'workflow': {
            "job_type": "workflow", "workflow_name": "music_production",
            "tasks": [
                {"task_type": "prompt_enhancement", "parameters": {"prompt": "Create upbeat electronic music", "creativity": 0.8}},
                {"task_type": "music_generation", "parameters": {"prompt": "Electronic music with synthesizers", "music_style": "Electronic"}},
                {"task_type": "album_cover_generation", "parameters": {"prompt": "Electronic album cover with neon lights", "creativity": 0.7}}
            ]
        },
        'music_remixing': {
            "job_type": "workflow", "workflow_name": "music_remixing_test",
            "tasks": [
                {"task_type": "music_remixing", "parameters": {
                    "audio_gcs_path": "gs://test-bucket/sample_audio.wav",
                    "prompt": "Transform this into an electronic dance remix with heavy bass and energetic beats",
                    "lyrics": "Dance all night, feel the beat, electronic vibes so sweet"
                }}
            ]
        },
        'sound_to_remix_workflow': {
            "job_type": "workflow", "workflow_name": "sound_to_remix_workflow",
            "tasks": [
                {"task_type": "prompt_enhancement", "parameters": {"prompt": "Create a melodic piano piece", "creativity": 0.6}},
                {"task_type": "sound_generation", "parameters": {"audio_length_seconds": 30}},
                {"task_type": "music_remixing", "parameters": {
                    "prompt": "Remix this piano piece into a modern hip-hop beat with urban elements",
                    "lyrics": "Smooth piano flows, urban beats grow, remix the sound, new vibes all around"
                }}
            ]
        }
    }

# Job submission
async def submit_jobs_sequentially(session: aiohttp.ClientSession, job_mgmt_url: str, job_configs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Submit jobs sequentially."""
    jobs = []
    print_status(f"📤 Phase 1: Sequential Job Submission ({len(job_configs)} jobs)")
    print("=" * 60)

    for i, job_config in enumerate(job_configs):
        workflow_name = job_config.get('workflow_name', f'job_{i+1}')
        print_status(f"Submitting job {i+1}/{len(job_configs)}: {workflow_name}")

        response = await submit_job(session, job_mgmt_url, job_config)
        if response:
            job_id = response.get("job_id")
            jobs.append({
                'job_id': job_id, 'index': i + 1, 'workflow_name': workflow_name,
                'initial_status': response.get("status"), 'submitted_at': time.time()
            })
            print_success(f"✅ Job {i+1} submitted: {truncate(job_id)}")

            # Trigger processing
            asyncio.create_task(trigger_task_processing(session, job_mgmt_url, job_id, i+1))
        else:
            print_error(f"❌ Failed to submit job {i+1}: {workflow_name}")

        if i < len(job_configs) - 1:
            await asyncio.sleep(1)

    print_success(f"📤 Phase 1 Complete: {len(jobs)}/{len(job_configs)} jobs submitted")
    return jobs


# Job monitoring
async def monitor_jobs_with_polling(session: aiohttp.ClientSession, job_mgmt_url: str, jobs: List[Dict[str, Any]],
                                  poll_interval: int = 5, timeout_minutes: int = None) -> List[JobResult]:
    """Monitor jobs using polling."""
    if not jobs:
        return []

    # Calculate timeout - account for MusicGPT concurrency limit of 10
    if timeout_minutes is None:
        # For jobs beyond the first 10, they need to wait for earlier jobs to complete
        # Each music generation can take 2-4 minutes, so we need more time for queued jobs
        if len(jobs) <= 10:
            timeout_minutes = max(8, min(35, len(jobs) * 4 + 3))  # 4min/job + 3min buffer
        else:
            # For jobs > 10: account for sequential processing after first batch
            first_batch_time = 8  # First 10 jobs in parallel
            remaining_jobs = len(jobs) - 10
            sequential_time = remaining_jobs * 5  # 5min per job for queued jobs (includes wait time)
            timeout_minutes = min(60, first_batch_time + sequential_time + 5)  # Cap at 60min with 5min buffer

    print()  # Add blank line before Phase 2
    print_status(f"📊 Phase 2: Monitoring {len(jobs)} jobs (timeout: {timeout_minutes}min)")
    print("=" * 60)

    # Initialize tracking
    completed_jobs, failed_jobs, results = set(), set(), []
    job_status_map = {job['job_id']: {'job': job, 'last_status': None} for job in jobs}

    start_time = time.time()
    poll_count = 0
    timeout_seconds = timeout_minutes * 60

    try:
        while len(completed_jobs) + len(failed_jobs) < len(jobs):
            poll_count += 1
            elapsed_time = time.time() - start_time

            # Check timeout
            if elapsed_time > timeout_seconds:
                print_warning(f"\n⏰ Timeout reached ({timeout_minutes}min)")
                for job_id, job_info in job_status_map.items():
                    if job_id not in completed_jobs and job_id not in failed_jobs:
                        results.append(JobResult(job_id=job_id, index=job_info['job']['index'],
                                               success=False, monitor_result={'status': 'timeout'}))
                break

            # Poll job statuses
            any_completions = False
            for job_id, job_info in job_status_map.items():
                if job_id in completed_jobs or job_id in failed_jobs:
                    continue

                job = job_info['job']
                try:
                    status_response = await get_job_status(session, job_mgmt_url, job_id)
                    if not status_response:
                        continue

                    current_status = status_response.get('status', 'unknown')

                    # Handle completed/failed jobs
                    if current_status in ['completed', 'failed', 'cancelled'] and current_status != job_info['last_status']:
                        any_completions = True
                        job_info['last_status'] = current_status

                        detailed_info = await get_detailed_job_status(session, job_mgmt_url, job_id)

                        if current_status == 'completed':
                            completed_jobs.add(job_id)
                            print_success(f"🎉 Job {job['index']} ({job.get('workflow_name', 'job')}) COMPLETED ({elapsed_time:.1f}s)")

                            # Show task results
                            if detailed_info and detailed_info.get('tasks'):
                                tasks = detailed_info['tasks']
                                print(f"   📋 Tasks: {len(tasks)} completed")

                            # Display GCS URLs immediately when job completes
                            if detailed_info and detailed_info.get('results'):
                                job_results = detailed_info.get('results', {})
                                assets = job_results.get('assets', {})
                                if assets:
                                    print(f"   🔗 Generated Assets:")
                                    for asset_type, asset_url in assets.items():
                                        print(f"     • {asset_type}: {asset_url}")

                            results.append(JobResult(job_id=job_id, index=job['index'], success=True,
                                                   has_results=bool(detailed_info and detailed_info.get('results')),
                                                   monitor_result={'status': 'completed', 'completion_time': elapsed_time},
                                                   job_data=detailed_info))
                        else:
                            failed_jobs.add(job_id)
                            print_error(f"❌ Job {job['index']} FAILED: {current_status}")

                            # Analyze error for retry behavior
                            if detailed_info and detailed_info.get('error_message'):
                                error_msg = detailed_info['error_message']
                                if should_requeue_job(error_msg, detailed_info.get('status_code')):
                                    print(f"   🔄 Error type: Requeue candidate")
                                else:
                                    print(f"   ⬆️  Error type: Cloud Tasks retry candidate")

                            results.append(JobResult(job_id=job_id, index=job['index'], success=False,
                                                   monitor_result={'status': current_status, 'completion_time': elapsed_time}))

                except Exception:
                    if poll_count % 3 == 0:  # Show connection issues every 3rd poll
                        print_warning(f"⚠️  Connection issue with job {job['index']}")
                    continue

            # Show progress updates
            total_completed = len(completed_jobs) + len(failed_jobs)
            if any_completions or (poll_count % 6 == 0 and total_completed < len(jobs)):
                remaining = len(jobs) - total_completed
                if not any_completions:
                    print(f"📊 Progress check #{poll_count}: {total_completed}/{len(jobs)} completed, {remaining} running")
            # Wait before next poll
            await asyncio.sleep(poll_interval)

    except KeyboardInterrupt:
        print_warning("\n⚠️  Monitoring interrupted by user")

    # Final summary
    total_time = time.time() - start_time
    successful_count = len(completed_jobs)
    failed_count = len(failed_jobs)

    print_success(f"📊 Phase 2 Complete: All jobs finished after {total_time:.1f}s")
    print(f"  ✅ Successful: {successful_count}")
    print(f"  ❌ Failed: {failed_count}")
    print(f"  📈 Success Rate: {(successful_count/len(jobs)*100):.1f}%")



    return results



# Main workflow test
async def run_multiple_job_configs(session: aiohttp.ClientSession, job_mgmt_url: str, job_configs: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Run multiple job configurations using two-phase approach."""
    print(f"🧪 Running {len(job_configs)} job configurations")
    print("=" * 70)
    print("Two-phase approach: Sequential submission + Polling-based monitoring\n")

    # Phase 1: Submit jobs
    jobs = await submit_jobs_sequentially(session, job_mgmt_url, job_configs)
    if not jobs:
        return {'status': 'error', 'message': 'No jobs submitted'}

    # Phase 2: Monitor jobs
    results = await monitor_jobs_with_polling(session, job_mgmt_url, jobs, poll_interval=5)

    # Summary
    successful = sum(1 for r in results if r.success)
    with_results = sum(1 for r in results if r.has_results)
    total = len(jobs)

    print("\n" + "=" * 70)
    print(f"🎯 FINAL RESULTS")
    print("=" * 70)
    print(f"  📊 Total: {total} | ✅ Success: {successful} | ❌ Failed: {total - successful}")
    print(f"  📁 With Results: {with_results} | 📈 Success Rate: {(successful/total*100):.1f}%")

    print("\n📋 Individual Results:")
    for result in results:
        job_info = next((j for j in jobs if j['job_id'] == result.job_id), {})
        name = job_info.get('workflow_name', f'job_{result.index}')
        icon = "✅" if result.success else "❌"
        results_icon = "📁" if result.has_results else "📭"
        print(f"  {icon} Job {result.index}: {name} {results_icon}")

    return {
        'status': 'completed', 'total_jobs': total, 'successful_jobs': successful,
        'success_rate': (successful/total*100) if total > 0 else 0,
        'results': [r.__dict__ for r in results]
    }






# Job details display
async def display_job_details(job_data: Dict[str, Any]) -> None:
    """Display formatted job details."""
    # Basic info
    print("📋 Job Information:")
    for label, field in [('Job ID', 'job_id'), ('Type', 'job_type'), ('Status', 'status'),
                        ('Created', 'created_at'), ('Error', 'error_message')]:
        value = job_data.get(field)
        if value or field in ['job_id', 'job_type', 'status']:
            print(f"  {label}: {value or 'N/A'}")

    # Progress
    progress = job_data.get('progress_percentage', 0)
    print(f"  Progress: {progress}%")

    if job_data.get('job_type') == 'workflow':
        workflow_progress = job_data.get('workflow_progress_percentage', 0)
        print(f"  Workflow Progress: {workflow_progress}%")

    # Tasks
    tasks = job_data.get('tasks')
    if tasks:
        print(f"\n📋 Tasks ({len(tasks)}):")
        for i, task in enumerate(tasks):
            status = task.get('status', 'unknown')
            task_type = task.get('task_type', 'unknown')
            icon = get_status_icon(status)
            print(f"  {icon} Task {i}: {task_type} ({status})")

    # Results
    results = job_data.get('results')
    if results:
        print(f"\n🎯 Results:")
        assets = results.get('assets', {})
        if assets:
            print(f"  📁 Assets ({len(assets)}):")
            for asset_type, asset_url in assets.items():
                print(f"    - {asset_type}: {asset_url}")
        else:
            print("  📁 No assets available")
    elif job_data.get('status') == 'completed':
        print("\n⚠️  Job completed but no results available")
    else:
        print(f"\n📝 Job status: {job_data.get('status', 'unknown')}")


# Single job monitoring
async def monitor_single_job(session: aiohttp.ClientSession, job_mgmt_url: str, job_id: str, timeout: int = 300) -> Dict[str, Any]:
    """Monitor a single job with real-time updates."""
    print(f"🔍 Monitoring job: {truncate(job_id)} (real-time)")
    print("=" * 60)
    print("Press Ctrl+C to stop monitoring\n")

    start_time = time.time()
    last_status = None

    try:
        while True:
            try:
                job_data = await get_detailed_job_status(session, job_mgmt_url, job_id)
                if not job_data:
                    print("❌ Failed to get job data")
                    await asyncio.sleep(5)
                    continue

                current_status = job_data.get('status', 'unknown')
                elapsed = time.time() - start_time

                # Show status updates
                if current_status != last_status:
                    print(f"📊 Status: {current_status} | Time: {elapsed:.0f}s")
                    last_status = current_status

                # Show task progress
                if job_data.get('tasks'):
                    display_task_progress(job_data)

                # Check if complete
                if current_status in ['completed', 'failed', 'cancelled']:
                    print(f"\n🏁 Job {current_status} after {elapsed:.1f}s")
                    print("=" * 60)
                    await display_job_details(job_data)
                    return {'status': 'success', 'job_id': job_id, 'job_data': job_data, 'total_time': elapsed}

                # Check timeout
                if elapsed > timeout:
                    print(f"\n⏰ Timeout reached ({timeout}s)")
                    await display_job_details(job_data)
                    return {'status': 'timeout', 'job_id': job_id, 'total_time': elapsed}

                await asyncio.sleep(5)

            except Exception as e:
                print(f"❌ Error during monitoring: {e}")
                await asyncio.sleep(5)

    except KeyboardInterrupt:
        print(f"\n⚠️  Monitoring stopped by user")
        return {'status': 'interrupted', 'job_id': job_id}


async def get_detailed_job_info(session: aiohttp.ClientSession, job_mgmt_url: str, job_id: str) -> Dict[str, Any]:
    """Get detailed job information."""
    print(f"🔍 Getting detailed information for job: {job_id}")
    print("=" * 60)

    try:
        job_data = await get_detailed_job_status(session, job_mgmt_url, job_id)
        if not job_data:
            return {'status': 'not_found', 'job_id': job_id}

        await display_job_details(job_data)
        print(f"\n✅ Successfully retrieved job information!")
        return {'status': 'success', 'job_id': job_id, 'job_data': job_data}

    except Exception as e:
        print(f"❌ Error getting job information: {e}")
        return {'status': 'error', 'job_id': job_id, 'error': str(e)}


# Analytics
async def get_job_statistics(session: aiohttp.ClientSession, job_mgmt_url: str) -> Dict[str, Any]:
    """Get job statistics from analytics endpoint."""
    try:
        data = await http_request(session, "GET", f"{job_mgmt_url}/analytics/jobs/stats")
        if not data:
            return {'status': 'error', 'error': 'Failed to get statistics'}

        stats = {
            'total_jobs': data.get('total_jobs', 0),
            'completed_jobs': data.get('completed_jobs', 0),
            'failed_jobs': data.get('failed_jobs', 0),
            'success_rate': data.get('success_rate', 0)
        }

        # Display statistics
        print(f"  📈 Total Jobs: {stats['total_jobs']}")
        print(f"  ✅ Completed: {stats['completed_jobs']}")
        print(f"  ❌ Failed: {stats['failed_jobs']}")
        print(f"  🎯 Success Rate: {stats['success_rate']:.1f}%")

        return {'status': 'success', **stats}
    except Exception as e:
        return {'status': 'error', 'error': str(e)}


async def get_recent_jobs(session: aiohttp.ClientSession, job_mgmt_url: str, limit: int = 10) -> Dict[str, Any]:
    """Get recent jobs from analytics endpoint."""
    try:
        data = await http_request(session, "GET", f"{job_mgmt_url}/analytics/jobs/recent?limit={limit}")
        if not data:
            return {'status': 'error', 'error': 'Failed to get recent jobs'}

        jobs = data.get('jobs', [])
        if jobs:
            display_limit = min(5, len(jobs))
            for job in jobs[:display_limit]:
                status = job.get('status', 'unknown')
                job_type = job.get('job_type', 'unknown')
                job_id = truncate(job.get('job_id', 'unknown'))
                created = job.get('created_at', 'N/A')[:19] if job.get('created_at') else 'N/A'

                icon = get_status_icon(status)
                print(f"  {icon} {job_id} | {job_type} | {status} | {created}")

            if len(jobs) > display_limit:
                print(f"  ... and {len(jobs) - display_limit} more jobs")
        else:
            print("  No recent jobs found")

        return {'status': 'success', 'job_count': len(jobs)}
    except Exception as e:
        return {'status': 'error', 'error': str(e)}

async def test_job_analytics(session: aiohttp.ClientSession, job_mgmt_url: str) -> Dict[str, Any]:
    """Get job analytics including statistics and recent jobs."""
    print("📊 Job Analytics Dashboard")
    print("=" * 40)

    print("\n📊 Job Statistics:")
    job_stats = await get_job_statistics(session, job_mgmt_url)

    print("\n📋 Recent Jobs:")
    recent_jobs = await get_recent_jobs(session, job_mgmt_url, 10)

    successful = sum(1 for result in [job_stats, recent_jobs] if result.get('status') == 'success')
    print(f"\n🎯 Analytics Complete: {successful}/2 data sources retrieved")

    return {'job_stats': job_stats, 'recent_jobs': recent_jobs}


def print_usage():
    """Print usage information."""
    print("""
Usage:
  python test_job_management_suite.py <command> [options]
  python test_job_management_suite.py --run-jobs jobs.json [--num-jobs N]
  python test_job_management_suite.py --submit-jobs jobs.json [--num-jobs N] [--submitted-jobs-file FILE]
  python test_job_management_suite.py --monitor-jobs my_jobs.json

Commands:
  health                                  - Check system health
  details <job_id>                        - Get detailed job info
  analyze                                 - Show analytics dashboard

Workflow testing (flags only, no command required):
  --run-jobs <JSON_FILE>                   - Submit jobs from JSON and then monitor (combined mode)
  --submit-jobs <JSON_FILE>                - Submit jobs only; print and save submitted job IDs
  --monitor-jobs <JOB_IDS_FILE>            - Monitor previously submitted jobs listed in file
  --num-jobs N                             - Only with --run-jobs/--submit-jobs: limit to first N jobs
  --submitted-jobs-file <FILE>             - Optional: where to save job IDs on submit (default: submitted_jobs.json)

General options:
  --url <URL>                              - Job Management service URL (default: http://localhost:8001)
  --insecure                               - Skip SSL certificate verification

Examples:
  # Submit only first 5 jobs defined in input JSON, save IDs to file
  python test_job_management_suite.py --submit-jobs jobs.json --num-jobs 5 --submitted-jobs-file my_jobs.json

  # Track only previously submitted jobs from file
  python test_job_management_suite.py --monitor-jobs my_jobs.json

  # Combined: submit all jobs from input and then track until completion
  python test_job_management_suite.py --run-jobs jobs.json

  # Health, details, analytics
  python test_job_management_suite.py health
  python test_job_management_suite.py details job-abc123def456
  python test_job_management_suite.py analyze
""")


async def main():
    """Main function with command-line argument parsing"""
    args = sys.argv[1:]

    # Default URLs
    job_management_url = DEFAULT_JOB_MANAGEMENT_URL

    # Check for --url flag
    if "--url" in args:
        url_index = args.index("--url")
        if url_index + 1 >= len(args):
            print("❌ --url flag requires a URL argument")
            sys.exit(1)
        job_management_url = args[url_index + 1]
        # Remove --url and its value from args
        args = args[:url_index] + args[url_index + 2:]





    # --insecure flag
    global INSECURE
    if "--insecure" in args:
        INSECURE = True
        args = [a for a in args if a != "--insecure"]


    # aiohttp connector settings
    connector = None
    if INSECURE:
        try:
            import ssl
            ssl_ctx = ssl.create_default_context()
            ssl_ctx.check_hostname = False
            ssl_ctx.verify_mode = ssl.CERT_NONE
            connector = aiohttp.TCPConnector(ssl=ssl_ctx)
        except Exception as _e:
            print_warning(f"Failed to configure insecure SSL context: {_e}")
            connector = None

    try:
        async with aiohttp.ClientSession(connector=connector) as session:
            # Workflow mode driven by flags (no explicit command required)
            workflow_flags = {"--run-jobs", "--submit-jobs", "--monitor-jobs", "--num-jobs", "--submitted-jobs-file"}
            is_workflow_mode = any(flag in args for flag in workflow_flags)

            if is_workflow_mode:
                # Parse mode flags and enforce mutual exclusivity
                def parse_flag_with_value(flag: str) -> Optional[str]:
                    if flag in args:
                        idx = args.index(flag)
                        if idx + 1 >= len(args) or args[idx + 1].startswith("--"):
                            print_error(f"❌ {flag} requires a file path argument")
                            sys.exit(1)
                        return args[idx + 1]
                    return None

                run_jobs_path = parse_flag_with_value("--run-jobs")
                submit_jobs_path = parse_flag_with_value("--submit-jobs")
                monitor_jobs_path = parse_flag_with_value("--monitor-jobs")

                modes_selected = sum(1 for v in [run_jobs_path, submit_jobs_path, monitor_jobs_path] if v is not None)
                if modes_selected == 0:
                    print_error("❌ You must specify one of: --run-jobs, --submit-jobs, or --monitor-jobs")
                    sys.exit(1)
                if modes_selected > 1:
                    print_error("❌ --run-jobs, --submit-jobs, and --monitor-jobs are mutually exclusive")
                    sys.exit(1)

                # Optional: submitted IDs output file (used on submission)
                submitted_jobs_file = SUBMITTED_JOBS_DEFAULT_FILE
                if "--submitted-jobs-file" in args:
                    idx = args.index("--submitted-jobs-file")
                    if idx + 1 >= len(args) or args[idx + 1].startswith("--"):
                        print_error("❌ --submitted-jobs-file requires a file path")
                        sys.exit(1)
                    submitted_jobs_file = args[idx + 1]

                # Optional: --num-jobs (only valid for run/submit modes)
                num_jobs: Optional[int] = None
                if "--num-jobs" in args:
                    if monitor_jobs_path is not None:
                        print_error("❌ --num-jobs is not applicable with --monitor-jobs")
                        sys.exit(1)
                    n_idx = args.index("--num-jobs")
                    if n_idx + 1 >= len(args) or args[n_idx + 1].startswith("--"):
                        print_error("❌ --num-jobs requires a positive integer")
                        sys.exit(1)
                    try:
                        num_jobs = int(args[n_idx + 1])
                        if num_jobs <= 0:
                            raise ValueError
                    except Exception:
                        print_error("❌ --num-jobs must be a positive integer")
                        sys.exit(1)

                # Monitor-only mode: read job IDs and monitor
                if monitor_jobs_path is not None:
                    try:
                        job_ids = load_job_ids_from_file(monitor_jobs_path)
                    except Exception:
                        sys.exit(1)
                    jobs = build_jobs_from_ids(job_ids)
                    await monitor_jobs_with_polling(session, job_management_url, jobs, poll_interval=5)
                    return

                # Determine JSON file for run or submit modes
                json_file_path = run_jobs_path or submit_jobs_path

                # Load job configurations
                try:
                    config_data = load_job_config_from_file(json_file_path)
                    job_configs = config_data['jobs']
                    if num_jobs is not None:
                        job_configs = job_configs[:num_jobs]
                    print_success(f"Loaded {len(job_configs)} workflow configurations from {json_file_path}")
                except Exception as e:
                    print_error(f"Failed to load workflow configuration: {e}")
                    sys.exit(1)

                # Phase 1: submit
                jobs = await submit_jobs_sequentially(session, job_management_url, job_configs)
                job_ids = [j['job_id'] for j in jobs if j.get('job_id')]

                # Save IDs if submit mode OR if run mode with explicit output file
                if submit_jobs_path is not None or (run_jobs_path is not None and "--submitted-jobs-file" in args):
                    print("\nSubmitted Job IDs:")
                    for jid in job_ids:
                        print(jid)
                    save_submitted_job_ids_to_file(job_ids, submitted_jobs_file)

                # If submit-only, stop here
                if submit_jobs_path is not None:
                    return

                # Combined mode: monitor after submission
                await monitor_jobs_with_polling(session, job_management_url, jobs, poll_interval=5)
                return

            # Command-based operations
            if len(args) < 1:
                print_usage()
                sys.exit(1)

            command = args[0].lower()

            if command == "health":
                await check_system_health(session, job_management_url)

            elif command == "details":
                if len(args) < 2:
                    print("❌ Job ID required for details command")
                    sys.exit(1)
                job_id = args[1]
                await get_detailed_job_info(session, job_management_url, job_id)

            elif command == "analyze":
                await test_job_analytics(session, job_management_url)

            else:
                print(f"❌ Unknown command: {command}")
                print_usage()
                sys.exit(1)

    except Exception as e:
        print(f"❌ Error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print_warning("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        traceback.print_exc()
        sys.exit(1)
