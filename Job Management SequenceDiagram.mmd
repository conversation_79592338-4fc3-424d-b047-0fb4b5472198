sequenceDiagram
    participant C as Client Application
    participant JM_API as Job Management API<br/>(Port 8001)
    participant J<PERSON> as JobManager
    participant QM as CloudTasksQueueManager
    participant <PERSON><PERSON> as MusicGPTQueueManager
    participant TE as TaskExecutor
    participant MG<PERSON> as MusicGPT Executor
    participant SM as StorageManager
    participant PG as PostgreSQL Database
    participant GCS as Google Cloud Storage
    participant MGPT as MusicGPT API
    participant WS as WebSocket Connection

    Note over C,WS: Music Generation Workflow - Job Management System

    %% Job Submission Phase
    C->>JM_API: 1. POST /jobs/submit
    Note right of C: {<br/>  "job_type": "workflow",<br/>  "tasks": [<br/>    {"type": "music_generation",<br/>     "prompt": "energetic rock anthem",<br/>     "music_style": "Rock"}<br/>  ]<br/>}

    JM_API->>JM: 2. submit_job(request, client_ip, user_agent)
    
    JM->>PG: 3. INSERT INTO jobs
    Note right of JM: Create job record with<br/>status="queued"
    PG-->>JM: 4. job_id returned

    JM->>JM: 5. get_queue_for_job(job)
    Note right of JM: Analyze tasks:<br/>music_generation → musicgpt_queue

    JM->>QM: 6. enqueue_task("musicgpt_queue", task_data)
    QM->>QM: 7. Create Cloud Tasks job
    Note right of QM: Schedule task with<br/>job_id and queue info

    JM_API-->>C: 8. JobResponse
    Note left of JM_API: {<br/>  "job_id": "abc-123",<br/>  "status": "queued",<br/>  "created_at": "2024-01-01T10:00:00Z"<br/>}

    %% Optional WebSocket Connection
    C->>WS: 9. Connect for real-time updates
    WS-->>C: 10. Connection established

    %% Job Processing Phase
    Note over QM,TE: Cloud Tasks triggers job processing

    QM->>TE: 11. process_job(job_id, "musicgpt_queue")
    
    TE->>PG: 12. UPDATE jobs SET status='running'
    TE->>PG: 13. SELECT job configuration
    
    TE->>MGQ: 14. acquire_slot(task_id)
    Note right of MGQ: Check MusicGPT rate limits:<br/>- Max 10 concurrent<br/>- 60 conversions/min<br/>- 200 status checks/min

    alt Slot Available
        MGQ->>PG: 15. INSERT INTO active_music_generation
        MGQ-->>TE: 16. Slot acquired ✓
        
        TE->>MGE: 17. execute_music_generation(task_config)
        
        %% MusicGPT API Interaction
        MGE->>MGPT: 18. POST /MusicAI
        Note right of MGE: Headers: Authorization<br/>Payload: prompt, music_style, etc.
        MGPT-->>MGE: 19. {"task_id": "mgpt-456"}
        
        %% Progress Updates via WebSocket
        TE->>WS: 20. Send status update
        WS-->>C: 21. "Status: Processing started"
        
        %% Polling Loop
        loop Poll every 15 seconds (adaptive)
            MGE->>MGPT: 22. GET /byId?task_id=mgpt-456
            MGPT-->>MGE: 23. {"status": "PROCESSING", ...}
            
            TE->>PG: 24. UPDATE workflow progress
            TE->>WS: 25. Send progress update
            WS-->>C: 26. "Status: Generating music... 45%"
        end
        
        %% Completion
        MGPT-->>MGE: 27. {"status": "COMPLETED", "conversion_path_1": "https://...", ...}
        
        MGE->>SM: 28. store_job_result(job_id, result_data)
        SM->>GCS: 29. Upload result metadata
        Note right of SM: Store to results/ folder<br/>with job assets URLs
        
        MGE-->>TE: 30. Task completed with results
        
        TE->>MGQ: 31. release_slot(task_id)
        MGQ->>PG: 32. UPDATE active_music_generation SET is_active=false
        
        TE->>PG: 33. UPDATE jobs SET status='completed'
        TE->>PG: 34. UPDATE workflow_progress_percentage=100
        
        %% Final Status Update
        TE->>WS: 35. Send completion update
        WS-->>C: 36. "Status: Completed! Results available"
        
    else No Slot Available
        MGQ-->>TE: 16b. Slot unavailable - requeue
        TE->>QM: 17b. enqueue_task("musicgpt_queue", task_data, delay=60)
        Note right of TE: Retry after 60 seconds
    end

    %% Result Retrieval
    C->>JM_API: 37. GET /jobs/{job_id}/status
    JM_API->>JM: 38. get_job_status(job_id)
    JM->>PG: 39. SELECT job details
    JM->>SM: 40. get_job_asset_urls(job_id)
    SM->>GCS: 41. Check available assets
    
    JM_API-->>C: 42. Job status with asset URLs
    Note left of JM_API: {<br/>  "status": "completed",<br/>  "progress": 100,<br/>  "assets": {<br/>    "music_generation_audio": "gs://bucket/results/...",<br/>    "metadata": "gs://bucket/results/..."<br/>  }<br/>}

    %% Asset Download
    C->>GCS: 43. Download generated music files
    GCS-->>C: 44. Audio files and metadata

    %% Cleanup (Background)
    Note over TE,PG: Background cleanup processes
    TE->>PG: 45. Cleanup old job records (24h retention)
    SM->>GCS: 46. Cleanup temp files

    %% Error Handling Paths
    Note over C,WS: Error Scenarios (not shown in main flow)
    Note over MGQ,MGPT: - Rate limit exceeded → requeue with backoff<br/>- MusicGPT API timeout → retry with exponential backoff<br/>- Slot acquisition timeout → requeue to priority queue<br/>- WebSocket disconnect → job continues, status via polling