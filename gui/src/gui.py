import base64
import gradio as gr
import websockets
import requests
import json
import logging
import os
import csv
from typing import Optional
import urllib3
from PIL import Image
import io

# Disable SSL warnings for development (remove in production)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


BASE_URL = os.getenv("MUSIC_GENERATION_WS_URL", "ws://localhost:8000")


def get_auto_prompt(prompt: str, creativity: float) -> tuple[str, str, str]:
    data = {"prompt": prompt, "creativity": creativity}
    try:
        response = requests.post(
            f"{BASE_URL.replace('ws://', 'http://').replace('wss://', 'https://')}/auto-prompt",
            json=data,
        )
        if response.ok:
            data = response.json()
            return data["main_prompt"], data["music_genre"], data["lyrics"]
        else:
            return "ERROR: Failed to generate prompt", "ERROR", "ERROR"
    except Exception as e:
        logger.error(f"Error in auto-prompt: {e}")
        return f"ERROR: {str(e)}", "ERROR", "ERROR"


def get_album_cover(prompt: str, creativity: float, model: str) -> bytes:
    data = {"prompt": prompt, "creativity": creativity, "model": model}
    try:
        response = requests.post(
            f"{BASE_URL.replace('ws://', 'http://').replace('wss://', 'https://')}/album-cover-art",
            json=data,
        )
        if response.ok:
            data = response.json()
            return base64.b64decode(data["image_base64"])
        else:
            return b"ERROR: Failed to generate album cover"

    except Exception as e:
        logger.error(f"Error in album cover: {e}")
        return b"ERROR: Failed to generate album cover"


class MusicGenerationInterface:
    def __init__(self, websocket_url: str = f"{BASE_URL}/generate-music"):
        self.websocket_url = websocket_url
        self.current_status = "Ready"

    async def download_file(self, url: str, filename: str) -> Optional[str]:
        """Download a file from URL and save it locally"""
        try:
            # Create temp directory if it doesn't exist
            temp_dir = os.path.join(os.getcwd(), "temp_audio")
            os.makedirs(temp_dir, exist_ok=True)

            filepath = os.path.join(temp_dir, filename)

            # Download file with SSL verification disabled for IP addresses
            response = requests.get(url, verify=False, timeout=30)
            response.raise_for_status()

            with open(filepath, 'wb') as f:
                f.write(response.content)

            logger.info(f"Downloaded file to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Error downloading file from {url}: {e}")
            return None

    async def generate_music(
        self,
        prompt: str,
        music_style: Optional[str] = None,
        lyrics: Optional[str] = None,
        make_instrumental: bool = False,
        vocal_only: bool = False,
        voice_id: Optional[str] = None,
        progress=gr.Progress()
    ):
        """Generate music and return results for Gradio interface"""

        if not prompt.strip():
            return (
                "Error: Prompt is required",
                None, None, None, None, None, None, None, None, None
            )

        # Prepare generation data
        generation_data = {
            "prompt": prompt,
            "music_style": music_style if music_style else None,
            "lyrics": lyrics if lyrics else None,
            "make_instrumental": make_instrumental,
            "vocal_only": vocal_only,
            "voice_id": voice_id if voice_id else None
        }

        try:
            async with websockets.connect(self.websocket_url) as websocket:
                # Send generation request
                await websocket.send(json.dumps(generation_data))
                progress(0.1, desc="Sent generation request...")

                # Listen for responses
                while True:
                    try:
                        response = await websocket.recv()
                        data = json.loads(response)

                        if data.get("error"):
                            return (
                                f"Error: {data.get('data', 'Unknown error')}",
                                None, None, None, None
                            )

                        status = data.get("status", "")
                        response_data = data.get("data", {})

                        if status == "initiating music generation":
                            progress(0.2, desc="Initiating music generation...")

                        elif status == "generating music":
                            progress(0.5, desc=f"Generating music... Status: {response_data.get('status', 'Processing')}")

                        elif status == "completed":
                            progress(0.9, desc="Downloading generated files...")

                            # Extract results
                            song_1_url = response_data.get("song_1_url")
                            song_1_name = response_data.get("song_1_name", "Song 1")
                            song_1_lyrics = response_data.get("song_1_lyrics", "")
                            song_1_length = response_data.get("song_1_length", "")

                            song_2_url = response_data.get("song_2_url")
                            song_2_name = response_data.get("song_2_name", "Song 2")
                            song_2_lyrics = response_data.get("song_2_lyrics", "")
                            song_2_length = response_data.get("song_2_length", "")

                            album_cover_url = response_data.get("album_cover_url")

                            # Download files locally to avoid SSL issues
                            song_1_path = None
                            song_2_path = None
                            album_cover_path = None

                            if song_1_url:
                                song_1_path = await self.download_file(
                                    song_1_url,
                                    f"song1_{song_1_name.replace(' ', '_')}.mp3"
                                )

                            if song_2_url:
                                song_2_path = await self.download_file(
                                    song_2_url,
                                    f"song2_{song_2_name.replace(' ', '_')}.mp3"
                                )

                            if album_cover_url:
                                album_cover_path = await self.download_file(
                                    album_cover_url,
                                    f"album_cover_{prompt[:30].replace(' ', '_')}.jpg"
                                )

                            progress(1.0, desc="Music generation completed!")

                            return (
                                "✅ Music generation completed successfully!",
                                song_1_path, song_1_name, song_1_lyrics, song_1_length,
                                song_2_path, song_2_name, song_2_lyrics, song_2_length,
                                album_cover_path
                            )

                    except websockets.exceptions.ConnectionClosed:
                        return (
                            "Error: WebSocket connection closed unexpectedly",
                            None, None, None, None, None, None, None, None, None
                        )

        except Exception as e:
            logger.error(f"Error connecting to WebSocket: {e}")
            return (
                f"Error: Failed to connect to music generation service - {str(e)}",
                None, None, None, None, None, None, None, None, None
            )


class MusicRemixInterface:
    def __init__(self, websocket_url: str = f"{BASE_URL}/remix-music"):
        self.websocket_url = websocket_url
        self.current_status = "Ready"

    async def download_file(self, url: str, filename: str) -> Optional[str]:
        """Download a file from URL and save it locally"""
        try:
            # Create temp directory if it doesn't exist
            temp_dir = os.path.join(os.getcwd(), "temp_audio")
            os.makedirs(temp_dir, exist_ok=True)

            filepath = os.path.join(temp_dir, filename)

            # Download file with SSL verification disabled for IP addresses
            response = requests.get(url, verify=False, timeout=30)
            response.raise_for_status()

            with open(filepath, 'wb') as f:
                f.write(response.content)

            logger.info(f"Downloaded file to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Error downloading file from {url}: {e}")
            return None

    async def remix_music(
        self,
        audio_file,
        prompt: str,
        lyrics: Optional[str] = None,
        progress=gr.Progress()
    ):
        """Remix music and return results for Gradio interface"""

        if not audio_file:
            return (
                "Error: Audio file is required",
                None, None, None, None
            )

        if not prompt.strip():
            return (
                "Error: Prompt is required for remix",
                None, None, None, None
            )

        try:
            # Convert audio file to base64
            progress(0.1, desc="Processing audio file...")
            with open(audio_file, "rb") as f:
                audio_data = f.read()
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            # Prepare remix data
            remix_data = {
                "audio_base64": audio_base64,
                "prompt": prompt,
                "lyrics": lyrics if lyrics else None,
            }

            async with websockets.connect(self.websocket_url) as websocket:
                # Send remix request
                await websocket.send(json.dumps(remix_data))
                progress(0.2, desc="Sent remix request...")

                # Listen for responses
                while True:
                    try:
                        response = await websocket.recv()
                        data = json.loads(response)

                        if data.get("error"):
                            return (
                                f"Error: {data.get('data', 'Unknown error')}",
                                None, None, None, None
                            )

                        status = data.get("status", "")
                        response_data = data.get("data", {})

                        if status == "initiating music remixing":
                            progress(0.3, desc="Initiating music remixing...")

                        elif status == "generating music":
                            progress(0.5, desc=f"Remixing music... Status: {response_data.get('status', 'Processing')}")

                        elif status == "completed":
                            progress(0.9, desc="Downloading remixed files...")

                            # Extract results
                            song_1_url = response_data.get("song_1_url")
                            song_1_length = response_data.get("song_1_length", "")

                            song_2_url = response_data.get("song_2_url")
                            song_2_length = response_data.get("song_2_length", "")

                            # Download files locally to avoid SSL issues
                            song_1_path = None
                            song_2_path = None

                            if song_1_url:
                                song_1_path = await self.download_file(
                                    song_1_url,
                                    "remixed_song1.mp3"
                                )

                            if song_2_url:
                                song_2_path = await self.download_file(
                                    song_2_url,
                                    "remixed_song2.mp3"
                                )

                            progress(1.0, desc="Music remix completed!")

                            return (
                                "✅ Music remix completed successfully!",
                                song_1_path, song_1_length,
                                song_2_path, song_2_length
                            )

                    except websockets.exceptions.ConnectionClosed:
                        return (
                            "Error: WebSocket connection closed unexpectedly",
                            None, None, None, None
                        )

        except Exception as e:
            logger.error(f"Error connecting to WebSocket: {e}")
            return (
                f"Error: Failed to connect to music remix service - {str(e)}",
                None, None, None, None
            )


class VocalExtractionInterface:
    def __init__(self, websocket_url: str = f"{BASE_URL}/extract-vocals"):
        self.websocket_url = websocket_url
        self.current_status = "Ready"

    async def download_file(self, url: str, filename: str) -> Optional[str]:
        """Download a file from URL and save it locally"""
        try:
            # Create temp directory if it doesn't exist
            temp_dir = os.path.join(os.getcwd(), "temp_audio")
            os.makedirs(temp_dir, exist_ok=True)

            filepath = os.path.join(temp_dir, filename)

            # Download file with SSL verification disabled for IP addresses
            response = requests.get(url, verify=False, timeout=30)
            response.raise_for_status()

            with open(filepath, 'wb') as f:
                f.write(response.content)

            logger.info(f"Downloaded file to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Error downloading file from {url}: {e}")
            return None

    async def extract_vocals(
        self,
        audio_file,
        preprocessing_options: list,
        progress=gr.Progress()
    ):
        """Extract vocals and return results for Gradio interface"""

        if not audio_file:
            return (
                "Error: Audio file is required",
                None, None, None, None
            )

        try:
            # Convert audio file to base64
            progress(0.1, desc="Processing audio file...")
            with open(audio_file, "rb") as f:
                audio_data = f.read()
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            # Prepare extraction data
            extraction_data = {
                "audio_base64": audio_base64,
                "preprocessing_options": preprocessing_options if preprocessing_options else []
            }

            async with websockets.connect(self.websocket_url) as websocket:
                # Send extraction request
                await websocket.send(json.dumps(extraction_data))
                progress(0.2, desc="Sent vocal extraction request...")

                # Listen for responses
                while True:
                    try:
                        response = await websocket.recv()
                        data = json.loads(response)

                        if data.get("error"):
                            return (
                                f"Error: {data.get('data', 'Unknown error')}",
                                None, None, None, None
                            )

                        status = data.get("status", "")
                        response_data = data.get("data", {})

                        if status == "initiating vocal extraction":
                            progress(0.3, desc="Initiating vocal extraction...")

                        elif status == "generating music":
                            progress(0.5, desc=f"Extracting vocals... Status: {response_data.get('status', 'Processing')}")

                        elif status == "completed":
                            progress(0.9, desc="Downloading extracted files...")

                            # Extract results
                            song_1_url = response_data.get("song_1_url")
                            song_1_length = response_data.get("song_1_length", "")

                            song_2_url = response_data.get("song_2_url")
                            song_2_length = response_data.get("song_2_length", "")

                            # Download files locally to avoid SSL issues
                            song_1_path = None
                            song_2_path = None

                            if song_1_url:
                                song_1_path = await self.download_file(
                                    song_1_url,
                                    "extracted_vocals.mp3"
                                )

                            if song_2_url:
                                song_2_path = await self.download_file(
                                    song_2_url,
                                    "extracted_instrumental.mp3"
                                )

                            progress(1.0, desc="Vocal extraction completed!")

                            return (
                                "✅ Vocal extraction completed successfully!",
                                song_1_path, song_1_length,
                                song_2_path, song_2_length
                            )

                    except websockets.exceptions.ConnectionClosed:
                        return (
                            "Error: WebSocket connection closed unexpectedly",
                            None, None, None, None
                        )

        except Exception as e:
            logger.error(f"Error connecting to WebSocket: {e}")
            return (
                f"Error: Failed to connect to vocal extraction service - {str(e)}",
                None, None, None, None
            )


class MusicExtensionInterface:
    def __init__(self, websocket_url: str = f"{BASE_URL}/extend-music"):
        self.websocket_url = websocket_url
        self.current_status = "Ready"

    async def download_file(self, url: str, filename: str) -> Optional[str]:
        """Download a file from URL and save it locally"""
        try:
            # Create temp directory if it doesn't exist
            temp_dir = os.path.join(os.getcwd(), "temp_audio")
            os.makedirs(temp_dir, exist_ok=True)

            filepath = os.path.join(temp_dir, filename)

            # Download file with SSL verification disabled for IP addresses
            response = requests.get(url, verify=False, timeout=30)
            response.raise_for_status()

            with open(filepath, 'wb') as f:
                f.write(response.content)

            logger.info(f"Downloaded file to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Error downloading file from {url}: {e}")
            return None

    async def extend_music(
        self,
        audio_file,
        extend_after_seconds: int,
        prompt: Optional[str] = None,
        lyrics: Optional[str] = None,
        progress=gr.Progress()
    ):
        """Extend music and return results for Gradio interface"""

        if not audio_file:
            return (
                "Error: Audio file is required",
                None, None, None, None
            )

        if extend_after_seconds <= 0:
            return (
                "Error: Extension duration must be greater than 0",
                None, None, None, None
            )

        try:
            # Convert audio file to base64
            progress(0.1, desc="Processing audio file...")
            with open(audio_file, "rb") as f:
                audio_data = f.read()
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            # Prepare extension data
            extension_data = {
                "audio_base64": audio_base64,
                "extend_after_seconds": extend_after_seconds,
                "prompt": prompt if prompt else None,
                "lyrics": lyrics if lyrics else None,
            }

            async with websockets.connect(self.websocket_url) as websocket:
                # Send extension request
                await websocket.send(json.dumps(extension_data))
                progress(0.2, desc="Sent extension request...")

                # Listen for responses
                while True:
                    try:
                        response = await websocket.recv()
                        data = json.loads(response)

                        if data.get("error"):
                            return (
                                f"Error: {data.get('data', 'Unknown error')}",
                                None, None, None, None
                            )

                        status = data.get("status", "")
                        response_data = data.get("data", {})

                        if status == "initiating music extension":
                            progress(0.3, desc="Initiating music extension...")

                        elif status == "generating music":
                            progress(0.5, desc=f"Extending music... Status: {response_data.get('status', 'Processing')}")

                        elif status == "completed":
                            progress(0.9, desc="Downloading extended files...")

                            # Extract results
                            song_1_url = response_data.get("song_1_url")
                            song_1_length = response_data.get("song_1_length", "")

                            song_2_url = response_data.get("song_2_url")
                            song_2_length = response_data.get("song_2_length", "")

                            # Download files locally to avoid SSL issues
                            song_1_path = None
                            song_2_path = None

                            if song_1_url:
                                song_1_path = await self.download_file(
                                    song_1_url,
                                    "extended_song1.mp3"
                                )

                            if song_2_url:
                                song_2_path = await self.download_file(
                                    song_2_url,
                                    "extended_song2.mp3"
                                )

                            progress(1.0, desc="Music extension completed!")

                            return (
                                "✅ Music extension completed successfully!",
                                song_1_path, song_1_length,
                                song_2_path, song_2_length
                            )

                    except websockets.exceptions.ConnectionClosed:
                        return (
                            "Error: WebSocket connection closed unexpectedly",
                            None, None, None, None
                        )

        except Exception as e:
            logger.error(f"Error connecting to WebSocket: {e}")
            return (
                f"Error: Failed to connect to music extension service - {str(e)}",
                None, None, None, None
            )


class SoundGenerationInterface:
    def __init__(self, websocket_url: str = f"{BASE_URL}/sound-generation"):
        self.websocket_url = websocket_url
        self.current_status = "Ready"

    async def download_file(self, url: str, filename: str) -> Optional[str]:
        """Download a file from URL and save it locally"""
        try:
            # Create temp directory if it doesn't exist
            temp_dir = os.path.join(os.getcwd(), "temp_audio")
            os.makedirs(temp_dir, exist_ok=True)

            filepath = os.path.join(temp_dir, filename)

            # Download file with SSL verification disabled for IP addresses
            response = requests.get(url, verify=False, timeout=30)
            response.raise_for_status()

            with open(filepath, 'wb') as f:
                f.write(response.content)

            logger.info(f"Downloaded file to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Error downloading file from {url}: {e}")
            return None

    async def generate_sound(
        self,
        prompt: str,
        audio_length_seconds: int,
        progress=gr.Progress()
    ):
        """Generate sound and return results for Gradio interface"""

        if not prompt.strip():
            return (
                "Error: Prompt is required",
                None, None
            )

        if audio_length_seconds <= 0:
            return (
                "Error: Audio length must be greater than 0",
                None, None
            )

        # Prepare generation data
        generation_data = {
            "prompt": prompt,
            "audio_length_seconds": audio_length_seconds,
        }

        try:
            async with websockets.connect(self.websocket_url) as websocket:
                # Send generation request
                await websocket.send(json.dumps(generation_data))
                progress(0.1, desc="Sent sound generation request...")

                # Listen for responses
                while True:
                    try:
                        response = await websocket.recv()
                        data = json.loads(response)

                        if data.get("error"):
                            return (
                                f"Error: {data.get('data', 'Unknown error')}",
                                None, None
                            )

                        status = data.get("status", "")
                        response_data = data.get("data", {})

                        if status == "initiating sound generation":
                            progress(0.2, desc="Initiating sound generation...")

                        elif status == "generating music":
                            progress(0.5, desc=f"Generating sound... Status: {response_data.get('status', 'Processing')}")

                        elif status == "completed":
                            progress(0.9, desc="Downloading generated sound...")

                            # Extract results
                            sound_url = response_data.get("sound_url") or response_data.get("sound_url")
                            sound_length = response_data.get("sound_length") or response_data.get("sound_length", "")

                            # Download file locally to avoid SSL issues
                            sound_path = None

                            if sound_url:
                                sound_path = await self.download_file(
                                    sound_url,
                                    f"generated_sound_{prompt[:30].replace(' ', '_')}.mp3"
                                )

                            progress(1.0, desc="Sound generation completed!")

                            return (
                                "✅ Sound generation completed successfully!",
                                sound_path, sound_length
                            )

                    except websockets.exceptions.ConnectionClosed:
                        return (
                            "Error: WebSocket connection closed unexpectedly",
                            None, None
                        )

        except Exception as e:
            logger.error(f"Error connecting to WebSocket: {e}")
            return (
                f"Error: Failed to connect to sound generation service - {str(e)}",
                None, None
            )


class ArtistGenerationInterface:
    def __init__(self, base_url: str = f"{BASE_URL.replace('ws://', 'http://').replace('wss://', 'https://')}"):
        self.base_url = base_url

    def generate_artist_data(self, prompt: str, agent_description: str, creativity: float) -> tuple[str, str, str, str]:
        """Generate artist data and return JSON, CSV data, and download file"""

        if not prompt.strip():
            return (
                "Please enter a prompt for artist generation",
                "",
                "",
                None
            )

        try:
            data = {"prompt": prompt, "creativity": creativity, "agent_description": agent_description}
            response = requests.post(
                f"{self.base_url}/artist-generation",
                json=data,
            )

            if response.ok:
                result_data = response.json()

                # Format JSON for display
                json_display = json.dumps(result_data, indent=2)

                # Convert to CSV format (single row)
                if isinstance(result_data, dict):
                    # Create CSV header and data row
                    headers = list(result_data.keys())
                    values = [str(v) for v in result_data.values()]

                    csv_display = ",".join(headers) + "\n" + ",".join(values)

                    # Create downloadable CSV file
                    csv_file = self._create_csv_file(headers, values)

                    return (
                        "✅ Artist data generated successfully!",
                        json_display,
                        csv_display,
                        csv_file
                    )
                else:
                    return (
                        "❌ Unexpected response format",
                        json_display,
                        "",
                        None
                    )
            else:
                return (
                    f"❌ API Error: {response.status_code} - {response.text}",
                    "",
                    "",
                    None
                )

        except Exception as e:
            logger.error(f"Error in artist generation: {e}")
            return (
                f"❌ Error: {str(e)}",
                "",
                "",
                None
            )

    def _create_csv_file(self, headers, values):
        """Create a temporary CSV file for download"""
        try:
            # Create temp directory if it doesn't exist
            temp_dir = os.path.join(os.getcwd(), "temp_csv")
            os.makedirs(temp_dir, exist_ok=True)

            csv_path = os.path.join(temp_dir, "artist_data.csv")

            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerow(values)

            return csv_path
        except Exception as e:
            logger.error(f"Error creating CSV file: {e}")
            return None


def create_interface():
    """Create and return the Gradio interface with tabs"""

    music_gen = MusicGenerationInterface()
    music_remix = MusicRemixInterface()
    music_extend = MusicExtensionInterface()
    vocal_extract = VocalExtractionInterface()
    sound_gen = SoundGenerationInterface()
    artist_gen = ArtistGenerationInterface()

    with gr.Blocks(title="AI Music Generator", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🎵 AI Music Generator")
        gr.Markdown("Generate custom music using AI with auto-prompting assistance.")

        with gr.Tabs() as tabs:
            # Music Generation Tab
            with gr.Tab("Music Generation", id=1):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.Markdown("### Generation Parameters")

                        prompt = gr.Textbox(
                            label="Prompt",
                            placeholder="Describe the music you want to generate (e.g., 'upbeat pop song about summer')",
                            lines=3,
                            elem_id="main_prompt"
                        )

                        music_style = gr.Dropdown(
                            label="Music Style (Optional)",
                            choices=["Pop", "Rock", "Jazz", "Classical", "Hip-Hop", "Electronic",
                                   "Country", "R&B", "Folk", "Blues", "Lo-Fi", "Metal"],
                            value=None,
                            allow_custom_value=True,
                            elem_id="main_music_style"
                        )

                        lyrics = gr.Textbox(
                            label="Custom Lyrics (Optional)",
                            placeholder="Enter custom lyrics for your song...",
                            lines=5,
                            elem_id="main_lyrics"
                        )

                        with gr.Row():
                            make_instrumental = gr.Checkbox(label="Make Instrumental", value=False)
                            vocal_only = gr.Checkbox(label="Vocal Only", value=False)

                        voice_id = gr.Textbox(
                            label="Voice ID (Optional)",
                            placeholder="Enter a specific voice ID to use for vocals",
                            lines=1,
                            elem_id="main_voice_id"
                        )
                        generate_btn = gr.Button("🎵 Generate Music", variant="primary", size="lg")

                        status_text = gr.Textbox(
                            label="Status",
                            value="Ready to generate music",
                            interactive=False
                        )

                    with gr.Column(scale=2):
                        gr.Markdown("### Generated Music")

                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Song 1")
                                song_1_name = gr.Textbox(label="Title", interactive=False)
                                song_1_audio = gr.Audio(label="Audio", type="filepath", show_download_button=True)
                                song_1_lyrics_display = gr.Textbox(label="Lyrics", lines=4, interactive=False)
                                song_1_length = gr.Textbox(label="Duration", interactive=False)

                            with gr.Column():
                                gr.Markdown("#### Song 2")
                                song_2_name = gr.Textbox(label="Title", interactive=False)
                                song_2_audio = gr.Audio(label="Audio", type="filepath", show_download_button=True)
                                song_2_lyrics_display = gr.Textbox(label="Lyrics", lines=4, interactive=False)
                                song_2_length = gr.Textbox(label="Duration", interactive=False)

                        gr.Markdown("#### Album Cover using MusicGPT Image Generator")
                        album_cover = gr.Image(label="Album Cover", type="filepath")

                # Connect the generate button to the async function
                generate_btn.click(
                    fn=music_gen.generate_music,
                    inputs=[prompt, music_style, lyrics, make_instrumental, vocal_only, voice_id],
                    outputs=[
                        status_text,
                        song_1_audio, song_1_name, song_1_lyrics_display, song_1_length,
                        song_2_audio, song_2_name, song_2_lyrics_display, song_2_length,
                        album_cover
                    ],
                    show_progress=True
                )

                with gr.Row():
                    gr.Examples(
                        examples=[
                            ["Create an upbeat pop song about summer vacation", "Pop", "", False, False, None],
                            ["Generate a melancholic piano ballad", "Classical", "", True, False, None],
                            ["Make a hip-hop track with heavy bass", "Hip-Hop", "", False, False, None],
                        ],
                        inputs=[prompt, music_style, lyrics, make_instrumental, vocal_only],
                        label="Quick Start Examples"
                    )

            # Auto-Prompting Tab
            with gr.Tab("Auto-Prompting", id=2):
                gr.Markdown("### 🤖 AI Prompt Assistant")
                gr.Markdown("Let AI help you create detailed prompts for music generation.")

                with gr.Row():
                    with gr.Column():
                        gr.Markdown("#### Input")

                        auto_prompt_input = gr.Textbox(
                            label="Basic Prompt",
                            placeholder="Enter a simple idea (e.g., 'happy song', 'sad ballad', 'party music')",
                            lines=2
                        )

                        creativity_slider = gr.Slider(
                            label="Model Creativity",
                            minimum=0.0,
                            maximum=1.0,
                            value=0.5,
                            step=0.1,
                            info="Higher values = more creative/unexpected results"
                        )

                        with gr.Row():
                            submit_btn = gr.Button("🎨 Generate Enhanced Prompt", variant="primary")
                            use_this_btn = gr.Button("➡️ Use This", variant="secondary", interactive=False)

                    with gr.Column():
                        gr.Markdown("#### Generated Results")

                        generated_prompt = gr.Textbox(
                            label="Enhanced Prompt",
                            lines=3,
                            interactive=False
                        )

                        generated_genre = gr.Textbox(
                            label="Suggested Music Genre",
                            interactive=False
                        )

                        generated_lyrics = gr.Textbox(
                            label="Generated Lyrics",
                            lines=8,
                            interactive=False
                        )

                        auto_status = gr.Textbox(
                            label="Status",
                            value="Ready to generate enhanced prompt",
                            interactive=False
                        )

                # Examples for auto-prompting
                gr.Examples(
                    examples=[
                        ["happy birthday song", 0.5],
                        ["motivational workout music", 0.7],
                        ["relaxing meditation", 0.3],
                        ["epic boss battle", 0.8],
                        ["romantic dinner", 0.4],
                    ],
                    inputs=[auto_prompt_input, creativity_slider]
                )

                def handle_auto_prompt(prompt_text: str, creativity: float):
                    """Handle auto-prompt generation"""
                    if not prompt_text.strip():
                        return (
                            "Please enter a prompt",
                            "",
                            "",
                            "",
                            gr.update(interactive=False)
                        )

                    try:
                        main_prompt, genre, lyrics_text = get_auto_prompt(prompt_text, creativity)

                        if "ERROR" in main_prompt:
                            return (
                                f"Error: {main_prompt}",
                                "",
                                "",
                                "",
                                gr.update(interactive=False)
                            )

                        return (
                            "✅ Enhanced prompt generated successfully!",
                            main_prompt,
                            genre,
                            lyrics_text,
                            gr.update(interactive=True)  # Enable "Use This" button
                        )
                    except Exception as e:
                        return (
                            f"Error generating prompt: {str(e)}",
                            "",
                            "",
                            "",
                            gr.update(interactive=False)
                        )

                def transfer_to_main_tab(enhanced_prompt, genre, lyrics_text):
                    """Transfer generated content to main tab"""
                    return (
                        enhanced_prompt,
                        genre,
                        lyrics_text,
                        gr.update(selected=1)  # Switch to Music Generation tab
                    )

                # Connect auto-prompt submit button
                submit_btn.click(
                    fn=handle_auto_prompt,
                    inputs=[auto_prompt_input, creativity_slider],
                    outputs=[auto_status, generated_prompt, generated_genre, generated_lyrics, use_this_btn]
                )

                # Connect "Use This" button to transfer data to main tab
                use_this_btn.click(
                    fn=transfer_to_main_tab,
                    inputs=[generated_prompt, generated_genre, generated_lyrics],
                    outputs=[prompt, music_style, lyrics, tabs]
                )

            # Remix/Cover Music Tab
            with gr.Tab("Remix/Cover Music", id=3):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.Markdown("### Remix Parameters")

                        remix_audio_file = gr.File(
                            label="Audio File to Remix",
                            file_types=["audio"],
                            file_count="single",
                            type="filepath"
                        )

                        remix_prompt = gr.Textbox(
                            label="Remix Prompt (Required)",
                            placeholder="Describe how you want to remix the music (e.g., 'make it more upbeat and electronic', 'transform into jazz style')",
                            lines=3
                        )

                        remix_lyrics = gr.Textbox(
                            label="New Lyrics (Optional)",
                            placeholder="Enter new lyrics for the remix...",
                            lines=5
                        )

                        remix_btn = gr.Button("🎵 Remix Music", variant="primary", size="lg")

                        remix_status_text = gr.Textbox(
                            label="Status",
                            value="Ready to remix music",
                            interactive=False
                        )

                    with gr.Column(scale=2):
                        gr.Markdown("### Remixed Music Results")

                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Remix Version 1")
                                remix_song_1_audio = gr.Audio(label="Audio", type="filepath", show_download_button=True)
                                remix_song_1_length = gr.Textbox(label="Duration", interactive=False)

                            with gr.Column():
                                gr.Markdown("#### Remix Version 2")
                                remix_song_2_audio = gr.Audio(label="Audio", type="filepath", show_download_button=True)
                                remix_song_2_length = gr.Textbox(label="Duration", interactive=False)

                # Connect the remix button to the async function
                remix_btn.click(
                    fn=music_remix.remix_music,
                    inputs=[remix_audio_file, remix_prompt, remix_lyrics],
                    outputs=[
                        remix_status_text,
                        remix_song_1_audio, remix_song_1_length,
                        remix_song_2_audio, remix_song_2_length
                    ],
                    show_progress=True
                )

                with gr.Row():
                    gr.Examples(
                        examples=[
                            [None, "Transform into a jazz version", ""],
                            [None, "Make it more electronic and danceable", ""],
                            [None, "Create a rock cover with heavy guitar", ""],
                            [None, "Turn into an acoustic ballad", "Now I see the light, shining so bright"],
                        ],
                        inputs=[remix_audio_file, remix_prompt, remix_lyrics],
                        label="Remix Examples"
                    )

            # Extend Music Tab
            with gr.Tab("Extend Music", id=4):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.Markdown("### Extension Parameters")

                        extend_audio_file = gr.File(
                            label="Audio File to Extend",
                            file_types=["audio"],
                            file_count="single",
                            type="filepath"
                        )

                        extend_after_seconds = gr.Number(
                            label="Extend After (seconds)",
                            value=30,
                            minimum=1,
                            maximum=300,
                            step=1,
                            info="Time in seconds after which to start the extension"
                        )

                        extend_prompt = gr.Textbox(
                            label="Prompt (Optional)",
                            placeholder="Describe how you want the music to continue (e.g., 'add a guitar solo', 'make it more energetic')",
                            lines=3
                        )

                        extend_lyrics = gr.Textbox(
                            label="Additional Lyrics (Optional)",
                            placeholder="Enter lyrics for the extended part...",
                            lines=5
                        )

                        extend_btn = gr.Button("🎵 Extend Music", variant="primary", size="lg")

                        extend_status_text = gr.Textbox(
                            label="Status",
                            value="Ready to extend music",
                            interactive=False
                        )

                    with gr.Column(scale=2):
                        gr.Markdown("### Extended Music Results")

                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Extended Version 1")
                                extend_song_1_audio = gr.Audio(label="Audio", type="filepath", show_download_button=True)
                                extend_song_1_length = gr.Textbox(label="Duration", interactive=False)

                            with gr.Column():
                                gr.Markdown("#### Extended Version 2")
                                extend_song_2_audio = gr.Audio(label="Audio", type="filepath", show_download_button=True)
                                extend_song_2_length = gr.Textbox(label="Duration", interactive=False)

                # Connect the extend button to the async function
                extend_btn.click(
                    fn=music_extend.extend_music,
                    inputs=[extend_audio_file, extend_after_seconds, extend_prompt, extend_lyrics],
                    outputs=[
                        extend_status_text,
                        extend_song_1_audio, extend_song_1_length,
                        extend_song_2_audio, extend_song_2_length
                    ],
                    show_progress=True
                )

                with gr.Row():
                    gr.Examples(
                        examples=[
                            [None, 30, "Add a guitar solo", ""],
                            [None, 60, "Make it more energetic with drums", ""],
                            [None, 45, "", "And the beat goes on, through the night"],
                        ],
                        inputs=[extend_audio_file, extend_after_seconds, extend_prompt, extend_lyrics],
                        label="Extension Examples"
                    )

            # Extract Vocals Tab
            with gr.Tab("Extract Vocals", id=5):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.Markdown("### Vocal Extraction Parameters")

                        extract_audio_file = gr.File(
                            label="Audio File for Vocal Extraction",
                            file_types=["audio"],
                            file_count="single",
                            type="filepath"
                        )

                        extract_preprocessing = gr.CheckboxGroup(
                            label="Preprocessing Options",
                            choices=["Denoise", "Deecho", "Dereverb"],
                            value=[],
                            info="Select any combination of preprocessing options to improve extraction quality"
                        )

                        extract_vocals_btn = gr.Button("🎤 Extract Vocals", variant="primary", size="lg")

                        extract_status_text = gr.Textbox(
                            label="Status",
                            value="Ready to extract vocals",
                            interactive=False
                        )

                    with gr.Column(scale=2):
                        gr.Markdown("### Vocal Extraction Results")

                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Extracted Vocals")
                                extract_vocals_audio = gr.Audio(label="Vocals Only", type="filepath", show_download_button=True)
                                extract_vocals_length = gr.Textbox(label="Duration", interactive=False)

                            with gr.Column():
                                gr.Markdown("#### Extracted Instrumental")
                                extract_instrumental_audio = gr.Audio(label="Instrumental Only", type="filepath", show_download_button=True)
                                extract_instrumental_length = gr.Textbox(label="Duration", interactive=False)

                # Connect the extract button to the async function
                extract_vocals_btn.click(
                    fn=vocal_extract.extract_vocals,
                    inputs=[extract_audio_file, extract_preprocessing],
                    outputs=[
                        extract_status_text,
                        extract_vocals_audio, extract_vocals_length,
                        extract_instrumental_audio, extract_instrumental_length
                    ],
                    show_progress=True
                )

                with gr.Row():
                    gr.Examples(
                        examples=[
                            [None, []],
                            [None, ["Denoise"]],
                            [None, ["Denoise", "Deecho"]],
                            [None, ["Denoise", "Deecho", "Dereverb"]],
                        ],
                        inputs=[extract_audio_file, extract_preprocessing],
                        label="Preprocessing Examples"
                    )

            # Sound Generation Tab
            with gr.Tab("Sound Generation", id=6):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.Markdown("### Sound Generation Parameters")

                        sound_prompt = gr.Textbox(
                            label="Prompt",
                            placeholder="Describe the sound you want to generate (e.g., 'rain on leaves', 'ocean waves', 'birds chirping')",
                            lines=3
                        )

                        sound_audio_length = gr.Number(
                            label="Audio Length (seconds)",
                            value=10,
                            minimum=1,
                            maximum=300,
                            step=1,
                            info="Duration of the generated sound in seconds"
                        )

                        generate_sound_btn = gr.Button("🔊 Generate Sound", variant="primary", size="lg")

                        sound_status_text = gr.Textbox(
                            label="Status",
                            value="Ready to generate sound",
                            interactive=False
                        )

                    with gr.Column(scale=2):
                        gr.Markdown("### Generated Sound")

                        with gr.Column():
                            gr.Markdown("#### Generated Audio")
                            generated_sound_audio = gr.Audio(label="Sound", type="filepath", show_download_button=True)
                            generated_sound_length = gr.Textbox(label="Duration", interactive=False)

                # Connect the generate button to the async function
                generate_sound_btn.click(
                    fn=sound_gen.generate_sound,
                    inputs=[sound_prompt, sound_audio_length],
                    outputs=[
                        sound_status_text,
                        generated_sound_audio, generated_sound_length
                    ],
                    show_progress=True
                )

                with gr.Row():
                    gr.Examples(
                        examples=[
                            ["Rain falling on a metal roof", 15],
                            ["Ocean waves crashing on the shore", 30],
                            ["Birds singing in a forest", 20],
                            ["City traffic and street sounds", 25],
                            ["Crackling fireplace", 45],
                        ],
                        inputs=[sound_prompt, sound_audio_length],
                        label="Sound Generation Examples"
                    )

            # Advanced Music Art Tab
            with gr.Tab("Advanced Music Art", id=7):
                gr.Markdown("### 🎨 AI Album Cover Generator")
                gr.Markdown("Create stunning album cover artwork using AI.")

                with gr.Row():
                    with gr.Column():
                        gr.Markdown("#### Input Parameters")

                        art_prompt = gr.Textbox(
                            label="Art Prompt",
                            placeholder="Describe the album cover you want (e.g., 'neon cityscape at night', 'abstract colorful waves', 'vintage rock poster')",
                            lines=3
                        )

                        art_creativity = gr.Slider(
                            label="Creativity Level",
                            minimum=0.0,
                            maximum=1.0,
                            value=0.7,
                            step=0.1,
                            info="Higher values = more artistic/abstract results"
                        )

                        model_select = gr.Dropdown(
                            label="Model Backend",
                            choices=["imagen", "gemini-2.5", "gemini-2.0"],
                            value="imagen",
                            info="Choose the AI model for generating the album cover"
                        )

                        generate_art_btn = gr.Button("🎨 Generate Album Cover", variant="primary", size="lg")

                        art_status = gr.Textbox(
                            label="Status",
                            value="Ready to generate album cover",
                            interactive=False
                        )

                    with gr.Column():
                        gr.Markdown("#### Generated Artwork")

                        generated_artwork = gr.Image(
                            label="Album Cover Art",
                            type="pil",
                            show_download_button=True,
                            interactive=False
                        )

                        gr.Markdown("**Tips:**")
                        gr.Markdown("• Use descriptive keywords for better results")
                        gr.Markdown("• Mention art styles (e.g., 'minimalist', 'surreal', 'retro')")
                        gr.Markdown("• Include colors and moods in your prompt")
                        gr.Markdown("• Higher creativity values produce more unique designs")

                # Examples for album art
                gr.Examples(
                    examples=[
                        ["Psychedelic spiral galaxy with vibrant colors", 0.8, "imagen"],
                        ["Minimalist geometric shapes in black and white", 0.3, "imagen"],
                        ["Vintage 70s rock poster with bold typography", 0.6, "imagen"],
                        ["Dreamy pastel clouds at sunset", 0.5, "imagen"],
                        ["Dark gothic cathedral in moonlight", 0.7, "gemini-2.5"],
                        ["Abstract jazz instruments melting together", 0.9, "gemini-2.0"],
                        ["Cyberpunk neon city street", 0.7, "imagen"],
                        ["Watercolor flowers and butterflies", 0.4, "imagen"],
                    ],
                    inputs=[art_prompt, art_creativity, model_select],
                    label="Example Prompts"
                )

                def generate_album_art(prompt_text: str, creativity: float, model: str, progress=gr.Progress()):
                    """Generate album cover art"""
                    if not prompt_text.strip():
                        return (
                            "Please enter a prompt for the album cover",
                            None
                        )

                    try:
                        progress(0.3, desc="Sending request to AI...")

                        # Call the get_album_cover function
                        image_bytes = get_album_cover(prompt_text, creativity, model)

                        if image_bytes == b"ERROR: Failed to generate album cover":
                            return (
                                "❌ Failed to generate album cover. Please try again.",
                                None
                            )

                        progress(0.7, desc="Processing image...")

                        # Convert bytes to PIL Image for Gradio
                        image = Image.open(io.BytesIO(image_bytes))

                        progress(1.0, desc="Complete!")

                        return (
                            f"✅ Album cover generated successfully! Prompt: '{prompt_text[:50]}...'",
                            image
                        )

                    except Exception as e:
                        logger.error(f"Error generating album art: {e}")
                        return (
                            f"❌ Error generating album cover: {str(e)}",
                            None
                        )

                # Connect the generate button
                generate_art_btn.click(
                    fn=generate_album_art,
                    inputs=[art_prompt, art_creativity, model_select],
                    outputs=[art_status, generated_artwork],
                    show_progress=True
                )

            # Artist Generation Tab
            with gr.Tab("Artist Generation", id=8):
                gr.Markdown("### 🎤 AI Artist Data Generator")
                gr.Markdown("Generate comprehensive artist profiles and metadata using AI.")

                with gr.Row():
                    with gr.Column():
                        gr.Markdown("#### Input Parameters")

                        artist_prompt = gr.Textbox(
                            label="Artist Prompt",
                            placeholder="Describe the type of artist you want to generate (e.g., 'indie rock band from Seattle', 'R&B solo artist', 'electronic music producer')",
                            lines=3
                        )
                        agent_description = gr.Textbox(
                            label="Agent Description (Optional)",
                            placeholder="Describe the agent's role and behavior (e.g., 'You are a music industry expert who generates artist profiles')",
                            lines=3
                        )

                        artist_creativity = gr.Slider(
                            label="Creativity Level",
                            minimum=0.0,
                            maximum=1.0,
                            value=0.7,
                            step=0.1,
                            info="Higher values = more creative/unique artist profiles"
                        )

                        generate_artist_btn = gr.Button("🎤 Generate Artist Data", variant="primary", size="lg")

                        artist_status = gr.Textbox(
                            label="Status",
                            value="Ready to generate artist data",
                            interactive=False
                        )

                    with gr.Column():
                        gr.Markdown("#### Generated Artist Data")

                        with gr.Tabs():
                            with gr.Tab("JSON View"):
                                artist_json_display = gr.Code(
                                    label="Artist Data (JSON)",
                                    language="json",
                                    interactive=False
                                )

                            with gr.Tab("CSV View"):
                                artist_csv_display = gr.Textbox(
                                    label="Artist Data (CSV)",
                                    lines=3,
                                    max_lines=10,
                                    interactive=False
                                )

                        artist_csv_download = gr.File(
                            label="Download CSV File",
                            interactive=False
                        )

                        gr.Markdown("**Tips:**")
                        gr.Markdown("• Be specific about genre, origin, or style preferences")
                        gr.Markdown("• Higher creativity generates more unique artist profiles")
                        gr.Markdown("• JSON view shows structured data, CSV view shows tabular format")
                        gr.Markdown("• Use the download button to save the data as CSV")

                # Examples for artist generation
                gr.Examples(
                    examples=[
                        ["Indie folk singer-songwriter from Portland", "You are a music industry expert who generates artist profiles", 0.6],
                        ["Electronic music producer specializing in ambient techno", "You are a music industry expert who generates artist profiles", 0.8],
                        ["Alternative rock band influenced by 90s grunge", "You are a music industry expert who generates artist profiles", 0.5],
                        ["Jazz fusion guitarist with classical training", "You are a music industry expert who generates artist profiles", 0.7],
                        ["Hip-hop artist from Atlanta with trap influences", "You are a music industry expert who generates artist profiles", 0.6],
                        ["Pop singer with Broadway background", "You are a music industry expert who generates artist profiles", 0.4],
                    ],
                    inputs=[artist_prompt, agent_description, artist_creativity],
                    label="Artist Generation Examples"
                )

                # Connect the generate button
                generate_artist_btn.click(
                    fn=artist_gen.generate_artist_data,
                    inputs=[artist_prompt, agent_description, artist_creativity],
                    outputs=[artist_status, artist_json_display, artist_csv_display, artist_csv_download]
                )

    return demo


if __name__ == "__main__":
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False
    )
