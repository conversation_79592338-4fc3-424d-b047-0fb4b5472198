name: Deploy Music Generation

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

on:
  push:
    branches: [main]
    paths-ignore:
      - "**/*.md"
      - ".*ignore"

  pull_request:
    branches: [main]
    paths-ignore:
      - "**/*.md"
      - ".*ignore"

  workflow_dispatch:

env:
  APP_NAME: music-generation
  ENVIRONMENT: prd
  IMAGE_TAG: ${{ github.sha }}
  GCP_REGISTRY: us-central1-docker.pkg.dev
  REGION: us-central1
  GITHUB_ACTIONS_RUN_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
  TERRAGRUNT_VERSION: "v0.75.0"
  TERRAFORM_VERSION: "v1.11.0"

jobs:
  test-build-image-and-dry-run-deploy:
    name: Test Docker Build
    if: github.event_name == 'pull_request'
    permissions:
      contents: "read" # Required to checkout the repo
      id-token: "write" # Required for the authorization step
    environment:
      name: prd

    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: .

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: "Authenticate to Google Cloud"
        uses: "google-github-actions/auth@v2"
        with:
          workload_identity_provider: ${{ vars.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ vars.GCP_SERVICE_ACCOUNT }}
          access_token_lifetime: 900s

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"
        with:
          version: ">= 363.0.0"

      - name: Configure docker auth
        run: gcloud auth configure-docker ${{ env.GCP_REGISTRY }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Export Docker Repo Name
        run: echo "DOCKER_REPO=${{ env.GCP_REGISTRY }}/${{ vars.GCP_PROJECT_ID }}/${{ env.APP_NAME }}-${{ env.ENVIRONMENT }}/${{ env.APP_NAME }}" >> $GITHUB_ENV

      - name: Build Music Generation
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./api/Dockerfile
          tags: |
            ${{ env.DOCKER_REPO }}:test
          push: false
          cache-from: type=registry,ref=${{ env.DOCKER_REPO }}:latest

      - name: Export Docker Repo GUI Name
        run: echo "DOCKER_REPO_GUI=${{ env.GCP_REGISTRY }}/${{ vars.GCP_PROJECT_ID }}/${{ env.APP_NAME }}-gui-${{ env.ENVIRONMENT }}/${{ env.APP_NAME }}-gui" >> $GITHUB_ENV

      - name: Build Music Generation GUI
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./gui/Dockerfile
          tags: |
            ${{ env.DOCKER_REPO_GUI }}:test
          push: false
          cache-from: type=registry,ref=${{ env.DOCKER_REPO_GUI }}:latest

      - name: Set Terragrunt working directory
        run: |
          echo "TG_WORKING_DIR=iac/terraform/environments/${{ env.ENVIRONMENT }}" >> $GITHUB_ENV

      - name: Dry Run
        uses: gruntwork-io/terragrunt-action@v2
        env:
          MUSIC_GENERATION_IMAGE: ${{ env.DOCKER_REPO }}:latest
          MUSIC_GENERATION_GUI_IMAGE: ${{ env.DOCKER_REPO_GUI }}:latest
          MUSIC_GPT_API_KEY: ${{ secrets.MUSIC_GPT_API_KEY }}
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          ELEVENLABS_API_KEY: ${{ secrets.ELEVENLABS_API_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        with:
          tf_version: ${{ env.TERRAFORM_VERSION }}
          tg_version: ${{ env.TERRAGRUNT_VERSION }}
          # tg_dir: ${{ env.TG_WORKING_DIR }}  # this is not working for some reason
          tg_command: "run-all plan"

  build-push-deploy:
    name: Build, Push, Deploy
    if: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
    permissions:
      contents: "read" # Required to checkout the repo
      id-token: "write" # Required for the authorization step
    environment:
      name: prd

    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: .

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: "Authenticate to Google Cloud"
        uses: "google-github-actions/auth@v2"
        with:
          workload_identity_provider: ${{ vars.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ vars.GCP_SERVICE_ACCOUNT }}
          access_token_lifetime: 900s

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"
        with:
          version: ">= 363.0.0"

      - name: Configure docker auth
        run: gcloud auth configure-docker ${{ env.GCP_REGISTRY }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Export Docker Repo Name
        run: echo "DOCKER_REPO=${{ env.GCP_REGISTRY }}/${{ vars.GCP_PROJECT_ID }}/${{ env.APP_NAME }}-${{ env.ENVIRONMENT }}/${{ env.APP_NAME }}" >> $GITHUB_ENV

      - name: Build and Push Music Generation
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./api/Dockerfile
          tags: |
            ${{ env.DOCKER_REPO }}:latest
            ${{ env.DOCKER_REPO }}:${{ env.IMAGE_TAG }}
          push: true
          cache-from: type=registry,ref=${{ env.DOCKER_REPO }}:latest

      - name: Export Docker Repo GUI Name
        run: echo "DOCKER_REPO_GUI=${{ env.GCP_REGISTRY }}/${{ vars.GCP_PROJECT_ID }}/${{ env.APP_NAME }}-gui-${{ env.ENVIRONMENT }}/${{ env.APP_NAME }}-gui" >> $GITHUB_ENV

      - name: Build Music Generation GUI
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./gui/Dockerfile
          tags: |
            ${{ env.DOCKER_REPO_GUI }}:latest
            ${{ env.DOCKER_REPO_GUI }}:${{ env.IMAGE_TAG }}
          push: true
          cache-from: type=registry,ref=${{ env.DOCKER_REPO_GUI }}:latest

      - name: Set Terragrunt working directory
        run: |
          echo "TG_WORKING_DIR=iac/terraform/environments/${{ env.ENVIRONMENT }}" >> $GITHUB_ENV

      - name: Apply Deployment
        uses: gruntwork-io/terragrunt-action@v2
        env:
          MUSIC_GENERATION_IMAGE: ${{ env.DOCKER_REPO }}:${{ env.IMAGE_TAG }}
          MUSIC_GENERATION_GUI_IMAGE: ${{ env.DOCKER_REPO_GUI }}:${{ env.IMAGE_TAG }}
          MUSIC_GPT_API_KEY: ${{ secrets.MUSIC_GPT_API_KEY }}
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          ELEVENLABS_API_KEY: ${{ secrets.ELEVENLABS_API_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        with:
          tf_version: ${{ env.TERRAFORM_VERSION }}
          tg_version: ${{ env.TERRAGRUNT_VERSION }}
          # tg_dir: ${{ env.TG_WORKING_DIR }}  # this is not working for some reason
          tg_command: "run-all apply"
