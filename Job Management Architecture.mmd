graph TB
    %% Client Layer
    subgraph "Client Layer"
        C[Client Applications]
        WS[WebSocket Connections]
    end

    %% API Gateway Layer
    subgraph "API Gateway Layer"
        JM_API[Job Management API<br/>FastAPI Service<br/>Port 8001]
        MG_API[Music Generation API<br/>FastAPI Service<br/>Port 8000]
    end

    %% Job Management Core
    subgraph "Job Management Core"
        JM[JobManager<br/>Job Lifecycle Management]
        TE[TaskExecutor<br/>Workflow Execution]
        WCM[WorkflowContextManager<br/>Cross-task State]
        WT[WorkflowTracker<br/>Task Progress Tracking]
    end

    %% Queue Management Layer
    subgraph "Queue Management Layer"
        QM[CloudTasksQueueManager<br/>Google Cloud Tasks]
        MGQ[MusicGPTQueueManager<br/>Rate Limit & Slots]
        ELQ[ElevenLabsQueueManager<br/>Rate Limit & Slots]
        
        subgraph "Queue Types"
            Q1[job_queue<br/>20/100<br/>Orchestration]
            Q2[task_queue<br/>100/200<br/>Fast Tasks]
            Q3[musicgpt_queue<br/>5/10<br/>Music Tasks]
            Q4[elevenlabs_queue<br/>10/20<br/>Voice Tasks]
            Q5[priority_queue<br/>50/50<br/>High Priority]
        end
    end

    %% Storage Layer
    subgraph "Storage Layer"
        SM[StorageManager<br/>GCS Operations]
        GCS[Google Cloud Storage<br/>Single Bucket]
        
        subgraph "Storage Structure"
            ASSETS[assets/<br/>Input Files]
            TEMP[temp/<br/>Processing Files]
            RESULTS[results/<br/>Output Files]
        end
    end

    %% Database Layer
    subgraph "Database Layer"
        PG[(PostgreSQL<br/>Job Tracking)]
        
        subgraph "Database Tables"
            JOBS[jobs<br/>Job Records]
            USAGE[musicgpt_usage_tracker<br/>Rate Limiting]
            ACTIVE[active_music_generation<br/>Slot Management]
            TASKS[workflow_task_execution<br/>Task Tracking]
        end
    end

    %% External Services
    subgraph "External AI Services"
        MGPT[MusicGPT API<br/>Music Generation]
        EL[ElevenLabs API<br/>Voice Synthesis]
        GEMINI[Google Gemini<br/>Content Generation]
        OAI[Azure OpenAI<br/>Text Processing]
    end

    %% Task Executors
    subgraph "Task Executors"
        MGE[MusicGPT Executor<br/>Music Tasks]
        ELE[ElevenLabs Executor<br/>Voice Tasks]
        CE[Content Executor<br/>Text/Image Tasks]
        ME[Media Executor<br/>File Processing]
        TCE[Transcoder Executor<br/>Format Conversion]
    end

    %% Proposed Redis Layer (Optimization)
    subgraph "Proposed Redis Optimization" 
        style "Proposed Redis Optimization" fill:#fff3cd,stroke:#856404
        RQ[Redis Queue<br/>High-Performance]
        RW[Background Workers<br/>Async Processing]
        WSH[WebSocket Handlers<br/>Real-time Updates]
    end

    %% Client Connections
    C --> JM_API
    C --> MG_API
    WS --> MG_API

    %% API to Core Services
    JM_API --> JM
    MG_API --> JM
    JM --> TE
    TE --> WCM
    TE --> WT

    %% Queue Management
    JM --> QM
    QM --> Q1 & Q2 & Q3 & Q4 & Q5
    TE --> MGQ
    TE --> ELQ
    MGQ --> Q3
    ELQ --> Q4

    %% Storage Operations
    JM --> SM
    TE --> SM
    SM --> GCS
    GCS --> ASSETS & TEMP & RESULTS

    %% Database Operations
    JM --> PG
    TE --> PG
    MGQ --> PG
    ELQ --> PG
    PG --> JOBS & USAGE & ACTIVE & TASKS

    %% Task Execution
    TE --> MGE & ELE & CE & ME & TCE

    %% External Service Connections
    MGE --> MGPT
    ELE --> EL
    CE --> GEMINI
    CE --> OAI

    %% Rate Limiting
    MGQ -.->|Rate Limits<br/>10 concurrent<br/>200/min status<br/>60/min conversions| MGPT
    ELQ -.->|Rate Limits<br/>10 concurrent| EL

    %% Proposed Optimization Connections (dotted)
    MG_API -.->|Future Enhancement| RQ
    RQ -.-> RW
    RW -.-> WSH
    WSH -.-> WS

    %% Styling
    classDef clientLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef apiLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef coreLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef queueLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef storageLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef dbLayer fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px
    classDef externalLayer fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef executorLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    class C,WS clientLayer
    class JM_API,MG_API apiLayer
    class JM,TE,WCM,WT coreLayer
    class QM,MGQ,ELQ,Q1,Q2,Q3,Q4,Q5 queueLayer
    class SM,GCS,ASSETS,TEMP,RESULTS storageLayer
    class PG,JOBS,USAGE,ACTIVE,TASKS dbLayer
    class MGPT,EL,GEMINI,OAI externalLayer
    class MGE,ELE,CE,ME,TCE executorLayer